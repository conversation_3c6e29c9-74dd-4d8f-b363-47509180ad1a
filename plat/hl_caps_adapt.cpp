/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* 文件名称:  hl_caps_dummy_adapt.c
* 功能描述:  CAPS 结构化 库适配层
* 版    本:
* 编写日期:  2024/08/26
* 说    明:
* 修改历史:
* 修改日期    修改人  BugID/CRID      修改内容
* ------------------------------------------------------------------------------
* 2024/08/29  zonghui    创建文件
*******************************************************************************/
/* JSON parser in C. */
#include <string.h>
#include <stdio.h>
#include <math.h>
#include <stdlib.h>
#include <float.h>
#include <limits.h>
#include <ctype.h>
#include "hl_caps_adapt.h"

/******************************* 局部宏定义 ***********************************/

/******************************* 局部常数和类型定义 ***************************/

/******************************* 全局变量定义/初始化 **************************/
DtStruct_ApiSet  g_DtApi;

/******************************* 局部函数原型声明 *****************************/

/******************************* 函数实现 *************************************/
/*******************************************************************************
* 函数名称: DT_Adapt_Init
* 函数功能: 结构化库初始化
* 函数参数:
* 参数名称:         类型      输入/输出     描述
* 返回值: 无
* 函数说明:
*
* 修改日期    版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
DtStruct_ApiSet *DT_Adapt_Init(DtVoid)
{
    memset(&g_DtApi, 0x00, sizeof(g_DtApi));
    g_DtApi.m_pApi_InitHooks = cJSON_InitHooks;              
    g_DtApi.m_pApi_Parse = cJSON_Parse;                  
    g_DtApi.m_pApi_Print = cJSON_Print;                  
    g_DtApi.m_pApi_PrintUnformatted = cJSON_PrintUnformatted;       
    g_DtApi.m_pApi_PrintBuffered = cJSON_PrintBuffered;          
    g_DtApi.m_pApi_Delete = cJSON_Delete;                 
    g_DtApi.m_pApi_GetArraySize = cJSON_GetArraySize;           
    g_DtApi.m_pApi_GetArrayItem = cJSON_GetArrayItem;           
    g_DtApi.m_pApi_GetObjectItem = cJSON_GetObjectItem;          
    g_DtApi.m_pApi_GetErrorPtr = cJSON_GetErrorPtr;             
    g_DtApi.m_pApi_CreateNull = cJSON_CreateNull;              
    g_DtApi.m_pApi_CreateTrue = cJSON_CreateTrue;               
    g_DtApi.m_pApi_CreateFalse = cJSON_CreateFalse;              
    g_DtApi.m_pApi_CreateBool = cJSON_CreateBool;               
    g_DtApi.m_pApi_CreateNumber = cJSON_CreateNumber;             
    g_DtApi.m_pApi_CreateString = cJSON_CreateString;             
    g_DtApi.m_pApi_CreateArray = cJSON_CreateArray;              
    g_DtApi.m_pApi_CreateObject = cJSON_CreateObject;             
    g_DtApi.m_pApi_CreateIntArray = cJSON_CreateIntArray;           
    g_DtApi.m_pApi_CreateFloatArray = cJSON_CreateFloatArray;         
    g_DtApi.m_pApi_CreateDoubleArray = cJSON_CreateDoubleArray;        
    g_DtApi.m_pApi_CreateStringArray  = cJSON_CreateStringArray;        
    g_DtApi.m_pApi_AddItemToArray = cJSON_AddItemToArray;           
    g_DtApi.m_pApi_AddItemToObject =  cJSON_AddItemToObject;          
    g_DtApi.m_pApi_AddItemToObjectCS =  cJSON_AddItemToObjectCS;        
    g_DtApi.m_pApi_AddItemReferenceToArray =  cJSON_AddItemReferenceToArray;  
    g_DtApi.m_pApi_AddItemReferenceToObject = cJSON_AddItemReferenceToObject; 
    g_DtApi.m_pApi_DetachItemFromArray = cJSON_DetachItemFromArray;      
    g_DtApi.m_pApi_DeleteItemFromArray = cJSON_DeleteItemFromArray;      
    g_DtApi.m_pApi_DetachItemFromObject = cJSON_DetachItemFromObject;     
    g_DtApi.m_pApi_DeleteItemFromObject = cJSON_DeleteItemFromObject;     
    g_DtApi.m_pApi_InsertItemInArray = cJSON_InsertItemInArray;        
    g_DtApi.m_pApi_ReplaceItemInArray = cJSON_ReplaceItemInArray;       
    g_DtApi.m_pApi_ReplaceItemInObject = cJSON_ReplaceItemInObject;      
    g_DtApi.m_pApi_Duplicate = cJSON_Duplicate;                
    g_DtApi.m_pApi_ParseWithOpts = cJSON_ParseWithOpts;            
    g_DtApi.m_pApi_Minify = cJSON_Minify;  
    g_DtApi.m_pApi_AddNullToObject=cJSON_AddItemToObject;
    g_DtApi.m_pApi_AddTrueToObject=cJSON_AddItemToObject;
    g_DtApi.m_pApi_AddFalseToObject=cJSON_AddItemToObject;
    g_DtApi.m_pApi_AddBoolToObject=cJSON_AddItemToObject;
    g_DtApi.m_pApi_AddNumberToObject=cJSON_AddItemToObject;
    g_DtApi.m_pApi_AddStringToObject=cJSON_AddItemToObject;

    return &g_DtApi;
}
