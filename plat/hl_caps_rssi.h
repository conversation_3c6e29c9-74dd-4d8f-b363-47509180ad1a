/*******************************************************************************
* COPYRIGHT Beijing RHHK Equipment CO.,LTD
********************************************************************************
* �ļ�����:  hl_caps_rssi.h
* ��������:  CAPS�ڲ����ݽṹ���塢������������
* ��    ��:
* ��д����:  2024/12/26
* ˵    ��:
* �޸���ʷ:
* �޸�����    �޸���  BugID/CRID      �޸�����
* ------------------------------------------------------------------------------
* 2024/12/25  zonghui    �����ļ�
*******************************************************************************/
/******************************** Í·ÎÄ¼þ±£»¤¿ªÍ· ******************************/
#ifndef HL_CAPS_rssi_H
#define HL_CAPS_rssi_H

/******************************** °üº¬ÎÄ¼þÉùÃ÷ ********************************/
#include <stdarg.h>
#include <stdio.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#ifndef WIN32
#include <unistd.h>
#else
#include <windows.h>
#endif
#include <stdint.h>
#include "../interface.h"
/******************************** ºêºÍ³£Á¿¶¨Òå ********************************/
#define GPIO_ENABLE         1 
#define GPIO_DISABLE        0
#define RF_MAX              8
#define MEASURE_FREQ_MAX    6
#define MEASURE_GATHER_ROW    40 
#define MEASURE_GATHER_COLOMN 2
#define MEAS_FREQ_PORT   9024
#define GPS_INFO_PORT   9023
#define MEAS_FREQ_ADDR   "192.168.2.1"
#define MEAS_BUFFER_SIZE 3000
#define MEAS_RESULT_MIN_SIZE 26
#define MEAS_FREQ_MAX_COUNT  50
#define MEAS_FREQ_PART_COUNT     5
#define MEAS_FREQ_PART_LENGTH    100
#define POUNDING_GY_COUNT   200

#define DJI_MODULE_PORT   9023
#define DJI_MODULE_ADDR   "192.168.2.2"
#define DJI_RESULT_MIN_SIZE 90
#define DJI_BUFFER_SIZE 5000
#define DJI_PACKET_MAX_COUNT  100
#define DJI_ARG_PART_COUNT     18
#define DJI_ARG_PART_LENGTH    100

#define DJI_DRONE_O4_LIB_MAX     20
#define DJI_DRONE_O4_KEEPLIVE_TIME   15
#define DJI_DRONE_O4_UP_LEVEL    1
#define DIJ_DRONE_O3_DOWN_LEVEL  2

#define DJI_O4_ADD_COUNT     10
#define DJI_O4_PART_COUNT     5
#define DJI_O4_PART_LENGTH    50
#define GE_FREQ_PART_COUNT    4

#define DETCT_O3_DATA_COUNT   (DJI_ARG_PART_COUNT*2)
#define DETCT_O4_DATA_COUNT   (DJI_O4_PART_COUNT*2)
#define DETCT_GE_DATA_COUNT   (GE_FREQ_PART_COUNT*2)

#define GY_DRONE_UPT_NAME_LEN   50
#define GY_DRONE_UPT_MAX_COUNT  50
#define GY_DRONE_FREQ_THRESHOLD  20
#define GY_DRONE_INVALID_TIME   30

#define UDP_PORT 10068
#define TCP_PORT 10087
#define BUFFER_SIZE 5000
#define MAX_CLIENTS 10
/******************************** ¶¨ÒåÀàÐÍ ************************************/
typedef  unsigned int   u32;
typedef  signed int     s32;
typedef  unsigned short u16;
typedef  signed short   s16;
typedef  unsigned char  u8;
typedef  signed char    s8;

typedef struct __GatherRssiInfo
{
    float rssi_drone[RF_MAX];
	float rssi_max[RF_MAX];
	int64_t freq;
	char  data_valid;
}T_RhhkGatherRssiInfo;

typedef struct __UpReportRssiInfo
{
    float rssi_drone;
	float rssi_max;
	float rssi_angle;
	int64_t freq;
}T_RhhkUpReportRssiInfo;

typedef struct __RssiPacketInfo
{
    char  valid;
	float freq; 
	float rssi; 
	char  gpio;
}T_RhhkRssiPacketInfo;

typedef struct __GeneralDroneInfo
{
    char valid;
    float arr_target_freq;
	float arr_target_rssi;
	char  arr_target_serial[MEAS_FREQ_PART_LENGTH]; //max byte of drone serial 
	char  arr_target_model[MEAS_FREQ_PART_LENGTH]; //max byte of drone mode 
}T_RhhkGeneralDroneInfo;

typedef struct __DjiDroneInfo
{
    char valid;
	uint16_t seq_num;  //�����
	uint8_t drone_serial_num[17]; //���˻�Ψһ���к�
	double drone_longitude;//�ɻ�����
	double drone_latitude;//�ɻ�γ��
	float altitude;//����
	float height; //�߶�
	float north_speed;//���ٶ�
	float east_speed;//���ٶ�
	float up_speed; //�����ٶ�
	//uint64_t gpstime; //only for ver2// gpsʱ��
	double pilot_longitude;  //���־���
	double pilot_latitude;  //����γ��
	double home_longitude;//��������
	double home_latitude;//����γ��
	//uint8_t product_type;//��Ʒ�ͺŴ���
	char product_type_str[32];//��Ʒ�ͺ�
	//uint8_t uuid_length;//ͨ��Ψһʶ���볤��
	//uint8_t uuid[18];// ͨ��Ψһʶ����
	float freq;//Ƶ��
	float rssi; //�źŵ�ƽ
	uint16_t device_num;
	float distance;
	//uint16_t  reserved[14];
}T_RhhkDjiDroneInfo;

typedef struct __DjiDroneO4AddInfo
{
    char valid;
	uint16_t seq_num;  //�����
	float freq;//Ƶ��
	float rssi; //�źŵ�ƽ
	uint16_t device_num;
}T_RhhkDjiAddInfo;

typedef struct __GyDroneFilterInfo
{
    char valid;
    char gy_model[GY_DRONE_UPT_NAME_LEN];
    float gy_freq;
    time_t  gy_time;    
}T_RhhkGyFilterInfo;

typedef struct _tag_RhhkGpsData
{
	double dLongitude;  //����
	double dLatitude;   //γ��
	double dAltitude;   //�߶�
	int year;
	int mon;
	int day;
	int hour;
	int minute;
	int second;
}T_RhhkGpsDataInfo;

typedef struct _tag_dronelib
{
    uint8_t drone_serial_num[17];
    long drone_keeplive;
	  char data_valid; /*1-valid, 0-invalid*/
}T_RhhkDroneLib;

typedef struct _tag_RhhkDecrytDrone
{
  uint8_t drone_serial_num[17];//"sn": "F6N8C238R0033TWX", �ɻ����к�
  uint8_t drone_uuid[33];     //"uuid": "00000000000000000000000000000000", ���ֵ�ִ�մ���
  uint8_t product_type;       //"type": 90, ���ʹ���
  double drone_longitude;     //"lon": 0.0, �ɻ�����
  double drone_latitude;      //"lat": 0.0, �ɻ�γ��
  float  drone_altitude;             //"alt": 0,   ����
  float  drone_height;               //"height": 0.0, �߶�
  float east_speed;          //"x": 0.0, �򶫵��ٶ�
  float north_speed;         //"y": 0.0, �򱱵��ٶ�
  float up_speed;            //"z": 0.0, ���ϵ��ٶ�
  float drone_yaw;           //"yaw": 3.08,  �ɻ����ǣ�ûʲô��
  char gpstime[20];          //"gps_time": "1970-01-01 08:00:00",
  double pilot_longitude;    //"pilot_lon": 0.0, ���־���
  double pilot_latitude;     //"pilot_lat": 0.0, ����γ�ȶ�
  double home_longitude;     //"home_lon": 0.0, �����㾭��
  double home_latitude;      //"home_lat": 0.0,������γ��
  char product_type_str[32]; //"model": "Air 3"����
}T_RhhkDecryptDjiDroneInfo;

typedef struct _tag_RhhkDebugQuestion
{
    u8 module_number;
    u8 init_status;
	u32 send_req_cnt;
	u32 recv_msg_cnt;
	char latest_recv_buffer[DJI_BUFFER_SIZE];
	char recv_timeStr[20];	
	u32 online_o4_rpt_cnt;
	u32 digi_o3_rpt_cnt;
	u32 general_rpt_cnt;    	
	T_RhhkDecryptDjiDroneInfo tOnlineLatestDjiO4Info;
}T_RhhkDebugInfo;

typedef struct _tag_RhhkThread
{    
   int udp_sock;    
   int tcp_sock;    
   struct sockaddr_in u1_device_addr;
   struct sockaddr_in u2_device_addr;
   struct sockaddr_in device_addr;
}T_RhhkThreadArgs;

typedef struct 
{
	int sockfd;
  struct sockaddr_in *addr;
	socklen_t size;
	char cmd[50];
} ModuleMonitorArg;

/******************************** º¯ÊýºêÀàÐÍ ************************************/

/******************************** È«¾Ö±äÁ¿ÉùÃ÷ ********************************/

/******************************** Íâ²¿º¯ÊýÔ­ÐÍÉùÃ÷ ****************************/
extern char *CreateGeneralDroneSN(char *model, float freq, float rssi);
extern void  Handle_RfRssiDataToPacket(T_RhhkGatherRssiInfo *pstrGatherRssiInfo);
extern void CreatRssiSwitchPthread(void);
extern int Create_FreqMeas_Socket(char *ip_remote, char *ip_local);
extern void Create_DjiModule_Pthread(void);
extern int Create_CliUdpSocket(char *ip, int port);
extern int Rhhk_UpdateTokenTask(void);
extern int Rhhk_JudgeDirExistAndCreate(const char *dir_path);
extern int Rhhk_InitDebugData(void);
extern int Rhhk_WriteDebugData(void);
extern char* Get_Current_Time(void);
extern int Rhhk_SetDebugData(u8 module_number, u8 argment_number, u32 argment_value, char *buffer, int buffer_size);
/******************************** Í·ÎÄ¼þ±£»¤½áÎ² ******************************/
#endif
/******************************** Í·ÎÄ¼þ½áÊø **********************************/

