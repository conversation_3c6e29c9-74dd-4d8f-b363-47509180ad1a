/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* 文件名称:  hl_caps_adapt.h
* 功能描述:  CAPS 结构化 库适配层头文件
* 版    本:
* 编写日期:  2024/08/26
* 说    明:
* 修改历史:
* 修改日期    修改人  BugID/CRID      修改内容
* ------------------------------------------------------------------------------
* 2024/10/29  zonghui    创建文件
*******************************************************************************/
#ifndef HL_CAPS_ADAPT_H
#define HL_CAPS_ADAPT_H

#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "hl_caps_adapt.h"
#include "hl_caps_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/********************************  Private Typedef**********************************/
typedef unsigned int u32;
typedef signed int s32;
typedef unsigned short u16;
typedef signed short s16;
typedef unsigned char u8;
typedef signed char s8;

/********************************  类型定义  **********************************/
typedef int DtInt;
typedef char DtChar;
typedef void DtVoid;
typedef double DtDouble;
typedef cJSON DtAdapt;
typedef float DtFloat;
typedef unsigned int DtU32;
typedef unsigned short DtU16;
typedef unsigned char DtU8;
typedef cJSON_Hooks DtAdapt_Hooks;

typedef DtVoid (*DtAdapt_InitHooks)(DtAdapt_Hooks *hooks);

typedef DtAdapt * (*DtAdapt_Parse)(const DtChar *value);

typedef DtChar * (*DtAdapt_Print)(DtAdapt *item);

typedef DtChar * (*DtAdapt_PrintUnformatted)(DtAdapt *item);

typedef DtChar * (*DtAdapt_PrintBuffered)(cJSON *item, DtInt prebuffer, DtInt fmt);

typedef DtVoid (*DtAdapt_Delete)(DtAdapt *c);

typedef DtInt (*DtAdapt_GetArraySize)(DtAdapt *array);

typedef DtAdapt * (*DtAdapt_GetArrayItem)(DtAdapt *array, DtInt item);

typedef DtAdapt * (*DtAdapt_GetObjectItem)(DtAdapt *object, const DtChar *string);

typedef const DtChar * (*DtAdapt_GetErrorPtr)(DtVoid);

/* These calls create a cJSON item of the appropriate type. */
typedef DtAdapt * (*DtAdapt_CreateNull)(DtVoid);

typedef DtAdapt * (*DtAdapt_CreateTrue)(DtVoid);

typedef DtAdapt * (*DtAdapt_CreateFalse)(DtVoid);

typedef DtAdapt * (*DtAdapt_CreateBool)(DtInt b);

typedef DtAdapt * (*DtAdapt_CreateNumber)(DtDouble num);

typedef DtAdapt * (*DtAdapt_CreateString)(const DtChar *string);

typedef DtAdapt * (*DtAdapt_CreateArray)(DtVoid);

typedef DtAdapt * (*DtAdapt_CreateObject)(DtVoid);

/* These utilities create an Array of count items. */
typedef DtAdapt * (*DtAdapt_CreateIntArray)(const DtInt *numbers, DtInt count);

typedef DtAdapt * (*DtAdapt_CreateFloatArray)(const DtFloat *numbers, DtInt count);

typedef DtAdapt * (*DtAdapt_CreateDoubleArray)(const DtDouble *numbers, DtInt count);

typedef DtAdapt * (*DtAdapt_CreateStringArray)(const DtChar **strings, DtInt count);

/* Append item to the specified array/object. */
typedef DtVoid (*DtAdapt_AddItemToArray)(DtAdapt *array, DtAdapt *item);

typedef DtVoid (*DtAdapt_AddItemToObject)(DtAdapt *object, const DtChar *string, DtAdapt *item);

typedef DtVoid (*DtAdapt_AddItemToObjectCS)(DtAdapt *object, const DtChar *string, DtAdapt *item);

/* Use this when string is definitely const (i.e. a literal, or as good as), and will definitely survive the cJSON object */
/* Append reference to item to the specified array/object. Use this when you want to add an existing cJSON to a new cJSON, but don't want to corrupt your existing cJSON. */
typedef DtVoid (*DtAdapt_AddItemReferenceToArray)(DtAdapt *array, DtAdapt *item);

typedef DtVoid (*DtAdapt_AddItemReferenceToObject)(DtAdapt *object, const DtChar *string, DtAdapt *item);

/* Remove/Detatch items from Arrays/Objects. */
typedef DtAdapt * (*DtAdapt_DetachItemFromArray)(DtAdapt *array, DtInt which);

typedef DtVoid (*DtAdapt_DeleteItemFromArray)(DtAdapt *array, DtInt which);

typedef DtAdapt * (*DtAdapt_DetachItemFromObject)(DtAdapt *object, const DtChar *string);

typedef DtVoid (*DtAdapt_DeleteItemFromObject)(DtAdapt *object, const DtChar *string);

/* Update array items. */
typedef DtVoid (*DtAdapt_InsertItemInArray)(DtAdapt *array, DtInt which, DtAdapt *newitem);

/* Shifts pre-existing items to the right. */
typedef DtVoid (*DtAdapt_ReplaceItemInArray)(DtAdapt *array, DtInt which, DtAdapt *newitem);

typedef DtVoid (*DtAdapt_ReplaceItemInObject)(DtAdapt *object, const DtChar *string, DtAdapt *newitem);

/* Duplicate a cJSON item */
typedef DtAdapt * (*DtAdapt_Duplicate)(DtAdapt *item, DtInt recurse);

/* Duplicate will create a new, identical cJSON item to the one you pass, in new memory that will
need to be released. With recurse!=0, it will duplicate any children connected to the item.
The item->next and ->prev pointers are always zero on return from Duplicate. */

/* ParseWithOpts allows you to require (and check) that the JSON is null terminated, and to retrieve the pointer to the final byte parsed. */
typedef DtAdapt * (*DtAdapt_ParseWithOpts)(const DtChar *value, const DtChar **return_parse_end,
                                           DtInt require_null_terminated);

typedef DtVoid (*DtAdapt_Minify)(DtChar *json);

/* Macros for creating things quickly. */
#define cJSON_AddNullToObject(object,name) cJSON_AddItemToObject(object, name, cJSON_CreateNull())
#define cJSON_AddTrueToObject(object,name) cJSON_AddItemToObject(object, name, cJSON_CreateTrue())
#define cJSON_AddFalseToObject(object,name) cJSON_AddItemToObject(object, name, cJSON_CreateFalse())
#define cJSON_AddBoolToObject(object,name,b) cJSON_AddItemToObject(object, name, cJSON_CreateBool(b))
#define cJSON_AddNumberToObject(object,name,n)  cJSON_AddItemToObject(object, name, cJSON_CreateNumber(n))
#define cJSON_AddStringToObject(object,name,s) cJSON_AddItemToObject(object, name, cJSON_CreateString(s))

typedef DtVoid (*DtAdapt_AddItemToObject)(DtAdapt *object, const DtChar *string, DtAdapt *item);

/* When assigning an integer value, it needs to be propagated to valuedouble too. */
#define DtAdapt_SetIntValue(object,val)  ((object)?(object)->valueint=(object)->valuedouble=(val):(val))
#define DtAdapt_SetNumberValue(object,val) ((object)?(object)->valueint=(object)->valuedouble=(val):(val))

typedef struct {
    DtAdapt_InitHooks m_pApi_InitHooks;
    DtAdapt_Parse m_pApi_Parse;
    DtAdapt_Print m_pApi_Print;
    DtAdapt_PrintUnformatted m_pApi_PrintUnformatted;
    DtAdapt_PrintBuffered m_pApi_PrintBuffered;
    DtAdapt_Delete m_pApi_Delete;

    DtAdapt_GetArraySize m_pApi_GetArraySize;
    DtAdapt_GetArrayItem m_pApi_GetArrayItem;
    DtAdapt_GetObjectItem m_pApi_GetObjectItem;

    DtAdapt_GetErrorPtr m_pApi_GetErrorPtr;

    /* These calls create a cJSON item of the appropriate type. */
    DtAdapt_CreateNull m_pApi_CreateNull;
    DtAdapt_CreateTrue m_pApi_CreateTrue;
    DtAdapt_CreateFalse m_pApi_CreateFalse;
    DtAdapt_CreateBool m_pApi_CreateBool;
    DtAdapt_CreateNumber m_pApi_CreateNumber;
    DtAdapt_CreateString m_pApi_CreateString;
    DtAdapt_CreateArray m_pApi_CreateArray;
    DtAdapt_CreateObject m_pApi_CreateObject;

    /* These utilities create an Array of count items. */
    DtAdapt_CreateIntArray m_pApi_CreateIntArray;
    DtAdapt_CreateFloatArray m_pApi_CreateFloatArray;
    DtAdapt_CreateDoubleArray m_pApi_CreateDoubleArray;
    DtAdapt_CreateStringArray m_pApi_CreateStringArray;

    /* Append item to the specified array/object. */
    DtAdapt_AddItemToArray m_pApi_AddItemToArray;
    DtAdapt_AddItemToObject m_pApi_AddItemToObject;
    DtAdapt_AddItemToObjectCS m_pApi_AddItemToObjectCS;
    /* Use this when string is definitely const (i.e. a literal, or as good as), and will definitely survive the cJSON object */
    /* Append reference to item to the specified array/object. Use this when you want to add an existing cJSON to a new cJSON, but don't want to corrupt your existing cJSON. */
    DtAdapt_AddItemReferenceToArray m_pApi_AddItemReferenceToArray;
    DtAdapt_AddItemReferenceToObject m_pApi_AddItemReferenceToObject;

    /* Remove/Detatch items from Arrays/Objects. */
    DtAdapt_DetachItemFromArray m_pApi_DetachItemFromArray;
    DtAdapt_DeleteItemFromArray m_pApi_DeleteItemFromArray;
    DtAdapt_DetachItemFromObject m_pApi_DetachItemFromObject;
    DtAdapt_DeleteItemFromObject m_pApi_DeleteItemFromObject;

    /* Update array items. */
    DtAdapt_InsertItemInArray m_pApi_InsertItemInArray;
    DtAdapt_ReplaceItemInArray m_pApi_ReplaceItemInArray;
    DtAdapt_ReplaceItemInObject m_pApi_ReplaceItemInObject;

    /* Duplicate a cJSON item */
    DtAdapt_Duplicate m_pApi_Duplicate;
    /* Duplicate will create a new, identical cJSON item to the one you pass, in new memory that will
    need to be released. With recurse!=0, it will duplicate any children connected to the item.
    The item->next and ->prev pointers are always zero on return from Duplicate. */

    /* ParseWithOpts allows you to require (and check) that the JSON is null terminated, and to retrieve the pointer to the final byte parsed. */
    DtAdapt_ParseWithOpts m_pApi_ParseWithOpts;
    DtAdapt_Minify m_pApi_Minify;
    DtAdapt_AddItemToObject m_pApi_AddNullToObject;
    DtAdapt_AddItemToObject m_pApi_AddTrueToObject;
    DtAdapt_AddItemToObject m_pApi_AddFalseToObject;
    DtAdapt_AddItemToObject m_pApi_AddBoolToObject;
    DtAdapt_AddItemToObject m_pApi_AddNumberToObject;
    DtAdapt_AddItemToObject m_pApi_AddStringToObject;
} DtStruct_ApiSet;

/****************************  外部函数原型声明  ******************************/
extern DtStruct_ApiSet *DT_Adapt_Init(DtVoid);

#ifdef __cplusplus
}
#endif

#endif
