/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* 文件名称: hl_caps_report.cpp
* 功能描述: CAPS 配置操作和报告生成
* 使用说明:
* 文件作者: zonghui
* 编写日期: 204/10/30
* 修改历史:
*
* 修改日期    修改人  BugID/CRID      修改内容
* ------------------------------------------------------------------------------
*2024/10/30  zonghui                  创建文件
* ------------------------------------------------------------------------------*/

#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>

#include <string.h>
#include <dirent.h>
#include <sys/stat.h>

#ifndef WIN32
#include <unistd.h>
#include <thread>
#include <fcntl.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#else
#include <windows.h>
#endif
#include "hl_caps_config.h"
#include "hl_caps_report.h"
#include "hl_caps_rdi.h"
#include "hl_caps_rssi.h"
/******************************* 局部宏定义 ***********************************/
#define  SEVER_PORT   10085
#define  SEVER_ADDR   "192.168.100.130"
#define  PACKET_BUFFER_LEN   3000
#define  MAX_JAVA_PLATFORM_NUM  5
#define  MAX_DRONE_INFO_LEN     50
#define  MAX_RCV_BUFFER_SIZE  2048

#define MAX_FILE_SIZE (20 * 1024 * 1024) // 10MB
#define MAX_FILES 50
extern int Find_MaxLogNumber();
extern void Rotate_Log(int *current_num, FILE **log_file);
extern void Write_Log(const char *message, int *current_num, FILE **log_file);

export void CreateConfigFile(char *filename,  char *ConfigData);
export int Send_MsgToJava(char *outdata, u32 u32DataLength);
export int Handle_GpsFromFreqPatch(void);
export int Handle_MakeVersionInfo(void);
export long Rhhk_TransTimeToString(char *dateTimeStr);
export void Rhhk_RecordDroneTolib(uint8_t RidSn[16]);
export int SendDroneToQueue(int new_sockfd);
/******************************* 全局变量 ***********************************/
int detect_sockfd, new_socket;
struct sockaddr_in detect_server_addr;
int log_cycle_num = 1; /*log define first num*/
int log_cycle_base = 20;
int client_sockfd[MAX_JAVA_PLATFORM_NUM];
int g_debug_switch = 0;
int O4_count = 0;
double g_manu_longitude = 0;
double g_manu_latitude = 0;
double g_manu_altitude = 0;
char g_device_code[] = "";
int  RdiOrOnlineOkStopVirtual = 0;
/******************************* 局部常数和类型定义 ***************************/
typedef struct
{
    u8 u8SequenceNum;
    u8 u8NofStep;
    u8 u8StepNum[1];
}HL_STRU_CAPS_SeqCaseList;

typedef struct
{
    u8 u8SeqCaseNum;
    HL_STRU_CAPS_SeqCaseList struSeqCaseList[1];
}HL_STRU_CAPS_SeqCaseInfo;

typedef struct
{
    char  packet_buffer[PACKET_BUFFER_LEN];
    int   packet_len;
    int   packet_rcvsign;
	char  packet_drone_model[MAX_DRONE_INFO_LEN];
}DroneDetect_Packet;

typedef struct
{
	int   packet_wr_index;
	int   packet_rd_index;
  DroneDetect_Packet DroneRptQueue[MAX_UPREPORT_BUFFER_COUNT];
}DroneUprpt_Queue;
/******************************* 全局变量定义/初始化 **************************/
DtStruct_ApiSet *g_pApiSet = NULLPTR;
HL_STRU_CAPS_SeqCaseInfo g_struSeqCaseInfo;
u32 g_dtm_default_print_level = 5;
u32 g_u32CapsSaMaxSceneNum = 2;
u32 g_u32IfRecordSwitch = SWITCH_OPEN;
DroneDetect_Packet  g_DroneDetectPacket;
DroneUprpt_Queue g_DroneUptQueue;

pthread_mutex_t mutex  = PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t queue_mutex = PTHREAD_MUTEX_INITIALIZER; 
extern T_RHHKReadRdiO4Info g_RhhkReadRdiO4Info[DJI_O4_RDI_MAX_COUNT];
extern T_RhhkUpReportRssiInfo  g_UpReportRssiInfo;
extern T_RhhkGeneralDroneInfo struGeneralDroneInfo[MEAS_FREQ_MAX_COUNT];

int g_rid_index = 0;
int detect_general_drone[DRONE_GENERAL_TYPE_NUM];
int detect_packet_flag[DRONE_GENERAL_TYPE_NUM];
int g_drone_type_index = 0;

int support_general_drone[] = 
{        PACKET_VEDIO_LightBrdige_OTHERS, 
	     PACKET_VEDIO_DAOTONG, 
	     PACKET_VEDIO_Yuneec_H52e,
		 PACKET_VEDIO_HARBOSEN,
	   	 PACKET_VEDIO_ANALOG,
		 PACKET_VEDIO_WIFI_PARROT,
		 PACKET_VEDIO_WIFI_POWEREGG,
		 PACKET_VEDIO_WIFI_SJF11,
		 PACKET_VEDIO_ENHANCED_WIFI,
		 PACKET_DATALINK_P900,
		 PACKET_VEDIO_FEIMI,
		 PACKET_VEDIO_Avatar,
		 PACKET_VEDIO_ZONGHENG,
		 PACKET_HAIKAN_WEISHI,
	     PACKET_VEDIO_LTE_TYPE
};

/******************************Local Function Define **************************/
extern int CheckWrQueueStatus(void);
extern void ReleaseWrQueueLock(void);
extern void  CopyDroneToQueue(char *out, u32 u32DataLength, char* pdrone_model);
extern void  CopyDroneDataPacket(char *out, u32 u32DataLength, char* drone_model);
extern int Handle_DroneDetectData(int new_sockfd, int listen_sockfd, fd_set read_fds);
/******************************* 函数实现 *************************************/
/*Delete Tab, Return Symbol  in cJson String */
char * DelString(char *cJsonSrcStr, char *dstStr, char *cJsonDstStr)
{
    int len = 0;
    char *ptr = NULL;
    char *ptd = NULL;
    if((NULLPTR == cJsonSrcStr) || (NULLPTR == dstStr) || (NULLPTR == cJsonDstStr))
        return NULLPTR;

   len = strlen(cJsonSrcStr);
   ptd = malloc(len);
   if(NULLPTR == ptd)
        return NULLPTR;
   
    ptr = strtok(cJsonSrcStr, dstStr);
    if(NULLPTR == ptr)
    {
        free(ptd);
        return NULLPTR;
    }
    strcpy((s8 *)ptd, (const s8 *)ptr);
    while(ptr = strtok(NULLPTR, dstStr))
    {
        if(ptr == NULLPTR)
            break;
        /*ptr = ptr + sizeof(char); */
        strcat(ptd, ptr);
    }
    strcpy((s8 *)cJsonDstStr, (const s8 *)ptd);
    free(ptd);
    
    return cJsonDstStr; 
}

/*****************************************************************************
 函 数 名  : CAPS_JudgeFileExist
 功能描述  : 判断文件是否存在
 输入参数  :
 输出参数  : 无
 返 回 值  : 0--文件存在, 1--表示文件不存在
 调用函数  :
 被调函数  :
 说明:
 修改历史      :
  1.日    期   : 2024年10月25日
    作    者   : 
    修改内容   : 新生成函数
*****************************************************************************/
s32 CAPS_JudgeFileExist(const char* pfilename)
{
     int exist = 0;
    if(NULLPTR == pfilename)
    {
        return 1; /*File is not exist*/ 
    }

    #ifndef WIN32
    exist = access(pfilename, F_OK);
    #else
    exist = _access(pfilename, 0);
    #endif
    if(0 == exist)
    {
        return 0;  /*文件存在*/ 
    }
    else
    {
        return 1;  /*文件不存在*/
    }
}

/* Write a file, parse, render back, etc. */
void CreateConfigFile(char *filename,  char *ConfigData)
{
    FILE *f;
    long len;
 
    if((NULLPTR == filename) || (NULLPTR == ConfigData))
    {
        //MISC_Alarm(AP_MACRO_NULL_POINTER_ERROR_TDL, HL_CAPS_NULL_FIELDVALUE, 
        //                        __FILE__, __LINE__, g_u32CapsKeyPara, g_s32CapsNofPara);
        return;  
    }
    
    f=fopen(filename,"w");
    if(NULLPTR == f)
    {
        // MISC_Alarm(AP_MACRO_NULL_POINTER_ERROR_TDL, HL_CAPS_NULL_FIELDVALUE, 
        //                        __FILE__, __LINE__, g_u32CapsKeyPara, g_s32CapsNofPara);
         return;
    }
    fseek(f,0,SEEK_END);
    /*len=ftell(f);*/
    len = strlen(ConfigData);
    fseek(f,0,SEEK_SET);
    /*fwrite(ConfigData,1,5000,f);*/
    fwrite(ConfigData,1,len,f);
    fclose(f);
   return;
}
/*******************************************************************************
* 函数名称: RemoveDot
* 函数功能: 清除干扰字符，将目标字符串输出
* 函数参数: 
* 参数名称: 		类型			输入/输出	   描述
* str    			char*			输入/输出	   配置字符串
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/12/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
void RemoveDot (char *str)
{
  int len = strlen (str);
  int j = 0;
  char *pAlloc = (char *)malloc(len);
  if(pAlloc == NULL)
  {
      return;
  }
  for (int i = 0; i < len; i++)
    {
      if ((str[i] != '.') && (str[i] != '_') && (str[i] != '-') && (str[i] != ' '))
      {
           *(pAlloc + j) = str[i];
           j++;
        }
      else
        {
          continue;
        }
    }
    *(pAlloc + j) = '\0';
    strcpy(str, pAlloc);
    free(pAlloc);
    return;
}

/*******************************************************************************
* 函数名称: CreateGeneralDroneSN
* 函数功能: 创建一个临时Serial Number
* 函数参数: 
* 参数名称: 		类型			输入/输出	   描述
* str    			char*			输入/输出	   配置字符串
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/12/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
char *CreateGeneralDroneSN(char *model, float freq, float rssi)
{
    if (strlen(model) < 2)
    {
        return NULLPTR;
	}

    char cDroneSN[MAX_DRONE_INFO_LEN];
	memset(cDroneSN, 0, sizeof(cDroneSN));
	snprintf(cDroneSN, MAX_DRONE_INFO_LEN, "%s%f%f", model, freq, rssi);
	RemoveDot(cDroneSN);
    
	snprintf(cDroneSN, MAX_DRONE_INFO_LEN, "%s%s%d", "GY", model, int(freq/100));
	return cDroneSN;
}

/*******************************************************************************
* 函数名称: Trans_32BytesToString
* 函数功能: 字节流数组转换成字符串
* 函数参数: 
* 参数名称: 	         类型	                  输入/输出	   描述
* dji_proxy              uint8_t                  输入         类型识别参数
* output                 char*  	                输入
* len                    int                      输入
* 返回值:                char *
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2025/06/20  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
char* Trans_32BytesToString(const uint8_t uuid[32], char* output,  int len) 
{    
    if (output == NULLPTR)
    {
        return NULL;
    }
	
    memset(output, 0, len*sizeof(char));
    memcpy(output, uuid, len);
    //for(int i = 0; i < 32; i++) 
	  //{        
	  //    sprintf(output + (i * 2), "%02X", uuid[i]);    
	  //}    
	  output[64] = '\0';  // 确保字符串终止
	  return output;
}

/*******************************************************************************
* 函数名称: QueryGeneralDroneType
* 函数功能: 构造通用无人机Type(暂未启用)
* 函数参数: 
* 参数名称: 		类型			            输入/输出	   描述
* message  			DJI_FLIGHT_INFO_Str			输入	       无人机数据结构
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
char *QueryGeneralDroneType(DJI_FLIGHT_INFO_Str *message)
{
    
    char cDroneType[MAX_DRONE_INFO_LEN];
	memset(cDroneType, 0, sizeof(cDroneType));
    
	if (message->packet_type == PACKET_DJI_ID)	snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_DJI_ID", '_')+1));
	if (message->packet_type == PACKET_VEDIO_DJI) snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_DJI", '_')+1));
	if (message->packet_type == PACKET_VEDIO_LightBrdige_DJI)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_LightBrdige_DJI", '_')+1));
	if (message->packet_type == PACKET_VEDIO_LightBrdige_OTHERS)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_LightBrdige_OTHERS", '_')+1));
	if (message->packet_type == PACKET_VEDIO_DAOTONG || message->packet_type == PACKET_VEDIO_Yuneec_H52e)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_DAOTONG", '_')+1));
	//if (message->packet_type == PACKET_VEDIO_Yuneec_H52e)  snprintf(cDroneSN, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_Yuneec_H52e", '_')+1));
	if (message->packet_type == PACKET_VEDIO_WIFI_DJI_5M)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_WIFI_DJI_5M", '_')+1));
	if (message->packet_type == PACKET_VEDIO_WIFI_DJI)	snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_WIFI_DJI", '_')+1));
	if (message->packet_type == PACKET_VEDIO_HARBOSEN)	snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_HARBOSEN", '_')+1));
	if (message->packet_type == PACKET_VEDIO_ANALOG && message->bandwidth == 5e6)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_ANALOG", '_')+1));
	if (message->packet_type == PACKET_VEDIO_ANALOG && message->bandwidth == 4.2e6)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_WIFI_PARROT", '_')+1));
	if (message->packet_type == PACKET_VEDIO_WIFI_PARROT)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_WIFI_PARROT", '_')+1));
	if (message->packet_type == PACKET_VEDIO_WIFI_POWEREGG)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_WIFI_POWEREGG", '_')+1));
	if (message->packet_type == PACKET_VEDIO_WIFI_SJF11)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_WIFI_SJF11", '_')+1));
	if (message->packet_type == PACKET_VEDIO_ENHANCED_WIFI)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_ENHANCED_WIFI", '_')+1));
	if (message->packet_type == PACKET_DATALINK_P900)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_DATALINK_P900", '_')+1));
	if (message->packet_type == PACKET_VEDIO_FEIMI)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_FEIMI", '_')+1));
	if (message->packet_type == PACKET_VEDIO_Avatar)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_Avatar", '_')+1));
	if (message->packet_type == PACKET_VEDIO_ZONGHENG)	snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_ZONGHENG", '_')+1));
	if (message->packet_type == PACKET_DJI_M200)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_DJI_M200", '_')+1));
	if (message->packet_type == PACKET_HAIKAN_WEISHI)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_HAIKAN_WEISHI", '_')+1));
	if (message->packet_type == PACKET_VEDIO_DJI_O4)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_DJI_O4", '_')+1));
	if (message->packet_type == PACKET_VEDIO_LTE_TYPE)	snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_LTE_TYPE", '_')+1));
	if (message->packet_type == PACKET_VEDIO_LightBridge)  snprintf(cDroneType, MAX_DRONE_INFO_LEN, "%s", (strrchr("PACKET_VEDIO_LightBridge", '_')+1));
   
	return cDroneType;
}

/*******************************************************************************
* 函数名称: printArrayAsString
* 函数功能: 调试函数，快速输出字符数组
* 函数参数: 
* 参数名称: 	类型	    输入/输出	   描述
* arr  			int []		输入	       字符数组
* size          int         输入           数组长度
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
void printArrayAsString(int arr[], int size) 
{
    char *str = malloc(size * 10); 
	if (str == NULL)
	{
        Dbg_Level_Print(LOG_INFO,"Memory allocation failed\n");
        return;
    }
    str[0] = '\0';
 
    for (int i = 0; i < size; i++) 
    {
        sprintf(str + strlen(str), "%d ", arr[i]); 
    }
 
    Dbg_Level_Print(LOG_INFO,"%s\n", str); 
    free(str); 
}
 

/*******************************************************************************
* 函数名称: DebugDroneDectectData
* 函数功能: 记录支持无人机和探测无人机日志，目的是查找环境中不支持的无人机类型
* 函数参数: 
* 参数名称: 	类型	                输入/输出	   描述
* message  			DJI_FLIGHT_INFO_Str		输入	       字符数组
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
void DebugDroneDectectData(DJI_FLIGHT_INFO_Str *message)
{
    char buffer[500];
	time_t timep;
	struct tm* currentTime;
	int size = 0;
	char *suport_type = NULL; 
	char *detect_type = NULL; 
	char *detect_flag = NULL; 

    g_drone_type_index = g_drone_type_index % DRONE_GENERAL_TYPE_NUM;
	if ((g_drone_type_index < DRONE_GENERAL_TYPE_NUM) && (message != NULL))
	{
	    detect_general_drone[g_drone_type_index] = message->packet_type;
		detect_packet_flag[g_drone_type_index] = message->decode_flag;
		g_drone_type_index++;
	}

    if (1)
	{
		time(&timep);
		currentTime = localtime(&timep);		
		FILE* fid = fopen("record_drone_type.log", "a");
		
	    size = (sizeof(support_general_drone)/sizeof(support_general_drone[0]));
        suport_type = malloc(size * 10);
        if (suport_type == NULL)
	    {
            Dbg_Level_Print(LOG_ERROR,"Memory allocation failed\n");
            return;
        }
        suport_type[0] = '\0';
		for (int i = 0; i < size; i++) 
        {
            sprintf(suport_type + strlen(suport_type), "%d ", support_general_drone[i]); 
        }
		
		fprintf(fid, "%d-%02d-%02d %02d:%02d:%02d  [supt type]%s\n\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, suport_type);
		free(suport_type);
        //------------------------------------------------------------------------------//

        size = (sizeof(detect_general_drone)/sizeof(detect_general_drone[0]));
        detect_type = malloc(size * 10);
        if (detect_type == NULL)
	    {
            Dbg_Level_Print(LOG_ERROR, "Memory allocation failed\n");
            return;
        }
        detect_type[0] = '\0';
		for (int i = 0; i < size; i++) 
        {
            sprintf(detect_type + strlen(detect_type), "%d ", detect_general_drone[i]); 
        }
		fprintf(fid, "%d-%02d-%02d %02d:%02d:%02d  [dect type]%s\n\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, detect_type);
        free(detect_type);


		
		//------------------------------------------------------------------------------//
        size = (sizeof(detect_packet_flag)/sizeof(detect_packet_flag[0]));
        detect_flag = malloc(size * 10);
        if (detect_flag == NULL)
	    {
            Dbg_Level_Print(LOG_ERROR, "Memory allocation failed\n");
            return;
        }
        detect_flag[0] = '\0';
		for (int i = 0; i < size; i++) 
        {
            sprintf(detect_flag + strlen(detect_flag), "%d ", detect_packet_flag[i]); 
        }
		fprintf(fid, "%d-%02d-%02d %02d:%02d:%02d  [dect flag]%s\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, detect_flag);
        fprintf(fid, "%d-%02d-%02d %02d:%02d:%02d  [over]%s\n\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, "===========finish=========");
		free(detect_flag);
        fclose(fid);
		return;
     }
	 return;
}

/*******************************************************************************
* 函数名称: CopyDroneDataPacket
* 函数功能: 数据拷贝至缓冲区
* 函数参数: 
* 参数名称: 	类型	    输入/输出	   描述
* out  			char*       输入	       缓冲区地址
* u32DataLength u32         输入           长度
* pdrone_model  char*       输入           model类型
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
void  CopyDroneDataPacket(char *out, u32 u32DataLength, char* pdrone_model)
{  
    int MaxBuf_Len = PACKET_BUFFER_LEN*sizeof(char);
	int drone_model_len  = 0;
    if (u32DataLength >= MaxBuf_Len)
    {
       g_DroneDetectPacket.packet_len = MaxBuf_Len;
    }
    else
    {
       g_DroneDetectPacket.packet_len = u32DataLength + 1;
    }
    memset(&(g_DroneDetectPacket.packet_buffer), 0, MaxBuf_Len);
    pthread_mutex_lock(&mutex);
    memcpy(&(g_DroneDetectPacket.packet_buffer), out, g_DroneDetectPacket.packet_len);
    g_DroneDetectPacket.packet_buffer[g_DroneDetectPacket.packet_len] = '\0';
    g_DroneDetectPacket.packet_rcvsign = 1;
	if (pdrone_model != NULL)
	{
	    drone_model_len = ((strlen(pdrone_model) < 50) ? strlen(pdrone_model) : 50);
        memset(&(g_DroneDetectPacket.packet_drone_model), 0, 50);
		memcpy(&(g_DroneDetectPacket.packet_drone_model), pdrone_model, drone_model_len);
	}
    pthread_mutex_unlock(&mutex);
    return;
}

/*******************************************************************************
* 函数名称: InitDroneQueue
* 函数功能: 初始化
* 函数参数: 
* 参数名称: 	类型	    输入/输出	   描述
* out  			char*       输入	       缓冲区地址
* u32DataLength u32         输入           长度
* pdrone_model  char*       输入           model类型
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
void InitDroneQueue(void)
{
  memset(&g_DroneUptQueue, 0, sizeof(g_DroneUptQueue));
  Dbg_Level_Print(LOG_DEBUG, "InitDroneQueue: Start Restore!\n");
	return;
}

/*******************************************************************************
* 函数名称: CheckWrQueueStatus
* 函数功能: 检查队列状态
* 函数参数: 
* 参数名称: 	类型	    输入/输出	   描述
* void  		
* 返回值: status
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int CheckWrQueueStatus(void)
{
   int chk_count = 0;  
   int wr_index = (g_DroneUptQueue.packet_wr_index % MAX_UPREPORT_BUFFER_COUNT);
   if ((g_DroneUptQueue.DroneRptQueue[wr_index].packet_rcvsign == 0) 
   	        && (g_DroneUptQueue.DroneRptQueue[wr_index].packet_len == 0))
   {
       return wr_index; 
   }	
   
   while (chk_count < MAX_UPREPORT_BUFFER_COUNT)
   {
       wr_index = (g_DroneUptQueue.packet_wr_index++ % MAX_UPREPORT_BUFFER_COUNT);
       if ((g_DroneUptQueue.DroneRptQueue[wr_index].packet_rcvsign == 0) 
	   	     && (g_DroneUptQueue.DroneRptQueue[wr_index].packet_len == 0))
       {
           return wr_index; 
	   }
	   chk_count++;
   }

   return -1;
}

/*******************************************************************************
* 函数名称: ReleaseWrQueueLock
* 函数功能: 检查队列状态
* 函数参数: 
* 参数名称: 	类型	    输入/输出	   描述
* void  		
* 返回值: status
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -------------------------------------------------------------------------
* 2025/7/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
void ReleaseWrQueueLock(void)
{
   Dbg_Level_Print(LOG_DEBUG, "ReleaseWrQueueLock: Queue is Release!\n");
}

/*******************************************************************************
* 函数名称: CopyDroneToQueue
* 函数功能: 数据拷贝至队列
* 函数参数: 
* 参数名称: 	类型	    输入/输出	   描述
* out  			char*       输入	       缓冲区地址
* u32DataLength u32         输入           长度
* pdrone_model  char*       输入           model类型
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
void CopyDroneToQueue(char *out, u32 u32DataLength, char* pdrone_model)
{
   int wr_index = 0;
	 int rd_index = 0;
	 int MaxBuf_Len = PACKET_BUFFER_LEN*sizeof(char);
	 int drone_model_len  = 0;
	 wr_index = g_DroneUptQueue.packet_wr_index;
	 rd_index = g_DroneUptQueue.packet_rd_index;

   pthread_mutex_lock(&queue_mutex);
   wr_index == CheckWrQueueStatus();
   if (wr_index == -1)
   {
        return;
	 }

   if ((wr_index >=  MAX_UPREPORT_BUFFER_COUNT))
   {
       wr_index = (wr_index % MAX_UPREPORT_BUFFER_COUNT);
       g_DroneUptQueue.packet_wr_index = 0;
	 }
   
	 memset(&(g_DroneUptQueue.DroneRptQueue[wr_index]), 0, sizeof(g_DroneUptQueue.DroneRptQueue[wr_index]));
	 Dbg_Level_Print(LOG_INFO, "^_^bf[wr_index:%d][packet_rcvsign=%d][packet_len=%d]\n", wr_index, 
   g_DroneUptQueue.DroneRptQueue[wr_index].packet_rcvsign, g_DroneUptQueue.DroneRptQueue[wr_index].packet_len);
   if ((g_DroneUptQueue.DroneRptQueue[wr_index].packet_rcvsign == 0) && (g_DroneUptQueue.DroneRptQueue[wr_index].packet_len == 0))
   {
       pthread_mutex_lock(&mutex);
       if (u32DataLength >= MaxBuf_Len)
       {
          g_DroneUptQueue.DroneRptQueue[wr_index].packet_len = MaxBuf_Len;
       }
       else
       {
          g_DroneUptQueue.DroneRptQueue[wr_index].packet_len = u32DataLength;
       }
	   
	   memcpy(&(g_DroneUptQueue.DroneRptQueue[wr_index].packet_buffer), out, g_DroneUptQueue.DroneRptQueue[wr_index].packet_len);
	   if (pdrone_model != NULL)
	   {
	       drone_model_len = ((strlen(pdrone_model) < 50) ? strlen(pdrone_model) : 50);
           memset(&(g_DroneUptQueue.DroneRptQueue[wr_index].packet_drone_model), 0, 50);
		   memcpy(&(g_DroneUptQueue.DroneRptQueue[wr_index].packet_drone_model), pdrone_model, drone_model_len);
	   }
	   g_DroneUptQueue.DroneRptQueue[wr_index].packet_rcvsign = 1;
	   Dbg_Level_Print(LOG_INFO, "^_^af[wr_index:%d][packet_rcvsign=%d][packet_len=%d]\n", wr_index, 
	   	    g_DroneUptQueue.DroneRptQueue[wr_index].packet_rcvsign, g_DroneUptQueue.DroneRptQueue[wr_index].packet_len);
	   g_DroneUptQueue.packet_wr_index += 1;
	   pthread_mutex_unlock(&mutex);
	}
  pthread_mutex_unlock(&queue_mutex);
  ReleaseWrQueueLock();
  
	return;
}
/*******************************************************************************
* 函数名称: SendDroneToQueue
* 函数功能: 缓冲区发送
* 函数参数: 
* 参数名称: 	类型	    输入/输出	   描述
* out  			char*       输入	       缓冲区地址
* u32DataLength u32         输入           长度
* pdrone_model  char*       输入           model类型
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2025/7/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int SendDroneToQueue(int new_sockfd)
{
    int snd_index = 0;
	  int MaxBuf_Len = PACKET_BUFFER_LEN*sizeof(char);
	  int drone_model_len  = 0;
	  char *outdata = 0;
    u32 u32DataLength = 0;
	  ssize_t SendBufSize = 0;
	
    while(snd_index < MAX_UPREPORT_BUFFER_COUNT)
    {
        outdata = (char *)&(g_DroneUptQueue.DroneRptQueue[snd_index].packet_buffer);
        u32DataLength = g_DroneUptQueue.DroneRptQueue[snd_index].packet_len;
        
        if ((u32DataLength > 0) && (g_DroneUptQueue.DroneRptQueue[snd_index].packet_rcvsign == 1))
        {
           Dbg_Level_Print(LOG_INFO, "^_^cf[rd_index:%d][wr_index=%d][snd_index=%d][rcvsign=%d][packet_len=%d]\n", 
			        g_DroneUptQueue.packet_rd_index,
		        	g_DroneUptQueue.packet_wr_index, 
			        snd_index, 
			        g_DroneUptQueue.DroneRptQueue[snd_index].packet_rcvsign,
			        u32DataLength);
           for (int i = 0; i < MAX_JAVA_PLATFORM_NUM; i++)
           {
	           if (client_sockfd[i] != 0)
	           {
                SendBufSize = send(client_sockfd[i], outdata, u32DataLength, MSG_NOSIGNAL);
			          if(SendBufSize == 0)
			          {
				            Dbg_Level_Print(LOG_INFO, "TCP Client Have Closed, Waiting For Reconnect!");
				            Dbg_Level_Print(LOG_INFO, "########1-2-#########\n");
				            close(client_sockfd[i]);
                    client_sockfd[i] = 0;                                      
				            continue;
			          }
			          else if(SendBufSize < 0)
			          {
				            perror("TCP Send Data Failed, Please Client Reconnect!");
				            Dbg_Level_Print(LOG_INFO, "########1-3-#########\n");
				            close(client_sockfd[i]);
                    client_sockfd[i] = 0;                                   
				            break;
			          } 
                usleep(50);             
             }   
          }
          memset(&(g_DroneUptQueue.DroneRptQueue[snd_index]), 0, sizeof(g_DroneUptQueue.DroneRptQueue[snd_index]));
          Dbg_Level_Print(LOG_INFO, "^_^af[rd_index:%d][packet_rcvsign=%d][packet_len=%d]\n", snd_index, 
          g_DroneUptQueue.DroneRptQueue[snd_index].packet_rcvsign, g_DroneUptQueue.DroneRptQueue[snd_index].packet_len);
           g_DroneUptQueue.packet_rd_index = ((g_DroneUptQueue.packet_rd_index + 1)% MAX_UPREPORT_BUFFER_COUNT);  
          usleep(10);       
       }
       snd_index++;
	}

	return 0;
}
/*******************************************************************************
* 函数名称: GeneralDroneHandle
* 函数功能: 构造无人机探测结果
* 函数参数: 
* 参数名称: 	         类型	                  输入/输出	   描述
* pstruGeneralDroneInfo  T_RhhkGeneralDroneInfo	  输入	       通用无人机信息
* message                DJI_FLIGHT_INFO_Str      输入         来自识别库的基础信息
* pstruGpsInfoRpt        Drone_Detect_GpsInfo_Rpt 输入         外置GSP信息
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int GeneralDroneHandle(T_RhhkGeneralDroneInfo *pstruGeneralDroneInfo, DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt)
{
    s32 s32Result = OSP_ERROR;
    DJI_FLIGHT_INFO_Str *tDroneOutputData = (DJI_FLIGHT_INFO_Str *)message;
    DtStruct_ApiSet *pApiSet;	
    DtAdapt *dataNum = NULLPTR;
    char str[DRONE_ARG_STRING_LEN];	
    u32 u32NofPara = 0;
    u32 u32AlarmKeyPara[7];
    cJSON *root, *root_report, *fld, *array, *SceneInfo, *TestDesc, *SceneResult, *GapsInfo, *GapsResult;
	
    char *out, *out_report;
    u8 u8NextSceneNum = 0;
    u8 u8Index = 0; 
    u8 TestSceneIndex = 0;
    u8  TestCheckIndex = 0;
    u8  TestCaseReport = 0;
    u8  TestCaseFailStatistic = 0;
    u8  u8CounterIndex = 0;
    u16 u16MsgPoint = 0;
    u16 u16EventType =  0;
    u8 *pu8Data = NULLPTR;
    u32 u32DataLength = 0;
    time_t timep;	 
    struct tm *currentTime;
    u32 log_index = 0;
    /*Dji O4 Drone Type*/
    int judgeDroneType = 0;
    int indexDroneO4 = 0;
    double freq = 0;
    float  rssi = 0;
	  char *generalDroneSN;
    
    if ((message == NULLPTR) || (pstruGpsInfoRpt == NULLPTR) || (pstruGeneralDroneInfo == NULLPTR))
    {
        return OSP_ERROR;
    }

    pApiSet = DT_Adapt_Init();
    if(NULLPTR != pApiSet)
    {
       s32Result = CAPS_SetApi(pApiSet);
       if(OSP_OK != s32Result)
       {
           return OSP_ERROR;
       }    
    }

   
    freq = (double)(message->freq / 1e6);
    rssi = message->rssi;
    if (pstruGeneralDroneInfo->valid != 0)
    {
        Dbg_Level_Print(LOG_INFO,"######## 2025-02-28  general drone report start ########\n");
        Dbg_Level_Print(LOG_INFO,"pstruGeneralDroneInfo->valid:%d\n", pstruGeneralDroneInfo->valid);
        Dbg_Level_Print(LOG_INFO,"drone model:%s\n", pstruGeneralDroneInfo->arr_target_model);
        Dbg_Level_Print(LOG_INFO,"drone freq:%.1f\n", pstruGeneralDroneInfo->arr_target_freq);
        Dbg_Level_Print(LOG_INFO,"drone rssi:%.1f\n", pstruGeneralDroneInfo->arr_target_rssi);
        Dbg_Level_Print(LOG_INFO,"drone serial:%s\n", pstruGeneralDroneInfo->arr_target_serial);
	    memset(message, 0, sizeof(DJI_FLIGHT_INFO_Str));
        Dbg_Level_Print(LOG_INFO,"######## 2025-02-28  general drone report end ########\n");
    }

    Dbg_Level_Print(LOG_INFO, "1. Init (%s) ..., Genarl Drone Please Wait a Moment!\n", "GenerateJson");
    root= g_pApiSet->m_pApi_CreateObject();
    
    /* Our array of "records": */    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->packet_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "packet_type", dataNum); 
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->seq_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "seq_num", dataNum); 

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->state_info);
    g_pApiSet->m_pApi_AddItemToObject(root, "state_info", dataNum);


    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    g_pApiSet->m_pApi_AddItemToObject(root, "rdi_ssid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    generalDroneSN  = CreateGeneralDroneSN(pstruGeneralDroneInfo->arr_target_model, pstruGeneralDroneInfo->arr_target_freq, pstruGeneralDroneInfo->arr_target_rssi);
	if (generalDroneSN != NULL)
	{
        g_pApiSet->m_pApi_AddItemToObject(root, "drone_serial_num", g_pApiSet->m_pApi_CreateString((const char *)&pstruGeneralDroneInfo->arr_target_serial));
	}
	else
	{
        memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
        sprintf((char *)str, "%s", (const char *)pstruGeneralDroneInfo->arr_target_serial);                 
        g_pApiSet->m_pApi_AddItemToObject(root, "drone_serial_num", g_pApiSet->m_pApi_CreateString((const char *)&pstruGeneralDroneInfo->arr_target_serial));
	}

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->drone_longitude);          
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_longitude", dataNum);


    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->drone_latitude);           
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_latitude", dataNum);
  

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->altitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "altitude", dataNum);                          
    

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->height);
    g_pApiSet->m_pApi_AddItemToObject(root, "height", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->north_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "north_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->east_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "east_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->up_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "up_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pitch_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "pitch_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->roll_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "roll_angle", dataNum);
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->yaw_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "yaw_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->gpstime);
    g_pApiSet->m_pApi_AddItemToObject(root, "gpstime", dataNum);
		

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pilot_longitude);           
    g_pApiSet->m_pApi_AddItemToObject(root, "pilot_longitude", dataNum);                   
    

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pilot_latitude);           
    g_pApiSet->m_pApi_AddItemToObject(root, "pilot_latitude", dataNum);
   

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->home_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->home_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_altitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_altitude", dataNum);
    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->product_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "product_type", dataNum);
 
	g_pApiSet->m_pApi_AddItemToObject(root, "product_type_str", g_pApiSet->m_pApi_CreateString((const char *)&pstruGeneralDroneInfo->arr_target_model));
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->uuid_length);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid_length", dataNum);
 
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->uuid[0],tDroneOutputData->uuid[1],tDroneOutputData->uuid[2],
		    tDroneOutputData->uuid[3],tDroneOutputData->uuid[4],tDroneOutputData->uuid[5],tDroneOutputData->uuid[6],tDroneOutputData->uuid[7],
		    tDroneOutputData->uuid[8],tDroneOutputData->uuid[9],tDroneOutputData->uuid[10],tDroneOutputData->uuid[11],tDroneOutputData->uuid[12],
		    tDroneOutputData->uuid[13],tDroneOutputData->uuid[14],tDroneOutputData->uuid[15],tDroneOutputData->uuid[16],tDroneOutputData->uuid[17]);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->license[0],tDroneOutputData->license[1],tDroneOutputData->license[2],tDroneOutputData->license[3],
		    tDroneOutputData->license[4],tDroneOutputData->license[4],tDroneOutputData->license[6],tDroneOutputData->license[7],
		    tDroneOutputData->license[8],tDroneOutputData->license[9]);
    g_pApiSet->m_pApi_AddItemToObject(root, "license", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    g_pApiSet->m_pApi_AddItemToObject(root, "ssid", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_ssid));
    g_pApiSet->m_pApi_AddItemToObject(root, "vendor", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_vendor));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_destination_address[0], tDroneOutputData->wifi_destination_address[1],
		    tDroneOutputData->wifi_destination_address[2],tDroneOutputData->wifi_destination_address[3],tDroneOutputData->wifi_destination_address[4],
		    tDroneOutputData->wifi_destination_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_destination_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_source_address[0],tDroneOutputData->wifi_source_address[1],
		    tDroneOutputData->wifi_source_address[2],tDroneOutputData->wifi_source_address[3],tDroneOutputData->wifi_source_address[4],
		    tDroneOutputData->wifi_source_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_source_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_bss_id_address[0], tDroneOutputData->wifi_bss_id_address[1],
		    tDroneOutputData->wifi_bss_id_address[2],tDroneOutputData->wifi_bss_id_address[3],tDroneOutputData->wifi_bss_id_address[4],
		    tDroneOutputData->wifi_bss_id_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_bss_id_address", g_pApiSet->m_pApi_CreateString((const char *)&str));

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->efingerprint);
    g_pApiSet->m_pApi_AddItemToObject(root, "efingerprint", dataNum);


    dataNum = g_pApiSet->m_pApi_CreateNumber((double)(pstruGeneralDroneInfo->arr_target_freq));
    g_pApiSet->m_pApi_AddItemToObject(root, "freq", dataNum);
   	

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGeneralDroneInfo->arr_target_rssi);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->sync_corr);
    g_pApiSet->m_pApi_AddItemToObject(root, "sync_corr", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->bandwidth /1e6);
    g_pApiSet->m_pApi_AddItemToObject(root, "bandwidth", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->decode_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "decode_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->wifi_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->device_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "device_num", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->antenna_code);
    g_pApiSet->m_pApi_AddItemToObject(root, "antenna_code", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->_10ms_offset);
    g_pApiSet->m_pApi_AddItemToObject(root, "10ms_offset", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->protocal_version);
    g_pApiSet->m_pApi_AddItemToObject(root, "protocal_version", dataNum);
    
    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->UA_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "UA_type", dataNum);                   
    

    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)tDroneOutputData->drone_id);
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_id", g_pApiSet->m_pApi_CreateString((const char *)&str));

   
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->direction);
    g_pApiSet->m_pApi_AddItemToObject(root, "direction", dataNum);                
    

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "speed", dataNum);


    dataNum = g_pApiSet->m_pApi_CreateNumber(0.00);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Hspeed", dataNum);
    
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(0.00);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Vspeed", dataNum);
    
    /*rssi handle*/
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.freq);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_freq", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_drone);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_drone", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_max);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_max", dataNum);
	
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->dji_byte176);
    g_pApiSet->m_pApi_AddItemToObject(root, "dji_byte176", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->reserved);
    g_pApiSet->m_pApi_AddItemToObject(root, "reserved", g_pApiSet->m_pApi_CreateString((const char *)&str));

    out= cJSON_Print(root);     
    u32DataLength = strlen(out);
    
    //Send Json To JavaPlat
    CopyDroneToQueue(out, u32DataLength, pstruGeneralDroneInfo->arr_target_model);
    //CopyDroneDataPacket(out, u32DataLength, pstruGeneralDroneInfo->arr_target_model);
	  Dbg_Level_Print(LOG_INFO,"########:GeneralDroneHandle CopyDroneDataPacket ########\n");
    cJSON_Delete(root);    

    return OSP_OK;
}

/*******************************************************************************
* 函数名称: Judge_WhetherGeneralDrone
* 函数功能: 判断通用机型
* 函数参数: 
* 参数名称: 	         类型	                  输入/输出	   描述
* pstruDJIFlightInfo     DJI_FLIGHT_INFO_Str*	  输入	       通用无人机信息
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int Judge_WhetherGeneralDrone(DJI_FLIGHT_INFO_Str *pstruDJIFlightInfo)
{
    int index = 0;

    if (pstruDJIFlightInfo == NULL)
    {
        return -1;
    }

    if (!(pstruDJIFlightInfo->decode_flag) && 
		((pstruDJIFlightInfo->packet_type == PACKET_VEDIO_LightBrdige_OTHERS) || 
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_DAOTONG) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_Yuneec_H52e) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_HARBOSEN) ||
	   	 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_ANALOG) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_WIFI_PARROT) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_WIFI_POWEREGG) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_WIFI_SJF11) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_ENHANCED_WIFI) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_DATALINK_P900) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_FEIMI) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_Avatar) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_ZONGHENG) ||
		 (pstruDJIFlightInfo->packet_type == PACKET_HAIKAN_WEISHI) ||
	     (pstruDJIFlightInfo->packet_type == PACKET_VEDIO_LTE_TYPE)))
    {
        return 1;
    }
    else
    {
        return 0;
    }
}
/*******************************************************************************
* 函数名称: DjiDroneHandle
* 函数功能: 大疆无人机报文构造
* 函数参数: 
* 参数名称: 	         类型	                  输入/输出	   描述
* dji_proxy              int                      输入         类型识别参数
* pstruDjiDroneInfo      T_RhhkDjiDroneInfo	      输入	       通用无人机信息
* message                DJI_FLIGHT_INFO_Str      输入         来自识别库的基础信息
* pstruGpsInfoRpt        Drone_Detect_GpsInfo_Rpt 输入         外置GSP信息
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int DjiDroneHandle(int dji_proxy, T_RhhkDjiDroneInfo *pstruDjiDroneInfo, DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt)
{
    s32 s32Result = OSP_ERROR;
    DJI_FLIGHT_INFO_Str *tDroneOutputData = (DJI_FLIGHT_INFO_Str *)message;
    DtStruct_ApiSet *pApiSet;	
    DtAdapt *dataNum = NULLPTR;
    char str[DRONE_ARG_STRING_LEN];	
    u32 u32NofPara = 0;
    u32 u32AlarmKeyPara[7];
    cJSON *root, *root_report, *fld, *array, *SceneInfo, *TestDesc, *SceneResult, *GapsInfo, *GapsResult;
	
    char *out, *out_report;
    u8 u8NextSceneNum = 0;
    u8 u8Index = 0; 
    u8 TestSceneIndex = 0;
    u8  TestCheckIndex = 0;
    u8  TestCaseReport = 0;
    u8  TestCaseFailStatistic = 0;
    u8  u8CounterIndex = 0;
    u16 u16MsgPoint = 0;
    u16 u16EventType =  0;
    u8 *pu8Data = NULLPTR;
    u32 u32DataLength = 0;
    time_t timep;	 
    struct tm *currentTime;
    u32 log_index = 0;
    /*Dji O4 Drone Type*/
    int judgeDroneType = 0;
    int indexDroneO4 = 0;
    double freq = 0;
    float  rssi = 0;
	char *generalDroneSN;
    
    if ((message == NULLPTR) || (pstruGpsInfoRpt == NULLPTR) || (pstruDjiDroneInfo == NULLPTR))
    {
        return OSP_ERROR;
    }

    pApiSet = DT_Adapt_Init();
    if(NULLPTR != pApiSet)
    {
       s32Result = CAPS_SetApi(pApiSet);
       if(OSP_OK != s32Result)
       {
           return OSP_ERROR;
       }    
    }

    if (dji_proxy == DIJ_DRONE_O3_DOWN_LEVEL)
    {
        Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]######## 2025-03-03 o3 dji drone report start ########\n");
        Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]pstruDjiDroneInfo->valid:%d\n", pstruDjiDroneInfo->valid);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone seq_num:%d\n", pstruDjiDroneInfo->seq_num);
        Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone device_num:%d\n", pstruDjiDroneInfo->device_num);
        Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone serial:%s\n", pstruDjiDroneInfo->drone_serial_num);
        Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone model:%s\n", pstruDjiDroneInfo->product_type_str);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone freq:%.1f\n", pstruDjiDroneInfo->freq);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone rssi:%.1f\n", pstruDjiDroneInfo->rssi);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone height:%.1f\n", pstruDjiDroneInfo->height);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone altitude:%.1f\n", pstruDjiDroneInfo->altitude);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone distance:%.1f\n", pstruDjiDroneInfo->distance);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone northV:%.1f\n", pstruDjiDroneInfo->north_speed);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone eastV:%.1f\n", pstruDjiDroneInfo->east_speed);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone upV:%.1f\n", pstruDjiDroneInfo->up_speed);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone drone_latitude:%.1f\n", pstruDjiDroneInfo->drone_latitude);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone drone_longitude:%.1f\n", pstruDjiDroneInfo->drone_longitude);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone pilot_latitude:%.1f\n", pstruDjiDroneInfo->pilot_latitude);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone pilot_longitude:%.1f\n", pstruDjiDroneInfo->pilot_longitude);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone home_latitude:%.1f\n", pstruDjiDroneInfo->home_latitude);
		Dbg_Level_Print(LOG_KEY,"[KEY-MAKE-O3]dji drone home_longitude:%.1f\n", pstruDjiDroneInfo->home_longitude);
	    memset(message, 0, sizeof(DJI_FLIGHT_INFO_Str));
        Dbg_Level_Print(LOG_INFO,"######## 2025-03-03  general drone report end ########\n");
    }

    Dbg_Level_Print(LOG_INFO, "1. Init (%s) ..., Genarl Drone Please Wait a Moment!\n", "GenerateJson");
    root= g_pApiSet->m_pApi_CreateObject();
    
    /* Our array of "records": */    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->packet_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "packet_type", dataNum); 
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->seq_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "seq_num", dataNum); 

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->state_info);
    g_pApiSet->m_pApi_AddItemToObject(root, "state_info", dataNum);


    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    g_pApiSet->m_pApi_AddItemToObject(root, "rdi_ssid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)pstruDjiDroneInfo->drone_serial_num);                 
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_serial_num", g_pApiSet->m_pApi_CreateString((const char *)&str));

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->drone_longitude);          
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_longitude", dataNum);


    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->drone_latitude);           
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_latitude", dataNum);
  

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->altitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "altitude", dataNum);                          
    

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->height);
    g_pApiSet->m_pApi_AddItemToObject(root, "height", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->north_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "north_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->east_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "east_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->up_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "up_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pitch_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "pitch_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->roll_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "roll_angle", dataNum);
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->yaw_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "yaw_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->gpstime);
    g_pApiSet->m_pApi_AddItemToObject(root, "gpstime", dataNum);
		

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->pilot_longitude);           
    g_pApiSet->m_pApi_AddItemToObject(root, "pilot_longitude", dataNum);                   
    

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->pilot_latitude);           
    g_pApiSet->m_pApi_AddItemToObject(root, "pilot_latitude", dataNum);
   

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->home_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->home_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_altitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_altitude", dataNum);
    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->product_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "product_type", dataNum);
 
	g_pApiSet->m_pApi_AddItemToObject(root, "product_type_str", g_pApiSet->m_pApi_CreateString((const char *)&pstruDjiDroneInfo->product_type_str));
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->uuid_length);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid_length", dataNum);
 
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->uuid[0],tDroneOutputData->uuid[1],tDroneOutputData->uuid[2],
		    tDroneOutputData->uuid[3],tDroneOutputData->uuid[4],tDroneOutputData->uuid[5],tDroneOutputData->uuid[6],tDroneOutputData->uuid[7],
		    tDroneOutputData->uuid[8],tDroneOutputData->uuid[9],tDroneOutputData->uuid[10],tDroneOutputData->uuid[11],tDroneOutputData->uuid[12],
		    tDroneOutputData->uuid[13],tDroneOutputData->uuid[14],tDroneOutputData->uuid[15],tDroneOutputData->uuid[16],tDroneOutputData->uuid[17]);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->license[0],tDroneOutputData->license[1],tDroneOutputData->license[2],tDroneOutputData->license[3],
		    tDroneOutputData->license[4],tDroneOutputData->license[4],tDroneOutputData->license[6],tDroneOutputData->license[7],
		    tDroneOutputData->license[8],tDroneOutputData->license[9]);
    g_pApiSet->m_pApi_AddItemToObject(root, "license", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    g_pApiSet->m_pApi_AddItemToObject(root, "ssid", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_ssid));
    g_pApiSet->m_pApi_AddItemToObject(root, "vendor", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_vendor));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_destination_address[0], tDroneOutputData->wifi_destination_address[1],
		    tDroneOutputData->wifi_destination_address[2],tDroneOutputData->wifi_destination_address[3],tDroneOutputData->wifi_destination_address[4],
		    tDroneOutputData->wifi_destination_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_destination_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_source_address[0],tDroneOutputData->wifi_source_address[1],
		    tDroneOutputData->wifi_source_address[2],tDroneOutputData->wifi_source_address[3],tDroneOutputData->wifi_source_address[4],
		    tDroneOutputData->wifi_source_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_source_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_bss_id_address[0], tDroneOutputData->wifi_bss_id_address[1],
		    tDroneOutputData->wifi_bss_id_address[2],tDroneOutputData->wifi_bss_id_address[3],tDroneOutputData->wifi_bss_id_address[4],
		    tDroneOutputData->wifi_bss_id_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_bss_id_address", g_pApiSet->m_pApi_CreateString((const char *)&str));

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->efingerprint);
    g_pApiSet->m_pApi_AddItemToObject(root, "efingerprint", dataNum);


    dataNum = g_pApiSet->m_pApi_CreateNumber((double)(pstruDjiDroneInfo->freq));
    g_pApiSet->m_pApi_AddItemToObject(root, "freq", dataNum);
   	

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->rssi);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->sync_corr);
    g_pApiSet->m_pApi_AddItemToObject(root, "sync_corr", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->bandwidth /1e6);
    g_pApiSet->m_pApi_AddItemToObject(root, "bandwidth", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->decode_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "decode_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->wifi_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruDjiDroneInfo->device_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "device_num", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->antenna_code);
    g_pApiSet->m_pApi_AddItemToObject(root, "antenna_code", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->_10ms_offset);
    g_pApiSet->m_pApi_AddItemToObject(root, "10ms_offset", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->protocal_version);
    g_pApiSet->m_pApi_AddItemToObject(root, "protocal_version", dataNum);
    
    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->UA_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "UA_type", dataNum);                   
    

    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)tDroneOutputData->drone_id);
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_id", g_pApiSet->m_pApi_CreateString((const char *)&str));

   
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->direction);
    g_pApiSet->m_pApi_AddItemToObject(root, "direction", dataNum);                
    

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "speed", dataNum);


    dataNum = g_pApiSet->m_pApi_CreateNumber(0.00);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Hspeed", dataNum);
    
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(0.00);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Vspeed", dataNum);
    
    /*rssi handle*/
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.freq);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_freq", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_drone);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_drone", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_max);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_max", dataNum);
	
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->dji_byte176);
    g_pApiSet->m_pApi_AddItemToObject(root, "dji_byte176", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->reserved);
    g_pApiSet->m_pApi_AddItemToObject(root, "reserved", g_pApiSet->m_pApi_CreateString((const char *)&str));

    out= cJSON_Print(root);     
    u32DataLength = strlen(out);

	// Filter invalid data
    const char *dj04_target = "\"0-\"";
    if(strstr(out, dj04_target)!= NULL)
    {
       memset(out, 0, u32DataLength);
       cJSON_Delete(root);
       return 0; 
    }

    //Send Json To JavaPlat
    CopyDroneToQueue(out, u32DataLength, pstruDjiDroneInfo->product_type_str);
    //CopyDroneDataPacket(out, u32DataLength, pstruDjiDroneInfo->product_type_str);
	  Dbg_Level_Print(LOG_INFO,"########:DjiDroneHandle CopyDroneDataPacket ########\n");
    cJSON_Delete(root);    

    return OSP_OK;
}
/*******************************************************************************
* 函数名称: DjiO4DroneHandle
* 函数功能: 大疆O4无人机报文构造
* 函数参数: 
* 参数名称: 	         类型	                  输入/输出	   描述
* dji_proxy              int                      输入         类型识别参数
* pstruDjiDroneInfo      T_RhhkDjiDroneInfo	      输入	       通用无人机信息
* message                DJI_FLIGHT_INFO_Str      输入         来自识别库的基础信息
* pstruGpsInfoRpt        Drone_Detect_GpsInfo_Rpt 输入         外置GSP信息
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int DjiO4DroneHandle(int dji_proxy, T_RhhkDjiAddInfo *pstrDjiO4AddInfo, DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt)
{
    s32 s32Result = OSP_ERROR;
    DJI_FLIGHT_INFO_Str *tDroneOutputData = (DJI_FLIGHT_INFO_Str *)message;
    DtStruct_ApiSet *pApiSet;	
    DtAdapt *dataNum = NULLPTR;
    char str[DRONE_ARG_STRING_LEN];	
    u32 u32NofPara = 0;
    u32 u32AlarmKeyPara[7];
    cJSON *root, *root_report, *fld, *array, *SceneInfo, *TestDesc, *SceneResult, *GapsInfo, *GapsResult;
	
    char *out, *out_report;
    u8 u8NextSceneNum = 0;
    u8 u8Index = 0; 
    u8 TestSceneIndex = 0;
    u8  TestCheckIndex = 0;
    u8  TestCaseReport = 0;
    u8  TestCaseFailStatistic = 0;
    u8  u8CounterIndex = 0;
    char ReportName[CAPS_SCENE_NAME_BYTE_COUNT] = {0};
    char ReportDesc[CAPS_SCENE_NAME_BYTE_COUNT] = {0};
    char ReportItem[CAPS_SCENE_NAME_BYTE_COUNT] = {0};
    u16 u16MsgPoint = 0;
    u16 u16EventType =  0;
    u8 *pu8Data = NULLPTR;
    u32 u32DataLength = 0;
    time_t timep;	 
    struct tm *currentTime;
    u32 log_index = 0;
    /*Dji O4 Drone Type*/
    int judgeDroneType = 0;
    int indexDroneO4 = 0;
    double freq = 0;
    float  rssi = 0;
	  uint8_t temp_serial_num[30] = "88dbc66bed";
    uint8_t DjiO4Sn[16]; 

    if ((message == NULLPTR) || (pstruGpsInfoRpt == NULLPTR) || (pstrDjiO4AddInfo == NULLPTR))
    {
        return OSP_ERROR;
    }

    pApiSet = DT_Adapt_Init();
    if(NULLPTR != pApiSet)
    {
       s32Result = CAPS_SetApi(pApiSet);
       if(OSP_OK != s32Result)
       {
           return OSP_ERROR;
       }    
    }

    /*Judge Drone O4*/
    while (g_rid_index < DJI_O4_RDI_MAX_COUNT)
    {
        if (g_RhhkReadRdiO4Info[g_rid_index].product_type_str != NULL)
        {
            indexDroneO4 = g_rid_index;
	          break;
        }
        g_rid_index++;
    }

    if (g_rid_index >= DJI_O4_RDI_MAX_COUNT)
    {
       g_rid_index = 0;
    }
    
    freq = (float)(pstrDjiO4AddInfo->freq);
    rssi = (float)pstrDjiO4AddInfo->rssi;
    if (dji_proxy == DJI_DRONE_O4_UP_LEVEL)
    {
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]######## 2025-02-14  rdi o4 report start ########\n");
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]UpLayer-index:%d\n", indexDroneO4);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]rdi_ssid:%s\n", g_RhhkReadRdiO4Info[indexDroneO4].rdi_ssid);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]drone_serial_num:%s\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_serial_num);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]product_type_str:%s\n", g_RhhkReadRdiO4Info[indexDroneO4].product_type_str);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]UA_type:%d\n", g_RhhkReadRdiO4Info[indexDroneO4].UA_type);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]drone_longtitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_longtitude);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]drone_latitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_latitude);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]pilot_longtitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].pilot_longtitude);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]pilot_latitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].pilot_latitude);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]drone_Hspeed:%f\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_Hspeed);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]drone_Vspeed:%f\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_Vspeed);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]direction:%d\n", g_RhhkReadRdiO4Info[indexDroneO4].direction);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]Altitude:%f\n", g_RhhkReadRdiO4Info[indexDroneO4].altitude);
        Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]freq:%.1f\n", freq);
	    memset(message, 0, sizeof(DJI_FLIGHT_INFO_Str));
        Dbg_Level_Print(LOG_INFO, "######## 2025-02-14   rdi report end ########\n");
    }

    Dbg_Level_Print(LOG_INFO, "1. Init (%s) ..., DJI Drone Please Wait a Moment!\n", "GenerateJson");
    root= g_pApiSet->m_pApi_CreateObject();

    //push,only when there are two consecutive empty data
    if ((0 == strlen(g_RhhkReadRdiO4Info[indexDroneO4].product_type_str)) && (RdiOrOnlineOkStopVirtual == 0))
    {
         if (O4_count == 2)
         {
             sprintf(g_RhhkReadRdiO4Info[indexDroneO4].product_type_str, "%s", "Dji_module(O4)");
			       memcpy(g_RhhkReadRdiO4Info[indexDroneO4].drone_serial_num, temp_serial_num,sizeof(temp_serial_num));
			       O4_count = 0;
		     }
		     O4_count++;
	}
	else
	{
        O4_count = 0;
        RdiOrOnlineOkStopVirtual = 2; //rdi gather success
	}
	
    /* Our array of "records": */    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->packet_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "packet_type", dataNum); 
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->seq_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "seq_num", dataNum); 

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->state_info);
    g_pApiSet->m_pApi_AddItemToObject(root, "state_info", dataNum);

    //add----0
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)g_RhhkReadRdiO4Info[indexDroneO4].rdi_ssid); 
    g_pApiSet->m_pApi_AddItemToObject(root, "rdi_ssid", g_pApiSet->m_pApi_CreateString((const char *)&str));
   

     //modify----1
 	memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
	sprintf((char *)str, "%s", (const char *)g_RhhkReadRdiO4Info[indexDroneO4].drone_serial_num); 				
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_serial_num", g_pApiSet->m_pApi_CreateString((const char *)&str));

    //modify----4
 	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_longtitude);		  
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_longitude", dataNum);

    //modify----5
	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_latitude); 		  
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_latitude", dataNum);

     //modify----11
	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].altitude);
	g_pApiSet->m_pApi_AddItemToObject(root, "altitude", dataNum);						   

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->height);
    g_pApiSet->m_pApi_AddItemToObject(root, "height", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->north_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "north_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->east_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "east_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->up_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "up_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pitch_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "pitch_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->roll_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "roll_angle", dataNum);
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->yaw_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "yaw_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->gpstime);
    g_pApiSet->m_pApi_AddItemToObject(root, "gpstime", dataNum);
		
    //modify----6
	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].pilot_longtitude);		   
	g_pApiSet->m_pApi_AddItemToObject(root, "pilot_longitude", dataNum);				   

    //modify----7
  	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].pilot_latitude); 		  
	g_pApiSet->m_pApi_AddItemToObject(root, "pilot_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->home_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->home_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_altitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_altitude", dataNum);
    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->product_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "product_type", dataNum);
 
    //modify----2
 	memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
	sprintf((char *)str, "%s", (const char *)g_RhhkReadRdiO4Info[indexDroneO4].product_type_str); 				
    g_pApiSet->m_pApi_AddItemToObject(root, "product_type_str", g_pApiSet->m_pApi_CreateString((const char *)&str)); 

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->uuid_length);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid_length", dataNum);
 
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->uuid[0],tDroneOutputData->uuid[1],tDroneOutputData->uuid[2],
		    tDroneOutputData->uuid[3],tDroneOutputData->uuid[4],tDroneOutputData->uuid[5],tDroneOutputData->uuid[6],tDroneOutputData->uuid[7],
		    tDroneOutputData->uuid[8],tDroneOutputData->uuid[9],tDroneOutputData->uuid[10],tDroneOutputData->uuid[11],tDroneOutputData->uuid[12],
		    tDroneOutputData->uuid[13],tDroneOutputData->uuid[14],tDroneOutputData->uuid[15],tDroneOutputData->uuid[16],tDroneOutputData->uuid[17]);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->license[0],tDroneOutputData->license[1],tDroneOutputData->license[2],tDroneOutputData->license[3],
		    tDroneOutputData->license[4],tDroneOutputData->license[4],tDroneOutputData->license[6],tDroneOutputData->license[7],
		    tDroneOutputData->license[8],tDroneOutputData->license[9]);
    g_pApiSet->m_pApi_AddItemToObject(root, "license", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    g_pApiSet->m_pApi_AddItemToObject(root, "ssid", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_ssid));
    g_pApiSet->m_pApi_AddItemToObject(root, "vendor", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_vendor));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_destination_address[0], tDroneOutputData->wifi_destination_address[1],
		    tDroneOutputData->wifi_destination_address[2],tDroneOutputData->wifi_destination_address[3],tDroneOutputData->wifi_destination_address[4],
		    tDroneOutputData->wifi_destination_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_destination_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_source_address[0],tDroneOutputData->wifi_source_address[1],
		    tDroneOutputData->wifi_source_address[2],tDroneOutputData->wifi_source_address[3],tDroneOutputData->wifi_source_address[4],
		    tDroneOutputData->wifi_source_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_source_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_bss_id_address[0], tDroneOutputData->wifi_bss_id_address[1],
		    tDroneOutputData->wifi_bss_id_address[2],tDroneOutputData->wifi_bss_id_address[3],tDroneOutputData->wifi_bss_id_address[4],
		    tDroneOutputData->wifi_bss_id_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_bss_id_address", g_pApiSet->m_pApi_CreateString((const char *)&str));

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->efingerprint);
    g_pApiSet->m_pApi_AddItemToObject(root, "efingerprint", dataNum);

    //modify----12
    dataNum = g_pApiSet->m_pApi_CreateNumber((double)freq);
    g_pApiSet->m_pApi_AddItemToObject(root, "freq", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->rssi);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->sync_corr);
    g_pApiSet->m_pApi_AddItemToObject(root, "sync_corr", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->bandwidth / 1e6);
    g_pApiSet->m_pApi_AddItemToObject(root, "bandwidth", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->decode_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "decode_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->wifi_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->device_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "device_num", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->antenna_code);
    g_pApiSet->m_pApi_AddItemToObject(root, "antenna_code", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->_10ms_offset);
    g_pApiSet->m_pApi_AddItemToObject(root, "10ms_offset", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->protocal_version);
    g_pApiSet->m_pApi_AddItemToObject(root, "protocal_version", dataNum);
    
    //modify----3
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].UA_type);
	g_pApiSet->m_pApi_AddItemToObject(root, "UA_type", dataNum);				   

    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)tDroneOutputData->drone_id);
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_id", g_pApiSet->m_pApi_CreateString((const char *)&str));

    //modify----10
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].direction);
    g_pApiSet->m_pApi_AddItemToObject(root, "direction", dataNum);                

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "speed", dataNum);

    //add----8
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_Hspeed);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Hspeed", dataNum);
	
    //add----9
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_Vspeed);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Vspeed", dataNum);

    /*rssi handle*/
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.freq);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_freq", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_drone);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_drone", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_max);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_max", dataNum);
	
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->dji_byte176);
    g_pApiSet->m_pApi_AddItemToObject(root, "dji_byte176", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->reserved);
    g_pApiSet->m_pApi_AddItemToObject(root, "reserved", g_pApiSet->m_pApi_CreateString((const char *)&str));

    //clear O4 data struct
	memset(&g_RhhkReadRdiO4Info[indexDroneO4], 0, sizeof(T_RHHKReadRdiO4Info));

    out= cJSON_Print(root);     
    u32DataLength = strlen(out);
    
    //Send Json To JavaPlat
    CopyDroneToQueue(out, u32DataLength, g_RhhkReadRdiO4Info[indexDroneO4].product_type_str);
    //CopyDroneDataPacket(out, u32DataLength, g_RhhkReadRdiO4Info[indexDroneO4].product_type_str);
	  Dbg_Level_Print(LOG_INFO,"########:DjiO4DroneHandle CopyDroneDataPacket ########\n");
    cJSON_Delete(root); 
      
	  memset(DjiO4Sn, 0, sizeof(DjiO4Sn));
	  memcpy(DjiO4Sn, &(g_RhhkReadRdiO4Info[indexDroneO4].drone_serial_num[4]), sizeof(DjiO4Sn));
	  Rhhk_RecordDroneTolib(DjiO4Sn);
   
    return OSP_OK;
}

/*******************************************************************************
* 函数名称: DjiOnlineDecryptedHandle
* 函数功能: 大疆O4无人机报文构造
* 函数参数: 
* 参数名称: 	         类型	                  输入/输出	   描述
* dji_proxy              int                      输入         类型识别参数
* pstruDjiDroneInfo      T_RhhkDjiDroneInfo	      通用无人机信息
* message                DJI_FLIGHT_INFO_Str      输入         来自识别库的基础信息
* pstruGpsInfoRpt        Drone_Detect_GpsInfo_Rpt 输入         外置GSP信息
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2025/06/20  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int DjiOnlineDecryptedHandle(int dji_proxy, T_RhhkDjiAddInfo *pstrDjiO4AddInfo, DJI_FLIGHT_INFO_Str *message, T_RhhkDecryptDjiDroneInfo *pDecryptedDjiDroneInfo, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt)
{
    s32 s32Result = OSP_ERROR;
    DJI_FLIGHT_INFO_Str *tDroneOutputData = (DJI_FLIGHT_INFO_Str *)message;
    DtStruct_ApiSet *pApiSet;	
    DtAdapt *dataNum = NULLPTR;
    char str[DRONE_ARG_STRING_LEN];	
    cJSON *root;
	
    char *out;
    u32 u32DataLength = 0;
    time_t timep;	 
    long gpstime_second;
    struct tm *currentTime;
    u32 log_index = 0;
    /*Dji O4 Drone Type*/
    int judgeDroneType = 0;
    int indexDroneO4 = 0;
    double freq = 0;
    float  rssi = 0;
	  
    char argbuffer[65];
    if ((message == NULLPTR) || (pstruGpsInfoRpt == NULLPTR) || (pstrDjiO4AddInfo == NULLPTR) || (pDecryptedDjiDroneInfo == NULL))
    {
        return OSP_ERROR;
    }
    
    pApiSet = DT_Adapt_Init();
    if(NULLPTR != pApiSet)
    {
       s32Result = CAPS_SetApi(pApiSet);
       if(OSP_OK != s32Result)
       {
           return OSP_ERROR;
       }    
    }
    //printf("2%%%%%%%%%%%%%%%%%%%%%%%%%%%%%\n");
    freq = (float)(pstrDjiO4AddInfo->freq);
    rssi = (float)pstrDjiO4AddInfo->rssi;
    if (dji_proxy == DJI_DRONE_O4_UP_LEVEL)
    {
        Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]######## 2025-06-20  rdi o4 report start ########\n");
        Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]drone_serial_num:%s\n", Trans_32BytesToString(pDecryptedDjiDroneInfo->drone_serial_num, argbuffer, sizeof(argbuffer)));
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]drone_uuid:%s\n", Trans_32BytesToString(pDecryptedDjiDroneInfo->drone_uuid, argbuffer, sizeof(argbuffer)));

		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]product_type:%d\n", pDecryptedDjiDroneInfo->product_type);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]drone_longitude:%lf\n", pDecryptedDjiDroneInfo->drone_longitude);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]drone_latitude:%lf\n", pDecryptedDjiDroneInfo->drone_latitude);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]drone_altitude:%f\n", pDecryptedDjiDroneInfo->drone_altitude);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]drone_height:%f\n", pDecryptedDjiDroneInfo->drone_height);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]east_speed:%f\n", pDecryptedDjiDroneInfo->east_speed);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]north_speed:%f\n", pDecryptedDjiDroneInfo->north_speed);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]up_speed:%f\n", pDecryptedDjiDroneInfo->up_speed);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]drone_yaw:%f\n", pDecryptedDjiDroneInfo->drone_yaw);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]gpstime:%s\n", pDecryptedDjiDroneInfo->gpstime);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]pilot_longitude:%f\n", pDecryptedDjiDroneInfo->pilot_longitude);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]pilot_latitude:%lf\n", pDecryptedDjiDroneInfo->pilot_latitude);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]home_longitude:%lf\n", pDecryptedDjiDroneInfo->home_longitude);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]home_latitude:%lf\n", pDecryptedDjiDroneInfo->home_latitude);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]product_type_str:%s\n", pDecryptedDjiDroneInfo->product_type_str);
        Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]freq:%.1f\n", freq);
		    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]rssi:%.1f\n", freq);
        memset(message, 0, sizeof(DJI_FLIGHT_INFO_Str));
        Dbg_Level_Print(LOG_INFO, "######## 2025-06-20   rdi report end ########\n");
    }

    Dbg_Level_Print(LOG_INFO, "1. Init (%s) ..., DJI Drone Please Wait a Moment!\n", "GenerateJson");
    root= g_pApiSet->m_pApi_CreateObject();
	
    /* Our array of "records": */    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->packet_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "packet_type", dataNum); 
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->seq_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "seq_num", dataNum); 

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->state_info);
    g_pApiSet->m_pApi_AddItemToObject(root, "state_info", dataNum);

    //add----0
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)"null"); 
    g_pApiSet->m_pApi_AddItemToObject(root, "rdi_ssid", g_pApiSet->m_pApi_CreateString((const char *)&str));
   

     //modify----1
 	memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
	sprintf((char *)str, "%s", (const char *)pDecryptedDjiDroneInfo->drone_serial_num); 				
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_serial_num", g_pApiSet->m_pApi_CreateString((const char *)&str));

    //modify----4
 	dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->drone_longitude);		  
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_longitude", dataNum);

    //modify----5
	dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->drone_latitude); 		  
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_latitude", dataNum);

     //modify----11
	dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->drone_altitude);
	g_pApiSet->m_pApi_AddItemToObject(root, "altitude", dataNum);						   

    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->drone_height);
    g_pApiSet->m_pApi_AddItemToObject(root, "height", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->north_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "north_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->east_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "east_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->up_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "up_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pitch_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "pitch_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->roll_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "roll_angle", dataNum);
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->drone_yaw);
    g_pApiSet->m_pApi_AddItemToObject(root, "yaw_angle", dataNum);

    gpstime_second = Rhhk_TransTimeToString((const char *)pDecryptedDjiDroneInfo->gpstime);
	  g_pApiSet->m_pApi_AddItemToObject(root, "gpstime", g_pApiSet->m_pApi_CreateNumber(gpstime_second));
		
    //modify----6
	dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->pilot_longitude);		   
	g_pApiSet->m_pApi_AddItemToObject(root, "pilot_longitude", dataNum);				   

    //modify----7
  	dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->pilot_latitude); 		  
	g_pApiSet->m_pApi_AddItemToObject(root, "pilot_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->home_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->home_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_altitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_altitude", dataNum);
    
    dataNum = g_pApiSet->m_pApi_CreateNumber(pDecryptedDjiDroneInfo->product_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "product_type", dataNum);
 
    //modify----2
 	memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
	sprintf((char *)str, "%s", (const char *)pDecryptedDjiDroneInfo->product_type_str); 				
    g_pApiSet->m_pApi_AddItemToObject(root, "product_type_str", g_pApiSet->m_pApi_CreateString((const char *)&str)); 

    dataNum = g_pApiSet->m_pApi_CreateNumber(strlen(pDecryptedDjiDroneInfo->drone_uuid));
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid_length", dataNum);
 
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
	sprintf((char *)str, "%s", (const char *)pDecryptedDjiDroneInfo->drone_uuid);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->license[0],tDroneOutputData->license[1],tDroneOutputData->license[2],tDroneOutputData->license[3],
		    tDroneOutputData->license[4],tDroneOutputData->license[4],tDroneOutputData->license[6],tDroneOutputData->license[7],
		    tDroneOutputData->license[8],tDroneOutputData->license[9]);
    g_pApiSet->m_pApi_AddItemToObject(root, "license", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    g_pApiSet->m_pApi_AddItemToObject(root, "ssid", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_ssid));
    g_pApiSet->m_pApi_AddItemToObject(root, "vendor", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_vendor));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_destination_address[0], tDroneOutputData->wifi_destination_address[1],
		    tDroneOutputData->wifi_destination_address[2],tDroneOutputData->wifi_destination_address[3],tDroneOutputData->wifi_destination_address[4],
		    tDroneOutputData->wifi_destination_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_destination_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_source_address[0],tDroneOutputData->wifi_source_address[1],
		    tDroneOutputData->wifi_source_address[2],tDroneOutputData->wifi_source_address[3],tDroneOutputData->wifi_source_address[4],
		    tDroneOutputData->wifi_source_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_source_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_bss_id_address[0], tDroneOutputData->wifi_bss_id_address[1],
		    tDroneOutputData->wifi_bss_id_address[2],tDroneOutputData->wifi_bss_id_address[3],tDroneOutputData->wifi_bss_id_address[4],
		    tDroneOutputData->wifi_bss_id_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_bss_id_address", g_pApiSet->m_pApi_CreateString((const char *)&str));

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->efingerprint);
    g_pApiSet->m_pApi_AddItemToObject(root, "efingerprint", dataNum);

    //modify----12
    dataNum = g_pApiSet->m_pApi_CreateNumber((double)freq);
    g_pApiSet->m_pApi_AddItemToObject(root, "freq", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->rssi);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->sync_corr);
    g_pApiSet->m_pApi_AddItemToObject(root, "sync_corr", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->bandwidth / 1e6);
    g_pApiSet->m_pApi_AddItemToObject(root, "bandwidth", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->decode_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "decode_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->wifi_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->device_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "device_num", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->antenna_code);
    g_pApiSet->m_pApi_AddItemToObject(root, "antenna_code", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->_10ms_offset);
    g_pApiSet->m_pApi_AddItemToObject(root, "10ms_offset", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->protocal_version);
    g_pApiSet->m_pApi_AddItemToObject(root, "protocal_version", dataNum);
    
    //modify----3
    dataNum = g_pApiSet->m_pApi_CreateNumber(0);
	g_pApiSet->m_pApi_AddItemToObject(root, "UA_type", dataNum);				   

    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)tDroneOutputData->drone_id);
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_id", g_pApiSet->m_pApi_CreateString((const char *)&str));

    //modify----10
    dataNum = g_pApiSet->m_pApi_CreateNumber(0);
    g_pApiSet->m_pApi_AddItemToObject(root, "direction", dataNum);                

    dataNum = g_pApiSet->m_pApi_CreateNumber(0);
    g_pApiSet->m_pApi_AddItemToObject(root, "speed", dataNum);

    //add----8
    dataNum = g_pApiSet->m_pApi_CreateNumber(0);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Hspeed", dataNum);
	
    //add----9
    dataNum = g_pApiSet->m_pApi_CreateNumber(0);      
    g_pApiSet->m_pApi_AddItemToObject(root, "Vspeed", dataNum);

    /*rssi handle*/
    dataNum = g_pApiSet->m_pApi_CreateNumber(0);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_freq", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(0);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_drone", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(0);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(0);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_max", dataNum);
	
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->dji_byte176);
    g_pApiSet->m_pApi_AddItemToObject(root, "dji_byte176", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->reserved);
    g_pApiSet->m_pApi_AddItemToObject(root, "reserved", g_pApiSet->m_pApi_CreateString((const char *)&str));

    out= cJSON_Print(root);     
    u32DataLength = strlen(out);
    
    //Send Json To JavaPlat
    CopyDroneToQueue(out, u32DataLength, pDecryptedDjiDroneInfo->product_type_str);
    //CopyDroneDataPacket(out, u32DataLength, pDecryptedDjiDroneInfo->product_type_str);
	  Dbg_Level_Print(LOG_INFO,"########:DjiO4DroneHandle CopyDroneDataPacket ########\n");
    cJSON_Delete(root); 
    RdiOrOnlineOkStopVirtual = 1;  //online gather success   

    return OSP_OK;
}


/*Generate Test Report*/
/*******************************************************************************
* 函数名称: TransformDroneData
* 函数功能: 基于底层功能库的探测信息处理
* 函数参数: 
* 参数名称: 	         类型	                  输入/输出	   描述
* g_u32IfRecordSwitch	 u32                      输入	       通用无人机信息
* message                DJI_FLIGHT_INFO_Str      输入         来自识别库的基础信息
* pstruGpsInfoRpt        Drone_Detect_GpsInfo_Rpt 输入         外置GSP信息
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* ----------------------------------------------------------------------------
* 2024/10/25  zonghui	 创建文件
* ----------------------------------------------------------------------------
*******************************************************************************/
int TransformDroneData(u32 g_u32IfRecordSwitch, DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt)
{
    s32 s32Result = OSP_ERROR;
    DJI_FLIGHT_INFO_Str *tDroneOutputData = (DJI_FLIGHT_INFO_Str *)message;
    DtStruct_ApiSet *pApiSet;	
    DtAdapt *dataNum = NULLPTR;
    char str[DRONE_ARG_STRING_LEN];	
    cJSON *root;
	
    char *out;
    char ReportName[CAPS_SCENE_NAME_BYTE_COUNT] = {0};
    u32 u32DataLength = 0;
    time_t timep;	 
    struct tm *currentTime;
    u32 log_index = 0;
    /*Dji O4 Drone Type*/
    int judgeDroneType = 0;
    int indexDroneO4 = 0;
    double freq = 0;
    float  rssi = 0;

    if ((message == NULLPTR) || (pstruGpsInfoRpt == NULLPTR))
    {
        return OSP_ERROR;
    }

    pApiSet = DT_Adapt_Init();
    if(NULLPTR != pApiSet)
    {
       s32Result = CAPS_SetApi(pApiSet);
       if(OSP_OK != s32Result)
       {
           return OSP_ERROR;
       }    
    }

#if 0 //close debug info    
    printf("========2024-12-31  Test begin========\n");
    printf("packet_type=%d\n",   message->packet_type); 
    printf("wifi_flag=%d\n",   message->wifi_flag);
    printf("freq=%.1f\n",   message->freq / 1e6);
    printf("product_type_str=%s\n", message->product_type_str);
    printf("========2024-12-31 Test  end ========\n");

    printf("seq_num=%d\n", message->seq_num);  
    printf("state_info=%d\n",         message->state_info);  
    printf("serial_num=%s\n",          message->drone_serial_num);
    printf("drone_longtitude=%lf\n",          message->drone_longitude);
    printf("drone_latitude=%lf\n",          message->drone_latitude);
    printf("altitude=%f\n",          message->altitude);
    printf("height=%f\n",          message->height);  
    printf("northspeed=%f\n",         message->north_speed);
    printf("east_speed=%f\n",         message->east_speed);
    printf("up_speed=%f\n",         message->up_speed); 
    printf("pitch_angle=%d\n",        message->pitch_angle); 
    printf("roll_angle=%d\n",         message->roll_angle); 
    printf("yaw_angle=%d\n",         message->yaw_angle); 
    printf("gpstime=%d\n",       message->gpstime); 
    printf("pilot_longtitude=%f\n",          message->pilot_longitude); 
    printf("pilot_latitude=%f\n",          message->pilot_latitude); 
    printf("home_longitude=%f\n",          message->home_longitude);
    printf("home_latitude=%f\n",          message->home_latitude);
    printf("prodect_type=%d\n",          message->product_type);
    printf("uuid_length=%d\n",          message->uuid_length);
    printf("uuid=%s\n",          message->uuid);   
    printf("license=%s\n",       message->license);
    printf("wifi_ssid=%s\n",      message->wifi_ssid);  
    printf("wifi_vendor=%s\n",       message->wifi_vendor);
    printf("wifi_destination_address=%02x:%02x:%02x:%02x:%02x:%02x\n",message->wifi_destination_address[0],message->wifi_destination_address[1],message->wifi_destination_address[2],message->wifi_destination_address[3], message->wifi_destination_address[4], message->wifi_destination_address[5]);
    printf("wifi_source_addresse=%02x:%02x:%02x:%02x:%02x:%02x\n", message->wifi_source_address[0],message->wifi_source_address[1],message->wifi_source_address[2],message->wifi_source_address[3],message->wifi_source_address[4],message->wifi_source_address[5]);
    printf("wifi_bss_id_address=%02x:%02x:%02x:%02x:%02x:%02x\n",  message->wifi_bss_id_address[0],message->wifi_bss_id_address[1],message->wifi_bss_id_address[2],message->wifi_bss_id_address[3],message->wifi_bss_id_address[4],message->wifi_bss_id_address[5]);
    printf("efingerprint=%d\n",          message->efingerprint);
    printf("rssi=%.1f\n",          message->rssi / 1e6);
    printf("sysc_corr=%f\n",          message->sync_corr); 
    printf("bandwidth=%f\n",          message->bandwidth/1e6); 
    printf("decode_flag=%d\n",          message->decode_flag);
    printf("wifi_flag=%d\n",          message->wifi_flag);
    printf("device_num=%d\n",           message->device_num); 
    printf("antenna_code=%d\n",          message->antenna_code);   
    printf("10ms-offset=%d\n",         message->_10ms_offset); 
    printf("protocal_version=%d\n",         message->protocal_version);
    printf("UA_type=%d\n",         message->UA_type); 
    printf("drone_id=%s\n",         message->drone_id);
    printf("direction=%d\n",         message->direction); 
    printf("speed=%f\n",         message->speed);
#endif
    
    /*Judge Drone O4*/
    judgeDroneType = Judge_WhetherDroneIsO4((DJI_FLIGHT_INFO_Str *)message);
    //indexDroneO4 = QueryO4DroneByModel(message->product_type_str);
    //printf("[1-judge]:%d  [2-index]:%d, [3-%s]\n", judgeDroneType, indexDroneO4, message->product_type_str);
    
    while (g_rid_index < DJI_O4_RDI_MAX_COUNT)
    {
        if (g_RhhkReadRdiO4Info[g_rid_index].product_type_str != NULL)
        {
            indexDroneO4 = g_rid_index;
	        Dbg_Level_Print(LOG_INFO, "==>>:%d\n", indexDroneO4);
	        break;
        }
        g_rid_index++;
    }

    if (g_rid_index >= DJI_O4_RDI_MAX_COUNT)
    {
       g_rid_index = 0;
    }
    
    freq = (double)(message->freq / 1e6);
    rssi = message->rssi;
    if (judgeDroneType == 1)
    {
        Dbg_Level_Print(LOG_INFO, "######## 2025-02-14  rdi report start ########\n");
        Dbg_Level_Print(LOG_INFO, "UpLayer-index:%d\n", indexDroneO4);
        Dbg_Level_Print(LOG_INFO, "rdi_ssid:%s\n", g_RhhkReadRdiO4Info[indexDroneO4].rdi_ssid);
        Dbg_Level_Print(LOG_INFO, "drone_serial_num:%s\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_serial_num);
        Dbg_Level_Print(LOG_INFO, "product_type_str:%s\n", g_RhhkReadRdiO4Info[indexDroneO4].product_type_str);
        Dbg_Level_Print(LOG_INFO, "UA_type:%d\n", g_RhhkReadRdiO4Info[indexDroneO4].UA_type);
        Dbg_Level_Print(LOG_INFO, "drone_longtitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_longtitude);
        Dbg_Level_Print(LOG_INFO, "drone_latitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_latitude);
        Dbg_Level_Print(LOG_INFO, "pilot_longtitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].pilot_longtitude);
        Dbg_Level_Print(LOG_INFO, "pilot_latitude:%lf\n", g_RhhkReadRdiO4Info[indexDroneO4].pilot_latitude);
        Dbg_Level_Print(LOG_INFO, "drone_Hspeed:%f\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_Hspeed);
        Dbg_Level_Print(LOG_INFO, "drone_Vspeed:%f\n", g_RhhkReadRdiO4Info[indexDroneO4].drone_Vspeed);
        Dbg_Level_Print(LOG_INFO, "direction:%d\n", g_RhhkReadRdiO4Info[indexDroneO4].direction);
        Dbg_Level_Print(LOG_INFO, "Altitude:%f\n", g_RhhkReadRdiO4Info[indexDroneO4].altitude);
        Dbg_Level_Print(LOG_INFO, "freq:%.1f\n", freq);
	    memset(message, 0, sizeof(DJI_FLIGHT_INFO_Str));
        Dbg_Level_Print(LOG_INFO, "######## 2025-02-14   rdi report end ########\n");
    }

    Dbg_Level_Print(LOG_INFO, "1. Init (%s) ..., DJI Drone Please Wait a Moment!\n", "GenerateJson");
    root= g_pApiSet->m_pApi_CreateObject();
    
    /* Our array of "records": */    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->packet_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "packet_type", dataNum); 
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->seq_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "seq_num", dataNum); 

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->state_info);
    g_pApiSet->m_pApi_AddItemToObject(root, "state_info", dataNum);

    if (judgeDroneType == 1) //add----0
    {
        memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
        sprintf((char *)str, "%s", (const char *)g_RhhkReadRdiO4Info[indexDroneO4].rdi_ssid); 
        g_pApiSet->m_pApi_AddItemToObject(root, "rdi_ssid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    }
    else
    {
        memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
        g_pApiSet->m_pApi_AddItemToObject(root, "rdi_ssid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    }

    if (judgeDroneType == 1) //modify----1
    {
 	memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
	sprintf((char *)str, "%s", (const char *)g_RhhkReadRdiO4Info[indexDroneO4].drone_serial_num); 				
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_serial_num", g_pApiSet->m_pApi_CreateString((const char *)&str));
    }
    else
    {
        memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
        sprintf((char *)str, "%s", (const char *)tDroneOutputData->drone_serial_num);                 
        g_pApiSet->m_pApi_AddItemToObject(root, "drone_serial_num", g_pApiSet->m_pApi_CreateString((const char *)&str));
    }

    if (judgeDroneType == 1) //modify----4
    {
 	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_longtitude);		  
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_longitude", dataNum);
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->drone_longitude);          
        g_pApiSet->m_pApi_AddItemToObject(root, "drone_longitude", dataNum);
    }

    if (judgeDroneType == 1) //modify----5
    {
	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_latitude); 		  
	g_pApiSet->m_pApi_AddItemToObject(root, "drone_latitude", dataNum);
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->drone_latitude);           
        g_pApiSet->m_pApi_AddItemToObject(root, "drone_latitude", dataNum);
    }

    if (judgeDroneType == 1) //modify----11
    {
	dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].altitude);
	g_pApiSet->m_pApi_AddItemToObject(root, "altitude", dataNum);						   
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->altitude);
        g_pApiSet->m_pApi_AddItemToObject(root, "altitude", dataNum);                          
    }

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->height);
    g_pApiSet->m_pApi_AddItemToObject(root, "height", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->north_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "north_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->east_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "east_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->up_speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "up_speed", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pitch_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "pitch_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->roll_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "roll_angle", dataNum);
	
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->yaw_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "yaw_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->gpstime);
    g_pApiSet->m_pApi_AddItemToObject(root, "gpstime", dataNum);
		
    if (judgeDroneType == 1) //modify----6
    {
	    dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].pilot_longtitude);		   
	    g_pApiSet->m_pApi_AddItemToObject(root, "pilot_longitude", dataNum);				   
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pilot_longitude);           
        g_pApiSet->m_pApi_AddItemToObject(root, "pilot_longitude", dataNum);                   
    }

    if (judgeDroneType == 1)  //modify----7
    {
  	    dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].pilot_latitude); 		  
	    g_pApiSet->m_pApi_AddItemToObject(root, "pilot_latitude", dataNum);
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->pilot_latitude);           
        g_pApiSet->m_pApi_AddItemToObject(root, "pilot_latitude", dataNum);
    }

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->home_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->home_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "home_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_longitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_longitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_latitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_latitude", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(pstruGpsInfoRpt->local_altitude);
    g_pApiSet->m_pApi_AddItemToObject(root, "local_altitude", dataNum);
    
    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->product_type);
    g_pApiSet->m_pApi_AddItemToObject(root, "product_type", dataNum);
 
    if (judgeDroneType == 1) //modify----2
    {
 	memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
	sprintf((char *)str, "%s", (const char *)g_RhhkReadRdiO4Info[indexDroneO4].product_type_str); 				
        g_pApiSet->m_pApi_AddItemToObject(root, "product_type_str", g_pApiSet->m_pApi_CreateString((const char *)&str)); 
    } 
    else
    {
        g_pApiSet->m_pApi_AddItemToObject(root, "product_type_str", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->product_type_str));
    }

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->uuid_length);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid_length", dataNum);
 
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->uuid[0],tDroneOutputData->uuid[1],tDroneOutputData->uuid[2],
		    tDroneOutputData->uuid[3],tDroneOutputData->uuid[4],tDroneOutputData->uuid[5],tDroneOutputData->uuid[6],tDroneOutputData->uuid[7],
		    tDroneOutputData->uuid[8],tDroneOutputData->uuid[9],tDroneOutputData->uuid[10],tDroneOutputData->uuid[11],tDroneOutputData->uuid[12],
		    tDroneOutputData->uuid[13],tDroneOutputData->uuid[14],tDroneOutputData->uuid[15],tDroneOutputData->uuid[16],tDroneOutputData->uuid[17]);
    g_pApiSet->m_pApi_AddItemToObject(root, "uuid", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%c%c%c%c%c%c%c%c%c%c", tDroneOutputData->license[0],tDroneOutputData->license[1],tDroneOutputData->license[2],tDroneOutputData->license[3],
		    tDroneOutputData->license[4],tDroneOutputData->license[4],tDroneOutputData->license[6],tDroneOutputData->license[7],
		    tDroneOutputData->license[8],tDroneOutputData->license[9]);
    g_pApiSet->m_pApi_AddItemToObject(root, "license", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    g_pApiSet->m_pApi_AddItemToObject(root, "ssid", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_ssid));
    g_pApiSet->m_pApi_AddItemToObject(root, "vendor", g_pApiSet->m_pApi_CreateString((const char *)&tDroneOutputData->wifi_vendor));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_destination_address[0], tDroneOutputData->wifi_destination_address[1],
		    tDroneOutputData->wifi_destination_address[2],tDroneOutputData->wifi_destination_address[3],tDroneOutputData->wifi_destination_address[4],
		    tDroneOutputData->wifi_destination_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_destination_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_source_address[0],tDroneOutputData->wifi_source_address[1],
		    tDroneOutputData->wifi_source_address[2],tDroneOutputData->wifi_source_address[3],tDroneOutputData->wifi_source_address[4],
		    tDroneOutputData->wifi_source_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_source_address", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "0x%x,0x%x,0x%x,0x%x,0x%x,0x%x", tDroneOutputData->wifi_bss_id_address[0], tDroneOutputData->wifi_bss_id_address[1],
		    tDroneOutputData->wifi_bss_id_address[2],tDroneOutputData->wifi_bss_id_address[3],tDroneOutputData->wifi_bss_id_address[4],
		    tDroneOutputData->wifi_bss_id_address[5]);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_bss_id_address", g_pApiSet->m_pApi_CreateString((const char *)&str));

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->efingerprint);
    g_pApiSet->m_pApi_AddItemToObject(root, "efingerprint", dataNum);

    if (judgeDroneType == 1)  //modify----12
    {	    
        dataNum = g_pApiSet->m_pApi_CreateNumber((double)freq);
        g_pApiSet->m_pApi_AddItemToObject(root, "freq", dataNum);
    }
    else    
    {	    
        dataNum = g_pApiSet->m_pApi_CreateNumber((double)(tDroneOutputData->freq / 1e6));
        g_pApiSet->m_pApi_AddItemToObject(root, "freq", dataNum);
    }	

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->rssi);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->sync_corr);
    g_pApiSet->m_pApi_AddItemToObject(root, "sync_corr", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->bandwidth / 1e6);
    g_pApiSet->m_pApi_AddItemToObject(root, "bandwidth", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->decode_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "decode_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->wifi_flag);
    g_pApiSet->m_pApi_AddItemToObject(root, "wifi_flag", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->device_num);
    g_pApiSet->m_pApi_AddItemToObject(root, "device_num", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->antenna_code);
    g_pApiSet->m_pApi_AddItemToObject(root, "antenna_code", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->_10ms_offset);
    g_pApiSet->m_pApi_AddItemToObject(root, "10ms_offset", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->protocal_version);
    g_pApiSet->m_pApi_AddItemToObject(root, "protocal_version", dataNum);
    
    if (judgeDroneType == 1) //modify----3
    {
	    dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].UA_type);
	    g_pApiSet->m_pApi_AddItemToObject(root, "UA_type", dataNum);				   
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->UA_type);
        g_pApiSet->m_pApi_AddItemToObject(root, "UA_type", dataNum);                   
    }

    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, "%s", (const char *)tDroneOutputData->drone_id);
    g_pApiSet->m_pApi_AddItemToObject(root, "drone_id", g_pApiSet->m_pApi_CreateString((const char *)&str));

    if (judgeDroneType == 1) //modify----10
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].direction);
        g_pApiSet->m_pApi_AddItemToObject(root, "direction", dataNum);                
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->direction);
        g_pApiSet->m_pApi_AddItemToObject(root, "direction", dataNum);                
    }

    dataNum = g_pApiSet->m_pApi_CreateNumber(tDroneOutputData->speed);
    g_pApiSet->m_pApi_AddItemToObject(root, "speed", dataNum);

    if (judgeDroneType == 1)  //add----8
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_Hspeed);      
        g_pApiSet->m_pApi_AddItemToObject(root, "Hspeed", dataNum);
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(0.00);      
        g_pApiSet->m_pApi_AddItemToObject(root, "Hspeed", dataNum);
    }
	
    if (judgeDroneType == 1)  //add----9
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(g_RhhkReadRdiO4Info[indexDroneO4].drone_Vspeed);      
        g_pApiSet->m_pApi_AddItemToObject(root, "Vspeed", dataNum);
    }
    else
    {
        dataNum = g_pApiSet->m_pApi_CreateNumber(0.00);      
        g_pApiSet->m_pApi_AddItemToObject(root, "Vspeed", dataNum);
    }

    /*rssi handle*/
    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.freq);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_freq", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_drone);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_drone", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_angle);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_angle", dataNum);

    dataNum = g_pApiSet->m_pApi_CreateNumber(g_UpReportRssiInfo.rssi_max);
    g_pApiSet->m_pApi_AddItemToObject(root, "rssi_max", dataNum);
	
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->dji_byte176);
    g_pApiSet->m_pApi_AddItemToObject(root, "dji_byte176", g_pApiSet->m_pApi_CreateString((const char *)&str));
    
    memset(str, 0, DRONE_ARG_STRING_LEN*sizeof(char));
    sprintf((char *)str, (const char *)tDroneOutputData->reserved);
    g_pApiSet->m_pApi_AddItemToObject(root, "reserved", g_pApiSet->m_pApi_CreateString((const char *)&str));

	if (judgeDroneType == 1)
    {
		memset(&g_RhhkReadRdiO4Info[indexDroneO4], 0, sizeof(T_RHHKReadRdiO4Info));
	}

    out= cJSON_Print(root);     
    u32DataLength = strlen(out);
    /*Config Info*/     
    if(SWITCH_OPEN == g_u32IfRecordSwitch)
    {
	log_index = ((log_cycle_num++) % log_cycle_base);
        memset(ReportName, 0x00, sizeof(ReportName));
	currentTime = localtime(&timep);

        #ifndef WIN32
        sprintf(ReportName, "%s/DroneDetect_%d.json", GNB_SELFTEST_PROCESS_FILE, log_index);
        #else
        sprintf(ReportName, "%s/DroneDetect_%d.json", GNB_SELFTEST_PROCESS_FILE, log_index);
        #endif


        /*If File is Existed , then Deleting the FIle, avoid Testing Result is not Real*/
        if(0 == CAPS_JudgeFileExist((const char*)ReportName))
        {
            remove(ReportName);
        } 

        /*Create Testing Result File*/
        CreateConfigFile(ReportName,  out);
    }
    //Send Json To JavaPlat
    CopyDroneToQueue(out, u32DataLength, tDroneOutputData->product_type_str);
    //CopyDroneDataPacket(out, u32DataLength, tDroneOutputData->product_type_str);
    cJSON_Delete(root);    

    return OSP_OK;
}

char* Get_Current_Time(void) 
{
	  struct tm tm;
    static char timeStr[20];
    time_t t = time(NULL);
	  tzset();
	  setenv("TZ", "Asia/Shanghai", 1);
    localtime_r(&t, &tm);
    strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", &tm);
    return timeStr;
}

/*******************************************************************************
* 函数名称: Dbg_Level_Print
* 函数功能: 根据级别输出打印
* 函数参数: 
* 参数名称: 		类型			输入/输出	   描述
* print_level		u32 			输入		   级别
* format            char *          输入           输出格式
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void  Dbg_Level_Print(u32 print_level, char *format, ...)
{
    int Current_Num = Find_MaxLogNumber();
    FILE *Log_File = NULL;
    char Filename[256];
	char Loghead[256];
	
    char acPrintfBuf[CAPS_TESTCASE_PRINT_BUF_LEN];
	char rcPrintfBuf[CAPS_TESTCASE_PRINT_BUF_LEN];
    va_list argList;

    if(0 == g_dtm_default_print_level)
    {
         return; 
    }

	if(print_level > g_dtm_default_print_level)
    {
         return; 
    }

    memset(Loghead, 0x00, sizeof(Loghead));
    memset(acPrintfBuf, 0x00, sizeof(acPrintfBuf));
    memset(rcPrintfBuf, 0x00, sizeof(rcPrintfBuf));
    va_start(argList, format);
    vsprintf(acPrintfBuf, format, argList);
    va_end(argList);
	switch (print_level) 
	{
		case LOG_ERROR: sprintf(Loghead, "[ERROR][%s] ", Get_Current_Time()); break;
		case LOG_KEY:   sprintf(Loghead, "[KEY][%s] ", Get_Current_Time()); break;
		case LOG_WARN:	sprintf(Loghead, "[WARN][%s] ", Get_Current_Time()); break;
		case LOG_INFO:	sprintf(Loghead, "[INFO][%s] ", Get_Current_Time()); break;
		case LOG_DEBUG: sprintf(Loghead, "[DEBUG][%s] ", Get_Current_Time()); break;
	}
	
    acPrintfBuf[CAPS_TESTCASE_PRINT_BUF_LEN-1] = '\0';
	sprintf(rcPrintfBuf, "%s %s\n", Loghead, acPrintfBuf);
    printf("%s\n", rcPrintfBuf);

    sprintf(Filename, "/home/<USER>/log/DetectionLogFile_%d.log", Current_Num);
    Log_File = fopen(Filename, "a");
    if (Log_File == NULL) 
    {
        printf("Failed to open log file %s\n", Filename);
        return;
    }    
    Write_Log(rcPrintfBuf, &Current_Num, &Log_File);    
    fclose(Log_File);
    
    return;
}

s32 CAPS_SetApi(DtStruct_ApiSet *pApiSet)
{
    if(NULLPTR == pApiSet)
    {
        return OSP_ERROR;
    }

    g_pApiSet = pApiSet;
    return OSP_OK;
}
/*******************************************************************************
* 函数名称: CPAS_Start_Socket
* 函数功能: 创建线程处理网络用户请求
* 函数参数: 
* 参数名称: 		类型			输入/输出	   描述
* ip_remote		    char *			输入		   目标ip
* ip_local		    char *			输入		   本地ip
* outdata		    char *			输入		   目标数据
* u32DataLength     u32DataLength   输入           输出格式
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int CPAS_Start_Socket(char *ip_remote, char *ip_local,  char *outdata, u32 u32DataLength)
{
    static int sockfd;
    struct sockaddr_in server_addr;
	
    // cereat UDP socket
    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) {
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }

    // set UDP server
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(SEVER_PORT);
    server_addr.sin_addr.s_addr = INADDR_ANY; 
    inet_pton(AF_INET, SEVER_ADDR, &server_addr.sin_addr);

    if (bind(sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("bind failed");
        exit(EXIT_FAILURE);
    }

    // 发送数据
    if (sendto(sockfd, outdata, u32DataLength, 0, (struct sockaddr*) &server_addr, sizeof(server_addr)) < 0) {
        perror("sendto failed");
        exit(EXIT_FAILURE);
    }

    int flags;
    if ((flags = fcntl(sockfd, F_GETFL, 0)) < 0) {
        perror("fcntl:F_GETFL");
        return -1;
    }
    // 设置非阻塞模式
    flags |= O_NONBLOCK;
    if (fcntl(sockfd, F_SETFL, flags) < 0) {
        perror("fcntl:F_SETFL");
        return -1;
    }

	if (close(sockfd) < 0) {
        perror("Error closing socket");
        exit(EXIT_FAILURE);
    }
    printf("Outdata: 0x%x, len: %d\n", outdata, u32DataLength);
    printf("Socket have send and close successfully\n");
    return 1;
}
/*******************************************************************************
* 函数名称: Handle_DroneDetectData
* 函数功能: 探测数据转发
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* new_socket		int			输入		   listen sock
* listen_socket		int			输入		   accept sock
* read_fds		    int			输入		   描述符集
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_DroneDetectData(int new_sockfd, int listen_sockfd, fd_set read_fds)
{
    char *outdata = 0;
    u32 u32DataLength = 0;
    ssize_t SendBufSize = 0;
    ssize_t RcvBufSize = 0;
    u32 indata[20]; //check fuction
    int flags = 0;
    int sockfd = 0;

    flags = fcntl(new_sockfd, F_GETFL, 0);
    fcntl(new_sockfd, F_SETFL, flags | O_NONBLOCK);

    while(1)
    {
        for (int i = 0; i < MAX_JAVA_PLATFORM_NUM; i++)
	{
           sockfd = client_sockfd[i];
	   if (FD_ISSET(sockfd, &read_fds))
           {
	        RcvBufSize = recv(new_socket, (void *)indata, 20, 0);
	        if (RcvBufSize <= 0)
	        {
                    printf("########-5-####### errno=%d\n", errno);
	            close(new_socket);
		    client_sockfd[i] = 0;
	            break;
	         }
	   }
	}

	if (g_DroneDetectPacket.packet_rcvsign == 1)
        {
            outdata = (char *)&(g_DroneDetectPacket.packet_buffer);
            u32DataLength = g_DroneDetectPacket.packet_len;

            Dbg_Level_Print(LOG_INFO, "[TCP SocketSend Start] accept_sockfd:%d, data_length:%d\n", new_sockfd, u32DataLength);
            SendBufSize = send(new_socket, outdata, u32DataLength, MSG_NOSIGNAL);
            
            if(SendBufSize == 0)
            {
                Dbg_Level_Print(LOG_INFO, "TCP Client Have Closed, Waiting For Reconnect!");
	            Dbg_Level_Print(LOG_INFO, "########-2-#########\n");
                close(new_sockfd);
                break;
            }
	        else if(SendBufSize < 0)
            {
                perror("TCP Send Data Failed, Please Client Reconnect!");
	            Dbg_Level_Print(LOG_INFO, "########-3-#########\n");
                close(new_sockfd);
                break;
            }
            g_DroneDetectPacket.packet_rcvsign = 0;
	        Dbg_Level_Print(LOG_KEY, "########-4-[rpt_drone_mode:%s]############\n", g_DroneDetectPacket.packet_drone_model);
	    }
        usleep(2000);
    }
    Dbg_Level_Print(LOG_INFO, "=================net re connect================\n");
    return 0;
}
/*******************************************************************************
* 函数名称: handle_data
* 函数功能: 多用户数据处理函数
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* arg        		void*       输入		   传递sockfd，已经放弃，采用全局变量
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void handle_data(void *arg)
{
    int client_socket;
    int RcvBufSize;
    char *outdata = 0;
    u32 indata[20];
    u32 u32DataLength = 0;
    ssize_t SendBufSize = 0;
    char RcvBuf[MAX_RCV_BUFFER_SIZE];
    Dbg_Level_Print(LOG_INFO, "accepted handle_data have enter!\n");       

    while(1)
    {	    
        SendDroneToQueue(1);
        #if 0
        for (int i = 0; i < MAX_JAVA_PLATFORM_NUM; i++)
        {
	          if (client_sockfd[i] != 0)
		        {	 	
                memset(RcvBuf, 0, sizeof(RcvBuf));
				        if ((RcvBufSize = recv(client_sockfd[i], RcvBuf, MAX_RCV_BUFFER_SIZE, MSG_DONTWAIT)) > 0)
				        {
                    printf("#########READ#########:%s\n", RcvBuf);
			          }    
                
                SendDroneToQueue(client_sockfd[i]);
                #if 0    
		            if (g_DroneDetectPacket.packet_rcvsign == 1)
                {
                    outdata = (char *)&(g_DroneDetectPacket.packet_buffer);
                    u32DataLength = g_DroneDetectPacket.packet_len;

                    Dbg_Level_Print(LOG_INFO, "[TCP SocketSend Start] accept_sockfd:%d, data_length:%d\n", client_sockfd[i], u32DataLength);
					          if (u32DataLength > 0)
					          {
                        SendBufSize = send(client_sockfd[i], outdata, u32DataLength, MSG_NOSIGNAL);
            
                        if(SendBufSize == 0)
                        {
                            Dbg_Level_Print(LOG_INFO, "TCP Client Have Closed, Waiting For Reconnect!");
                            Dbg_Level_Print(LOG_INFO, "######## multi-2 #########(%d)\n", SendBufSize);
                        }
                        else if(SendBufSize < 0)
                        {
                            Dbg_Level_Print(LOG_INFO, "TCP Send Data Failed, Please Client Reconnect!");
                            Dbg_Level_Print(LOG_INFO, "######## multi-3 #########(%d)\n", SendBufSize);
						                close(client_sockfd[i]);
						                client_sockfd[i] = 0;
                        }
					         }
					         else
					         {
                         Dbg_Level_Print(LOG_INFO, "[TCP SocketSend Start] DataSize is %d\n, NoData Need to Send\n", u32DataLength);
					         }
               } 
               #endif
				       Dbg_Level_Print(LOG_INFO, "######## multi-4 ############([index:%d, SendSize:%d, rcvsign:%d])\n", i, SendBufSize, g_DroneDetectPacket.packet_rcvsign);
		      }
			    usleep(1000); 
      }
      g_DroneDetectPacket.packet_rcvsign = 0;
      #endif	    
		  usleep(1000);
    }
     
	  Dbg_Level_Print(LOG_INFO, "######## multi-5 ############, socket closed and pthread_exit!\n");
	  pthread_exit(NULL);
    return;	
}
/*******************************************************************************
* 函数名称: Create_Socket
* 函数功能: 启动sock server接收客户请求
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* ip_remote        	char*       输入		   ip(改用全局和配置文件)
* ip_local        	char*       输入		   ip(改用全局和配置文件)
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Create_Socket(char *ip_remote, char *ip_local)
{
    //static int detect_sockfd;
    //struct sockaddr_in detect_server_addr;
    fd_set write_fds, read_fds;
    int select_return = 0;
    int flags;
    int sockfd;
    int maxsockfd;
    int pid;
    int opt = 1;
    int RcvBufSize;
    char *outdata = 0;
    u32 indata[20]; //check function
    u32 u32DataLength = 0;
    ssize_t SendBufSize = 0;
    pthread_t tid;
    int index = 0;
    socklen_t sin_size = sizeof(detect_server_addr);
    
    T_RhhkMangerConfArg OutangerConfArg;
    const char *pTCPIP;
    int16_t  TCPPORT;

    #ifdef UDP_MODE
    // cereat UDP socket
    detect_sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (detect_sockfd < 0) {
        perror("udp socket creation failed");
        exit(EXIT_FAILURE);
    }

    // set UDP server
    memset(&detect_server_addr, 0, sizeof(detect_server_addr));
    detect_server_addr.sin_family = AF_INET;
    detect_server_addr.sin_port = htons(SEVER_PORT);
    detect_server_addr.sin_addr.s_addr = INADDR_ANY;
    inet_pton(AF_INET, SEVER_ADDR, &detect_server_addr.sin_addr);
    memset(detect_server_addr.sin_zero, 0, sizeof(detect_server_addr.sin_zero));

    if (bind(detect_sockfd, (struct sockaddr *)&detect_server_addr, sizeof(detect_server_addr)) < 0) {
        perror("udp bind failed");
        exit(EXIT_FAILURE);
    }

    if ((flags = fcntl(detect_sockfd, F_GETFL, 0)) < 0) {
        perror("udp fcntl:F_GETFL");
        return -1;
    }
    // 脡猫脰脙路脟脳猫脠没脛拢脢陆
    flags |= O_NONBLOCK;
    if (fcntl(detect_sockfd, F_SETFL, flags) < 0) {
        perror("udp fcntl:F_SETFL");
        return -1;
    }

        if (close(detect_sockfd) < 0) {
        perror("udp Error closing socket");
        exit(EXIT_FAILURE);
    }
    printf("udp Socket have Created successfully\n");
    #else
    detect_sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (detect_sockfd < 0) {
        perror("tcp socket creation failed");
        exit(EXIT_FAILURE);
    }

    if(setsockopt(detect_sockfd, SOL_SOCKET,SO_REUSEADDR|SO_REUSEPORT, &opt, sizeof(opt)))
    { 
       perror("tcp setsockopt failed");
       exit(EXIT_FAILURE);
    }
    
    //Get Addr And Port by Parse Config File
    memset(&OutangerConfArg,0,sizeof(T_RhhkMangerConfArg));
    if(0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *)&OutangerConfArg))
    {
         pTCPIP = OutangerConfArg.tcp_ip;
         TCPPORT = OutangerConfArg.tcp_port;
		 g_debug_switch = OutangerConfArg.debug_switch;
		 g_dtm_default_print_level = OutangerConfArg.debug_switch;
		 g_manu_longitude = OutangerConfArg.lon;
		 g_manu_latitude = OutangerConfArg.lat;
		 g_manu_altitude = OutangerConfArg.alt;
		 strcpy(&(g_device_code[0]), OutangerConfArg.device_code);
    }
    else
    {
         pTCPIP = SEVER_ADDR;
         TCPPORT = SEVER_PORT;	 
		 g_debug_switch = 0;
		 g_manu_longitude = 0;
		 g_manu_latitude = 0;
		 g_manu_altitude = 0;
    }
	Handle_GpsFromFreqPatch();
	Handle_MakeVersionInfo();
    Dbg_Level_Print(LOG_INFO, "[*]Read Tcp Server AddrInfo: %s  %d\n", pTCPIP, TCPPORT);
    
    // set TCP server
    memset(&detect_server_addr, 0, sizeof(detect_server_addr));
    detect_server_addr.sin_family = AF_INET;
    detect_server_addr.sin_addr.s_addr = INADDR_ANY;
    detect_server_addr.sin_port = htons(TCPPORT);
    //inet_pton(AF_INET, SEVER_ADDR, &detect_server_addr.sin_addr);
    memset(detect_server_addr.sin_zero, 0, sizeof(detect_server_addr.sin_zero));

    if (bind(detect_sockfd, (struct sockaddr *)&detect_server_addr, sizeof(detect_server_addr)) < 0) {
        perror("tcp bind failed");
        exit(EXIT_FAILURE);
    }
    Dbg_Level_Print(LOG_INFO, "[*]Bind Tcp Server AddrInfo: sockfd:%d\n", detect_sockfd);
    
    if(listen(detect_sockfd, 5) < 0)
    {
        perror("tcp listen failed");
        exit(EXIT_FAILURE);
    }

    for (int i = 0; i < MAX_JAVA_PLATFORM_NUM; i++)
    {
        client_sockfd[i] = 0;
    }
    Dbg_Level_Print(LOG_INFO, "[*] While before\n");
    
    while(1)
    {
        FD_ZERO(&read_fds);
        FD_SET(detect_sockfd, &read_fds);
        maxsockfd = detect_sockfd;

        for (int i = 0; i < MAX_JAVA_PLATFORM_NUM; i++) 
		    {
            if (client_sockfd[i] > 0) 
			      {
                FD_SET(client_sockfd[i], &read_fds);
                if (client_sockfd[i] > maxsockfd) 
				        {
                    maxsockfd = client_sockfd[i];
                }
            }
        }
		
        //printf("[*] While after\n");
        //printf("[*] Read maxsockfd:%d\n", maxsockfd);
        select_return = select(maxsockfd + 1, &read_fds, NULL, NULL, NULL);
        if (select_return < 0)
        {			
		       if(errno == EINTR)
	        {
			        continue;
			    }
			    else
			    {
               Dbg_Level_Print(LOG_INFO, ".");
	        }     
	     } 
 
      //printf("[*] Accept before: select_return:%d\n", select_return);
	    if(FD_ISSET(detect_sockfd, &read_fds))
	    {   
            
            new_socket = accept(detect_sockfd,(struct sockaddr *)&detect_server_addr, (socklen_t *)&sin_size);
            if (new_socket < 0)
            {
                perror("tcp accept failed.\n");
	              continue;
            }
	          //printf("[*] Accetp after tcp accept successful.(new_socket:%d)\n", new_socket);
	          for (int i = 0; i < MAX_JAVA_PLATFORM_NUM; i++)
            {
	              if (client_sockfd[i] == 0)
		            {	 	
                  client_sockfd[i] = new_socket;
		              index = i;
					        Dbg_Level_Print(LOG_INFO, "[*]pthread_create:ACCEPT: clinet_sockfd[%d]=%d\n", index, client_sockfd[index]); 
		              break;
		            }
                            
                if (i >= MAX_JAVA_PLATFORM_NUM)
		  	        {
                    Dbg_Level_Print(LOG_INFO, "[*]pthread_create:ACCEPT: beyond max, index(%d),close(%d)\n", index, new_socket);
				            close(new_socket);                                    
			          }            
           } 
           //FD_CLR(detect_sockfd, &read_fds);         
	    }	  
      usleep(100);   
	    //printf("[*] Start handle drone infomation...\n");
    }
    #endif    
    return 1;
}

int handle_client(void)
{
    char *ip_remote = "************";
    char *ip_local =  "************";
    Create_Socket(ip_remote, ip_local);
	return 0;
}
/*******************************************************************************
* 函数名称: Create_ClinetAcception
* 函数功能: 启动sock server接收客户请求
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/

int Create_ClinetAcception(void)
{
    
	pthread_t client_tid;
    if (pthread_create(&client_tid, NULL, handle_client, NULL) != 0)
    {
		perror("create pthread for new client is failed\n");
		Dbg_Level_Print(LOG_ERROR, "[*]Create_ClinetAcception: create pthread is failed\n");
	    return -1;
    } 
    else 
    {
		pthread_detach(client_tid); 
    }

	Dbg_Level_Print(LOG_INFO, "[*]Create_ClinetAcception: create pthread is successful!\n");
	return 0;
}

/*******************************************************************************
* 函数名称: Create_JasonDataSend
* 函数功能: 启动线程向合法接入用户发送探测结果
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Create_JasonDataSend(void)
{
    pthread_t data_tid;
    if (pthread_create(&data_tid, NULL, handle_data, NULL) != 0)
    {
	     perror("create pthread for new client is failed\n");
         Dbg_Level_Print(LOG_ERROR, "[*]Create_JasonDataSend: create pthread is failed\n");;
	} 
	else 
	{
         pthread_detach(data_tid); 
    }

    Dbg_Level_Print(LOG_INFO, "[*]Create_JasonDataSend: create pthread is successful!\n");
	return 0;
}
/*******************************************************************************
* 函数名称: Send_MsgToJava
* 函数功能: 发送消息给Java客户端
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Send_MsgToJava(char *outdata, u32 u32DataLength)
{
    #if 1
    /********20241114 Add start***********/
    char *pSendStr = NULL;	
    if ((outdata == NULL) or (u32DataLength == 0))
    {
	free(outdata);
	close(new_socket);
	//close(detect_sockfd);
        Create_Socket("remote_ip", "local_ip");
        return 0;
    }
   
   // pSendStr = DelString(outdata, "\n", outdata);
   // pSendStr = DelString(outdata, "\r", outdata);
   // pSendStr = DelString(outdata, "\t", outdata);
   // u32DataLength = strlen(pSendStr);
   // *(pSendStr + u32DataLength + 1 ) = '\n';
   // u32DataLength = u32DataLength + 1;
    
    #ifdef UDP_MODE	
    Dbg_Level_Print(LOG_INFO, "\n[udp SocketSend Start---------] detect_sockfd:%d, data_length:%d\n", detect_sockfd, u32DataLength);
    if (sendto(detect_sockfd, pSendStr, u32DataLength, 0, (struct sockaddr*) &detect_server_addr, sizeof(detect_server_addr)) < 0) {
        perror("sendto failed, please client re connect!");
	    free(outdata);
	    close(new_socket);
	    //close(detect_sockfd);
	    exit(EXIT_FAILURE);
    }
    Dbg_Level_Print(LOG_INFO, "[UDP SocketSend Finish----------] pSendStr: 0x%x, len: %d\n\n", pSendStr, u32DataLength);
    #else
    Dbg_Level_Print(LOG_INFO, "\n[TCP SocketSend Start---------] detect_sockfd:%d, data_length:%d\n", detect_sockfd, u32DataLength);
    if(send(new_socket, outdata, u32DataLength, 0) < 0)
    {
        perror("TCP send data failed,please client re connect!");
	    free(outdata);
	    close(new_socket);
	    //close(detect_sockfd);
        Create_Socket("remote_ip", "local_ip");
	    exit(EXIT_FAILURE);
    }
    free(outdata);
    Dbg_Level_Print(LOG_INFO, "[TCP SocketSend Finish----------] len: %d\n\n", u32DataLength);
    #endif
    #else
    /********20241114 Add start***********/
    #ifdef UDP_MODE	
    printf("\n[udp SocketSend Start---------] detect_sockfd:%d, data_length:%d\n", detect_sockfd, u32DataLength);
    if (sendto(detect_sockfd, outdata, u32DataLength, 0, (struct sockaddr*) &detect_server_addr, sizeof(detect_server_addr)) < 0) {
        perror("sendto failed");
        exit(EXIT_FAILURE);
    }
    printf("[Udp SocketSend Finish----------] Outdata: 0x%x, len: %d\n\n", outdata, u32DataLength);
    #else
    printf("\n[Tcp SocketSend Start---------] detect_sockfd:%d, data_length:%d\n", detect_sockfd, u32DataLength);
    if(send(new_socket, outdata, u32DataLength, 0) < 0)
    {
        perror("tcp send data failed!");
    }
    printf("[Tcp SocketSend Finish----------] Outdata: 0x%x, len: %d\n\n", outdata, u32DataLength);
    #endif
    #endif
    return 0;
}

/*******************************************************************************
* 函数名称: Get_ManuStaticLocalGps
* 函数功能: 获取配置文件中手动配置静态GPS信息
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Get_ManuLocalGpsInfo(float *flon, float *flat, float *falt)
{
    if ((flon == NULL) || (flat == NULL)|| (falt == NULL))
    {
        return -1;
	}
    Dbg_Level_Print(LOG_INFO, "[1-@@@@@@]flon and flat point is not null!\n");

    if ((abs(g_manu_longitude) > 1e-9) && (abs(g_manu_latitude) > 1e-9))
    {
            *flon = float(g_manu_longitude);
            *flat = float(g_manu_latitude);
			*falt = float(g_manu_altitude);
    }

    Dbg_Level_Print(LOG_INFO, "[2-@@@@@@] rdi manu local gps info: lon: %f, lat:%f, alt:%f\n", *flon, *flat, *falt);    
    return 0;
}
/*******************************************************************************
* 函数名称: Find_MaxLogNumber
* 函数功能: 找最大日志号
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Find_MaxLogNumber() 
{
    DIR *dir;
    struct dirent *entry;
    int max_num = 1;
    
    dir = opendir(".");
    if (dir == NULL) 
    {
        perror("Failed to open directory");
        return 0;
    }
    
    while ((entry = readdir(dir)) != NULL) 
    {
        if (strncmp(entry->d_name, "DetectionLogFile_", 17) == 0) 
        {
            Dbg_Level_Print(LOG_INFO, "LogName==>:%s\n", entry->d_name);
            char *dot = strrchr(entry->d_name, '.');
            if (dot != NULL && strcmp(dot, ".log") == 0) 
            {
				        *dot = '\0';
                char *num_start = entry->d_name + 17;
                int num = atoi(num_start);
                if (num > max_num) 
                {
                    max_num = num;
                }
            }
        }
    }
    closedir(dir);
    
    // 检查最大文件大小
    if (max_num > 0) 
	  {
        char filename[256];
        sprintf(filename, "/home/<USER>/log/DetectionLogFile_%d.log", max_num);
        struct stat st;
        if ((stat(filename, &st) == 0) && (st.st_size >= MAX_FILE_SIZE))
		{
            max_num++;
        }
    } 
    else 
    {
        max_num = 1; // 从log_1.log开始
    }
    
    max_num = (max_num % MAX_FILES); 
    
    return max_num;
}
/*******************************************************************************
* 函数名称: Rotate_Log
* 函数功能: 循环记录日志
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void Rotate_Log(int *current_num, FILE **log_file) 
{
    if (*log_file != NULL)
	{
        fclose(*log_file);
        *log_file = NULL;
    }
    
    (*current_num)++;
    
    // 删除旧日志文件
    if (*current_num > MAX_FILES) 
	{
        int delete_num = (*current_num) % MAX_FILES;;
        char delete_filename[256];
        sprintf(delete_filename, "/home/<USER>/log/DetectionLogFile_%d.log", delete_num);
        if (remove(delete_filename) != 0) 
		{
            perror("Failed to delete old log file");
        }
    }
    
    // 创建新日志文件
    char filename[256];
    sprintf(filename, "/home/<USER>/log/DetectionLogFile_%d.log", *current_num);
    *log_file = fopen(filename, "a");
    if (*log_file == NULL) 
	{
        perror("Failed to open new log file");
        exit(EXIT_FAILURE);
    }
}
/*******************************************************************************
* 函数名称: Write_Log
* 函数功能: 记录日志
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void Write_Log(const char *message, int *current_num, FILE **log_file) 
{
    // 检查当前文件大小
    fseek(*log_file, 0, SEEK_END);
    long size = ftell(*log_file);
    if (size >= MAX_FILE_SIZE) 
	{
        Rotate_Log(current_num, log_file);
    }
    
    // 写入日志并刷新缓冲区
    fprintf(*log_file, "%s\n", message);
    fflush(*log_file);
}

