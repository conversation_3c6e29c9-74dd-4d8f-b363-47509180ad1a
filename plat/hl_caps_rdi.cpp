/*******************************************************************************
* COPYRIGHT RHHK  CO.,LTD
********************************************************************************
* �ļ�����: hl_caps_rdi.cpp
* ��������: RHHK RIDģ�����ݽ���
* �������:
* ��	��: zonghui
* ����ʱ��: 2024/12/20
* �޸�����	  �޸���  BugID/CRID	  �޸�����
* ------------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <psdk_inc/_fd_types.h>
#include <sys/time.h>
#ifndef WIN32
#include <termios.h>
#include <string.h>
#include <strings.h>
#include <unistd.h>
#include <thread>
#include <fcntl.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/time.h>
#include <math.h>
#else
#include <windows.h>
#endif
#include "hl_caps_rdi.h"
#include "hl_caps_report.h"
/******************************* ¾Ö²¿ºê¶¨Òå ***********************************/
#define  RDI_RCV_DATA_LENGTH  3000
#define DJI_O4_RDI_COUNT   40
#define DJI_O4_RDI_LENGTH  100
#define DJI_O4_RDI_MAX_COUNT  20
#define TIME_FILE_PATH "/home/<USER>/config/time"

/******************************* ¾Ö²¿³£ÊýºÍÀàÐÍ¶¨Òå ***************************/
typedef unsigned int u32;
typedef signed int s32;
typedef unsigned short u16;
typedef signed short s16;
typedef unsigned char u8;
typedef signed char s8;

/******************************* È«¾Ö±äÁ¿¶¨Òå/³õÊ¼»¯ **************************/
pthread_mutex_t mutex_tid;
T_RHHKReadRdiO4Info g_RhhkReadRdiO4Info[DJI_O4_RDI_MAX_COUNT];
int ReadRdiO4Index = 0;
static int time_flag = 0;
/*CONFIGURE*/
//options.c_cc[VTIME] = 0;

/* 等待100ms* 该值等待时间就返回 */
//options.c_cc[VMIN] = 1;
char g_DjiDroneO4Type[3][50] = {"Air 3", "Mini 4 Pro", "Avata 2"};

/*GpsCoordinate*/
float g_local_latitude = 0;
float g_local_longitude = 0;
float g_local_altitude = 0;
extern int g_debug_switch;
extern double g_manu_longitude;
extern double g_manu_latitude;
extern double g_manu_altitude;
extern T_RhhkMangerConfArg OutConfInfo;
time_t g_his_timesec = 0;

/******************************Local Function Define **************************/
extern int CheckWrQueueStatus(void);

extern void CopyDroneToQueue(char *out, u32 u32DataLength, char *pdrone_model);

extern void CopyDroneDataPacket(char *out, u32 u32DataLength, char *drone_model);

extern void Dbg_Level_Print(u32 print_level, char *format, ...);

extern int Get_ManuLocalGpsInfo(float *flon, float *flat, float *falt);

extern int Handle_GpsFromFreqPatch(void);

extern int Rhhk_SetDebugData(u8 module_number, u8 argment_number, u32 argment_value, char *buffer, int buffer_size);

/******************************* ����ʵ�� *************************************/
/*******************************************************************************
* ��������: Open_Port
* ��������: ���豸
* ��������:
* ��������: 		����		   ����/���	 ����
  *comport			int            ���� 		 �ַ��豸��Ӧ���
* ����ֵ: copy
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Open_Port(int comport) {
    int fd;
    char *dev[] = {"/dev/ttyUSB0", "/dev/ttySAC1"};
    if (comport == 0) {
        //串口0
        fd = open("/dev/serial/by-id/usb-1a86_USB_Serial-if00-port0",O_RDWR | O_NOCTTY | O_NDELAY);
        if (-1 == fd) {
            perror("NxPlat Can't Open Serial Port 0");
            return (-1);
        }
        Dbg_Level_Print(LOG_INFO, "Open_Port0[/dev/serial/by-id/usb-1a86_USB_Serial-if00-port0]:fd=%d\n", fd);
    } else if (comport == 1) {
        //串口1
        //fd=open("/dev/serial/by-path/pci-0000:00:14.0-usb-0:9:1.0",O_RDWR|O_NOCTTY|O_NDELAY);
        //fd=open("/dev/serial/by-id/usb-1a86_USB_Serial-if00-port0", O_RDWR|O_NOCTTY|O_NDELAY);
        fd = open("/dev/serial/by-id/usb-u-blox_AG_-_www.u-blox.com_u-blox_GNSS_receiver-if00",
                  O_RDWR | O_NOCTTY | O_NDELAY);
        if (-1 == fd) {
            fd = open("/dev/serial/by-id/usb-Espressif_USB_JTAG_serial_debug_unit_34:CD:B0:3C:10:E0-if00",
                      O_RDWR | O_NOCTTY | O_NDELAY);
            if (-1 == fd) {
                perror(
                    "NxPlat Can't Open Serial Port 1[first times(usb-Espressif_USB_JTAG_serial_debug_unit_34:CD:B0:3C:10:E0-if00)]");
            } else {
                Dbg_Level_Print(
                    LOG_INFO,
                    "Open_Port1[/dev/serial/by-id/usb-Espressif_USB_JTAG_serial_debug_unit_34:CD:B0:3C:10:E0-if00]:fd=%d\n",
                    fd);
            }
        }

        if (-1 == fd) {
            fd = open("/dev/ttyTHS3", O_RDWR | O_NOCTTY | O_NDELAY);
            if (-1 == fd) {
                perror("NxPlat Can't Open Serial Port 1[second times(/dev/ttyTHS3)]");
                return (-1);
            } else {
                Dbg_Level_Print(LOG_INFO, "Open_Port1[/dev/ttyTHS3]:fd=%d\n", fd);
            }
        }
        Dbg_Level_Print(LOG_INFO, "Open_Port1:fd=%d\n", fd);
    }

    if (fcntl(fd, F_SETFL, 0) < 0) {
        Dbg_Level_Print(LOG_INFO, "fcntl failed! ");
    }

    if (isatty(STDIN_FILENO) == 0) {
        Dbg_Level_Print(LOG_INFO, "Standard Input Is Not a Termina Device");
    }

    Dbg_Level_Print(LOG_INFO, "Open_Port:Finished!\n");
    return fd;
}

/*******************************************************************************
* ��������: Set_Option
* ��������: �豸��ʼ��
* ��������:
* ��������: 		����		   ����/���	 ����
* fd    			int            ���� 		 �ַ��豸������
* nSpeed            int            ����          ����
* nBits             int            ����          �ֳ�
* nEvent            char           ����          ��żУ��
* nStop             int            ����          ֹͣλ
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Set_Option(int fd, int nSpeed, int nBits, char nEvent, int nStop) {
    struct termios newtio, oldtio;
    if (tcgetattr(fd, &oldtio) != 0) {
        perror("Error: SetupSerial 1");
        return -1;
    }

    bzero(&newtio, sizeof (newtio));
    //对结构体清零
    newtio.c_cflag |= CLOCAL | CREAD;
    newtio.c_cflag &= ~CSIZE;

    switch (nBits) {
        case 7:
            newtio.c_cflag |= CS7;
            break;
        case 8:
            newtio.c_cflag |= CS8;
            break;
    }

    switch (nEvent) {
        case 'O':
            newtio.c_cflag |= PARENB;
            newtio.c_cflag |= PARODD;
            newtio.c_iflag |= (INPCK | ISTRIP);
            break;
        case 'E':
            newtio.c_iflag |= (INPCK | ISTRIP);
            newtio.c_cflag |= PARENB;
            newtio.c_cflag &= ~PARODD;
            break;
        case 'N':
            newtio.c_cflag &= ~PARENB;
            break;
    }

    switch (nSpeed) {
        case 2400:
            cfsetispeed(&newtio, B2400);
            cfsetospeed(&newtio, B2400);
            break;
        case 4800:
            cfsetispeed(&newtio, B4800);
            cfsetospeed(&newtio, B4800);
            break;
        case 9600:
            cfsetispeed(&newtio, B9600);
            cfsetospeed(&newtio, B9600);
            break;
        case 115200:
            cfsetispeed(&newtio, B115200);
            cfsetospeed(&newtio, B115200);
            break;
        case 460800:
            cfsetispeed(&newtio, B460800);
            cfsetospeed(&newtio, B460800);
            break;
        default:
            cfsetispeed(&newtio, B9600);
            cfsetospeed(&newtio, B9600);
            break;
    }

    if (nStop == 1) {
        newtio.c_cflag &= ~CSTOPB;
    } else if (nStop == 2) {
        newtio.c_cflag |= CSTOPB;
    }

    newtio.c_cc[VTIME] = 0;
    newtio.c_cc[VMIN] = 0;

    tcflush(fd, TCIFLUSH);
    //刷新收到的数据但是不清空

    if ((tcsetattr(fd, TCSANOW, &newtio)) != 0) {
        //TCSANOW：改变的配置立即生效
        perror("Commit Error Happen");
        return -1;
    }
    Dbg_Level_Print(LOG_INFO, "set done!\n");
    return 0;
}

/*******************************************************************************
* ��������: Judge_WhetherDroneIsO4
* ��������: �жϷɻ�������Ч
* ��������:
* ��������: 		  ����		            ����/���	 ����
* pstruDJIFlightInfo  DJI_FLIGHT_INFO_Str*  ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Judge_WhetherDroneIsO4(DJI_FLIGHT_INFO_Str *pstruDJIFlightInfo) {
    int index = 0;

    if (pstruDJIFlightInfo == NULL) {
        return -1;
    }

#if 0
    printf("========2024-12-31  judge begin========\n");
    printf("packet_type=%d\n", pstruDJIFlightInfo->packet_type);
    printf("wifi_flag=%d\n", pstruDJIFlightInfo->wifi_flag);
    printf("freq=%.1f\n", pstruDJIFlightInfo->freq / 1e6);
    printf("product_type_str=%s\n", pstruDJIFlightInfo->product_type_str);
    printf("========2024-12-31 judege  end ========\n");
#endif
    if ((pstruDJIFlightInfo->wifi_flag == 0) && ((pstruDJIFlightInfo->freq / 1e6) > 0.01) &&
        ((pstruDJIFlightInfo->packet_type == 21) || (pstruDJIFlightInfo->packet_type == 24))) {
        return 1;
    } else {
        return 0;
    }
}

/*******************************************************************************
* ��������: QueryO4DroneByModel
* ��������: ���ݷɻ�model�������˻����ݰ�����
* ��������:
* ��������: 		  ����		            ����/���	 ����
* pstruDJIFlightInfo  DJI_FLIGHT_INFO_Str*  ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int QueryO4DroneByModel(char *product_type_str) {
    int index = -1;
    if (product_type_str == NULL) {
        return -1; /*invalid*/
    }
    while (index < DJI_O4_RDI_MAX_COUNT) {
        if ((g_RhhkReadRdiO4Info[index].rdi_packet_valid == 1) &&
            (0 == strcmp(g_RhhkReadRdiO4Info[index].product_type_str, product_type_str))) {
            g_RhhkReadRdiO4Info[index].rdi_packet_valid = 0;
            return index;
        }
        index++;
    }
    return -1; /*can't find index by model of O4 Drone*/
}

/*******************************************************************************
* ��������: RemoveRdiHeader
* ��������: ɾ��RDI����4�ֽ�Header
* ��������:
* ��������: 		  ����		            ����/���	 ����
* buffer              char*                 ���� 		 ���ݰ�ָ��
* length              int
* ����ֵ:             void
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
void RemoveRdiHeader(char *buffer, int length) {
    if (length <= 4) {
        memset(buffer, 0, length);
        return;
    }
    // ����4�ֽں������ǰ��
    memmove(buffer, buffer + 4, length - 4);
    memset(buffer + length - 4, 0, 4);
    return;
}

/*******************************************************************************
* ��������: Parse_DjiO4Broadcast
* ��������: �������˻��㲥����
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Parse_DjiO4Broadcast(char *RdiBuffer) {
    const char *delim1 = ",";
    const char *delim2 = "=";
    char *token_rdi1;
    char *token_rdi2;
    char *keystr1;
    char *keystr2;
    char Data[DJI_O4_RDI_COUNT][DJI_O4_RDI_LENGTH];
    char position[2][50];
    int pos_index1 = 0, pos_index2 = 0;
    int UaType;
    int index1 = 0;
    int index2 = 0;
    int ivalue = 0;
    float fvalue = 0;
    double dvalue = 0;
    int ActO4Index = 0;
    int index3 = 0;
    char rdi_scan_finish = 0;
    char rdi_scan_start = 0;

    const char *drone_o4_type[13] = {
        "RID ssid", "serial", "model", " UA_type", "drone_GPS", "pilot_GPS", "speed", "Vspeed", "direc", "Altitude",
        "MAC", "rssi", "freq"
    };

    if (RdiBuffer == NULL) {
        Dbg_Level_Print(LOG_INFO, "Get RdiBroadcast is NULL!\n");
        return -1;
    }

    while (index3 < 10) {
        if (NULL == strstr(RdiBuffer, drone_o4_type[index3])) {
            Dbg_Level_Print(LOG_INFO, "the packet(index3:%d) from broadcast is not dji o4 drone!, drop it!\n", index3);
            return -1;
        }
        index3++;
    }

    memset(Data, 0, 100 * sizeof(char));
    //ActO4Index = (ReadRdiO4Index++)%DJI_O4_RDI_MAX_COUNT;
    ActO4Index = 0;
    memset(&g_RhhkReadRdiO4Info[ActO4Index], 0, sizeof(T_RHHKReadRdiO4Info));
    keystr1 = (char *) RdiBuffer;
    while (token_rdi1 = strtok_r(keystr1, delim1, &keystr1)) {
        keystr2 = token_rdi1;
        while (token_rdi2 = strtok_r(keystr2, delim2, &keystr2)) {
            if (NULL != strstr(token_rdi2, drone_o4_type[0])) {
                rdi_scan_start = 1;
            }

            if (rdi_scan_start == 1) {
                //printf("==>:[%d]  =ks2=>%s\n", index1, token_rdi2);
                if (NULL != strstr(token_rdi2, drone_o4_type[9])) {
                    rdi_scan_finish = 1;
                }

                if (index1 < DJI_O4_RDI_COUNT) {
                    strcpy(Data[index1], token_rdi2);
                    index1 = index1 + 1;
                }
            }
        }

        if (rdi_scan_finish == 1) {
            break;
        }
    }

    while (index2 < index1) {
        if (index2 >= DJI_O4_RDI_COUNT) {
            break;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[0])) {
            strcpy(g_RhhkReadRdiO4Info[ActO4Index].rdi_ssid, Data[index2 + 1]);
            //printf("---ssid:%s\n", Data[index2+1]);
            index2 = index2 + 2;
            continue;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[1])) {
            strcpy(g_RhhkReadRdiO4Info[ActO4Index].drone_serial_num, Data[index2 + 1]);
            //printf("---serial:%s\n", Data[index2+1]);
            index2 = index2 + 2;
            continue;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[2])) {
            strcpy(g_RhhkReadRdiO4Info[ActO4Index].product_type_str, Data[index2 + 1]);
            //printf("---%s\n", Data[index2+1]);
            index2 = index2 + 2;
            continue;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[3])) {
            g_RhhkReadRdiO4Info[ActO4Index].UA_type = atoi(Data[index2 + 1]);
            //printf("---:%s\n", Data[index2+1]);
            index2 = index2 + 2;
            continue;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[4])) {
            g_RhhkReadRdiO4Info[ActO4Index].drone_longtitude = strtod(Data[index2 + 1], NULL);
            //printf("---:%s\n", Data[index2+1]);
            g_RhhkReadRdiO4Info[ActO4Index].drone_latitude = strtod(Data[index2 + 2], NULL);
            //printf("---:%s\n", Data[index2+2]);
            index2 = index2 + 3;
            continue;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[5])) {
            g_RhhkReadRdiO4Info[ActO4Index].pilot_longtitude = strtod(Data[index2 + 1], NULL);
            //printf("---:%s\n", Data[index2+1]);
            g_RhhkReadRdiO4Info[ActO4Index].pilot_latitude = strtod(Data[index2 + 2], NULL);
            //printf("---:%s\n", Data[index2+2]);
            index2 = index2 + 3;
            continue;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[6])) {
            g_RhhkReadRdiO4Info[ActO4Index].drone_Hspeed = atof(Data[index2 + 1]);
            //printf("---:%s\n", Data[index2+1]);
            index2 = index2 + 2;
            continue;
        }

        if (NULL != strstr(Data[index2], drone_o4_type[7])) {
            g_RhhkReadRdiO4Info[ActO4Index].drone_Vspeed = atof(Data[index2 + 1]);
            //printf("---:%s\n", Data[index2+1]);
            index2 = index2 + 2;
            continue;
        }
        if (NULL != strstr(Data[index2], drone_o4_type[8])) {
            g_RhhkReadRdiO4Info[ActO4Index].direction = atoi(Data[index2 + 1]);
            //printf("---:%s\n", Data[index2+1]);
            index2 = index2 + 2;
            continue;
        }
        if (NULL != strstr(Data[index2], drone_o4_type[9])) {
            g_RhhkReadRdiO4Info[ActO4Index].altitude = atoi(Data[index2 + 1]);
            //printf("---rdi finish:%s\n", Data[index2+1]);
            break;
        }
    }
    g_RhhkReadRdiO4Info[ActO4Index].rdi_packet_valid = 1;

    Dbg_Level_Print(LOG_INFO, "======Parse_DjiO4Broadcast: Act04Index=[%d]======\n", ActO4Index);

    //translate rdi O4 droneinfo to online drone format
    RemoveRdiHeader((char *) g_RhhkReadRdiO4Info[ActO4Index].drone_serial_num,
                    sizeof(g_RhhkReadRdiO4Info[ActO4Index].drone_serial_num));
    RemoveRdiHeader((char *) g_RhhkReadRdiO4Info[ActO4Index].product_type_str,
                    sizeof(g_RhhkReadRdiO4Info[ActO4Index].product_type_str));


    if (ActO4Index < DJI_O4_RDI_MAX_COUNT) {
        Dbg_Level_Print(LOG_INFO, "Driver_Index:%d\n", ActO4Index);
        Dbg_Level_Print(LOG_INFO, "rdi_ssid:%s\n", g_RhhkReadRdiO4Info[ActO4Index].rdi_ssid);
        Dbg_Level_Print(LOG_INFO, "drone_serial_num:%s\n", g_RhhkReadRdiO4Info[ActO4Index].drone_serial_num);
        Dbg_Level_Print(LOG_INFO, "product_type_str:%s\n", g_RhhkReadRdiO4Info[ActO4Index].product_type_str);
        Dbg_Level_Print(LOG_INFO, "UA_type:%d\n", g_RhhkReadRdiO4Info[ActO4Index].UA_type);
        Dbg_Level_Print(LOG_INFO, "drone_longtitude:%lf\n", g_RhhkReadRdiO4Info[ActO4Index].drone_longtitude);
        Dbg_Level_Print(LOG_INFO, "drone_latitude:%lf\n", g_RhhkReadRdiO4Info[ActO4Index].drone_latitude);
        Dbg_Level_Print(LOG_INFO, "pilot_longtitude:%lf\n", g_RhhkReadRdiO4Info[ActO4Index].pilot_longtitude);
        Dbg_Level_Print(LOG_INFO, "pilot_latitude:%lf\n", g_RhhkReadRdiO4Info[ActO4Index].pilot_latitude);
        Dbg_Level_Print(LOG_INFO, "drone_Hspeed:%f\n", g_RhhkReadRdiO4Info[ActO4Index].drone_Hspeed);
        Dbg_Level_Print(LOG_INFO, "drone_Vspeed:%f\n", g_RhhkReadRdiO4Info[ActO4Index].drone_Vspeed);
        Dbg_Level_Print(LOG_INFO, "direction:%d\n", g_RhhkReadRdiO4Info[ActO4Index].direction);
        Dbg_Level_Print(LOG_INFO, "Altitude:%f\n", g_RhhkReadRdiO4Info[ActO4Index].altitude);
    }

    return 0;
}

/*******************************************************************************
* ��������: Get_ValidRdiData
* ��������: ��ȡ������Ч��
* ��������:
* ��������: 		  ����		            ����/���	 ����
* pRcvBuffer           char*                 ���� 		 ���ݰ�ָ��
* pOutputValidData     char*                 ��� 		 ���ݰ�ָ��

* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Get_ValidRdiData(char *pRcvBuffer, char *pOutputValidData) {
    char *current_line = pRcvBuffer;
    char utc[100];
    char *cmd_str = NULL;
    int invalid_result = -1;
    int valid_result = 0;
    int type_index = 0;
    char *pstart_pos = NULL;
    char *pend_pos = NULL;
    int valid_data_len = 0;
    const char *drone_o4_type[13] = {
        "RID ssid", "serial", "model", " UA_type", "drone_GPS", "pilot_GPS", "speed", "Vspeed", "direc", "Altitude",
        "MAC", "rssi", "freq"
    };

    if ((pRcvBuffer == NULL) || (pOutputValidData == NULL)) {
        Dbg_Level_Print(LOG_ERROR, "Get_ValidRdiData input RcvBuffer is Null, exit\n");
        return invalid_result;
    }

    char *line = strtok(pRcvBuffer, "\n");
    while (line != NULL) {
        char *equal_sign = strstr(line, "RID ssid=");
        if (NULL != equal_sign) {
            type_index = 0;
            while (type_index < 13) {
                if (NULL == strstr(line, drone_o4_type[type_index++])) {
                    //printf("[====not integreted====>][%d]\n", type_index);
                    break;
                }

                if (type_index >= 10) {
                    Dbg_Level_Print(LOG_INFO, "[====finish integreted====>][%d]\n", type_index);
                    valid_data_len = strlen(line);
                    valid_data_len = ((valid_data_len >= RDI_RCV_DATA_LENGTH) ? RDI_RCV_DATA_LENGTH : valid_data_len);
                    strncpy(pOutputValidData, line, valid_data_len);

                    return valid_result; //succeed
                }
            }
        }
        line = strtok(NULL, "\n");
    }

    return invalid_result; //failed
}

/*******************************************************************************
* ��������: Get_RdiData
* ��������: ��ȡ���˻��ؼ��ֶ���Ϣ
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Get_RdiData(int fd) {
    ssize_t Byte_Count = 0;
    const char *delim1 = ",";
    const char *delim2 = "=";
    char *token_rdi;
    char *token_value;
    char *token_gps;
    char *key, *value;
    char *skey, *svalue;
    char Data_Buffer[RDI_RCV_DATA_LENGTH];
    char Valid_Buffer[RDI_RCV_DATA_LENGTH];
    char *pValidDroneData = NULL;

    fd_set read_fds;
    struct timeval timeout;
    timeout.tv_sec = 1; // 1�볬ʱ
    timeout.tv_usec = 0;
    int ret;
    char *virtul_buf =
            "RID ssid=RID-1581F6Z9C24AB0033DCM, serial=1581F6Z9C24AB0033DCM, model=DJI Mini 4 pro, UA_type=2, drone_GPS=115.344780,40.383072, pilot_GPS=115.344773,40.383070, speed=0.0, Vspeed=0, direc=-181, Altitude=562.0, MAC=60:60:1f:25:32:26, rssi=-31, freq=2437";

    if (-1 == fd) {
        perror("Read RdiData Failed, fd Is InValid!");
        return (-1);
    }
    Rhhk_SetDebugData(2, 0, 1, NULL, 0);

    while (1) {
        memset(Data_Buffer, 0, RDI_RCV_DATA_LENGTH * sizeof(char));
        memset(Valid_Buffer, 0, RDI_RCV_DATA_LENGTH * sizeof(char));

        FD_ZERO(&read_fds);
        FD_SET(fd, &read_fds);
        ret = select(fd + 1, &read_fds, NULL, NULL, &timeout);
        if (ret > 0 && FD_ISSET(fd, &read_fds)) {
            Byte_Count = read(fd, (void *) Data_Buffer, RDI_RCV_DATA_LENGTH);
            if (Byte_Count <= 0) {
                Dbg_Level_Print(LOG_INFO, "<0>$$$$$$$$$$Reading RdiData is valid (fd:%d, nbyte:%d, content:%s)!\n\n",
                                fd, Byte_Count, Data_Buffer);
                continue;
            }
        }
        Rhhk_SetDebugData(2, 1, 1, NULL, 0);
        Rhhk_SetDebugData(2, 2, 1, NULL, 0);
        Rhhk_SetDebugData(2, 3, 1, Data_Buffer, Byte_Count);

        if (g_debug_switch == 125) {
            Dbg_Level_Print(LOG_INFO, "<debug>$$$$$$$$$$[KEY-READ-RID] Simulate RID Message:%d!\n", g_debug_switch);
            memset(Valid_Buffer, 0, RDI_RCV_DATA_LENGTH * sizeof(char));
            strcpy(Data_Buffer, virtul_buf);
        }

        Dbg_Level_Print(LOG_INFO, "<1>$$$$$$$$$$[KEY-READ-RID](fd:%d, size:%d), Read content:%s!\n", fd, Byte_Count,
                        Data_Buffer);
        if (0 == Get_ValidRdiData((char *) Data_Buffer, (char *) Valid_Buffer)) {
            Parse_DjiO4Broadcast((char *) Valid_Buffer);
            Valid_Buffer[RDI_RCV_DATA_LENGTH - 1] = '\0';
            Dbg_Level_Print(LOG_INFO, "<2>$$$$$$$$$$[KEY-READ-RID](fd:%d, size:%d), Parse Valid:%s!\n", fd, Byte_Count,
                            Valid_Buffer);
        }
        sleep(1);
    }
    return 0;
}

/*******************************************************************************
* ��������: Read_Time
* ��������: ����ʱ���ַ���
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* postion             int                   ����         �ؼ�������
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Read_Time(char *str, int position) {
    int length = strlen(str);
    char buffer[2];
    int value = 0;
    if (position < 0 || position >= length) {
        Dbg_Level_Print(LOG_INFO, "Position out of bounds.\n");
        return 0;
    }

    if (position + 1 >= length) {
        Dbg_Level_Print(LOG_INFO, "Only one character is available at this position.\n");
        Dbg_Level_Print(LOG_INFO, "Character at position %d: '%c'\n", position, str[position]);
        return 0;
    }

    // 读取和打印两个字�?
    memset(buffer, 0, 2 * sizeof(char));
    sprintf(buffer, "%c%c", str[position], str[position + 1]);
    value = atoi(buffer);
    return value;
}

/*******************************************************************************
* ��������: Real_GpsCoordinate
* ��������: ����gps����
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
float Real_GpsCoordinate(char *src, int start, int len) {
    float fGpsData;
    int src_len = strlen(src);

    if (start >= src_len || len <= 0) {
        return -1;
    }

    char *dest_buf = (char *) malloc(len + 1);
    if (!dest_buf) {
        return -1;
    }

    strncpy(dest_buf, src + start, len);
    dest_buf[len] = '\0';

    fGpsData = atof(dest_buf);

    free(dest_buf);
    return fGpsData;
}


time_t get_system_time() {
    struct timeval tv;
    if (gettimeofday(&tv, NULL) < 0) {
        Dbg_Level_Print(LOG_ERROR, "Failed to get system time\n");
        return (time_t) -1;
    }
    return tv.tv_sec;
}

/*******************************************************************************
* ��������: Parse_GpsBuffer
* ��������: ����gps
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Parse_GpsBuffer(char *GpsBuffer) {
    const char *delim1 = ",";
    char *token_rdi1;
    char *keystr1;
    int GpsIndex = 0;
    int index = 0;
    int max_gps_text = 13;
    char Data[4][50];
    int year, month, day, hour, min, second;
    /*$GNRMC,071653.00,A,3955.36644,N,11609.36263,E,0.007,,100325,,,A*6E*/
    char *strNeedle = "$GNRMC";
    char *targetBuffer;
    struct tm act_time = {0};
    struct timeval tv;
    time_t timesec = 0;
    float fDegrees = 0;
    float fMinutes = 0;
    int rdi_settime_valid = -1;
    int time_zone = 0;

    FILE *time_file;
    char buffer[20];
    time_t file_time = 0;
    time_t latest_time = 0;

    if (GpsBuffer == NULL) {
        return -1;
    }

    memset(Data, 0, 2 * 50 * sizeof(char));
    targetBuffer = strtok(GpsBuffer, "\n");
    while (targetBuffer != NULL) {
        if (NULL != strstr(targetBuffer, strNeedle)) {
            Dbg_Level_Print(LOG_INFO, "1====>%s\n", targetBuffer);
            keystr1 = (char *) targetBuffer;
            while (token_rdi1 = strtok_r(keystr1, delim1, &keystr1)) {
                Dbg_Level_Print(LOG_INFO, "4====>%s\n", token_rdi1);
                if ((index < max_gps_text) && (index == 1)) {
                    strcpy(Data[0], token_rdi1);
                    rdi_settime_valid += 1;
                    Dbg_Level_Print(LOG_INFO, "Data[0]##########:%s\n", Data[0]);
                }

                if ((index < max_gps_text) && (index == 3)) {
                    strcpy(Data[2], token_rdi1);
                    rdi_settime_valid += 1;
                    Dbg_Level_Print(LOG_INFO, "Data[2]##########:%s\n", Data[2]);
                }

                if ((index < max_gps_text) && (index == 5)) {
                    strcpy(Data[3], token_rdi1);
                    rdi_settime_valid += 1;
                    Dbg_Level_Print(LOG_INFO, "Data[3]##########:%s\n", Data[3]);
                }


                if ((index < max_gps_text) && (index == 8)) {
                    if (strchr(token_rdi1, '.') == NULL) {
                        strcpy(Data[1], token_rdi1);
                        rdi_settime_valid += 1;
                        Dbg_Level_Print(LOG_INFO, "Data8[1]##########:%s\n", Data[1]);
                    }
                }

                if ((index < max_gps_text) && (index == 9)) {
                    if (rdi_settime_valid <= 2) {
                        strcpy(Data[1], token_rdi1);
                        rdi_settime_valid += 1;
                        Dbg_Level_Print(LOG_INFO, "Data9[1]##########:%s\n", Data[1]);
                    }
                }
                index++;
            }
            break;
        }

        targetBuffer = strtok(NULL, "\n");
    }

    Dbg_Level_Print(LOG_INFO, "settimeofday is successful(%lld)\n", timesec);

    fDegrees = Real_GpsCoordinate((char *) Data[2], 0, 2);
    fMinutes = Real_GpsCoordinate((char *) Data[2], 2, (strlen(Data[2]) - 2));
    if ((fDegrees != -1) && (fMinutes != -1)) {
        g_local_latitude = fDegrees + fMinutes / 60;
        Dbg_Level_Print(LOG_INFO, "(1)degrees:%f,minutes:%f, g_local_latitude:%f\n", fDegrees, fMinutes,
                        g_local_latitude);
    }

    fDegrees = Real_GpsCoordinate((char *) Data[3], 0, 3);
    fMinutes = Real_GpsCoordinate((char *) Data[3], 3, (strlen(Data[3]) - 2));
    if ((fDegrees != -1) && (fMinutes != -1)) {
        g_local_longitude = fDegrees + fMinutes / 60;
        Dbg_Level_Print(LOG_INFO, "(2)degrees:%f,minutes:%f, g_local_longitude:%f\n", fDegrees, fMinutes,
                        g_local_longitude);
    }

    Dbg_Level_Print(LOG_INFO, "Real_GpsCoordinate@@@@@@: g_local_latitude:%f, g_local_longitude:%f\n", g_local_latitude,
                    g_local_longitude);

    if ((abs(g_local_longitude) < 1e-9) && (abs(g_local_latitude) < 1e-9)) {
        Get_ManuLocalGpsInfo(&g_local_longitude, &g_local_latitude, &g_local_altitude);
        Dbg_Level_Print(LOG_INFO, "[Parse_GpsBuffer@@@@@@]rdi manu local gps info: lon: %f, lat:%f\n",
                        g_local_longitude, g_local_latitude);
    }

    hour = Read_Time(Data[0], 0);
    min = Read_Time(Data[0], 2);
    second = Read_Time(Data[0], 4);
    day = Read_Time(Data[1], 0);
    month = Read_Time(Data[1], 2);
    year = Read_Time(Data[1], 4);

    time_zone = floor((g_local_longitude + 7.5) / 15);
    Dbg_Level_Print(LOG_INFO, "\nGetGpsTime is %s:%s, time_zone:%d!\n", Data[1], Data[0], time_zone);

    memset(&act_time, 0, sizeof(act_time));
    act_time.tm_year = year + 2000 - 1900;
    act_time.tm_mon = month - 1;
    act_time.tm_mday = day;
    act_time.tm_hour = hour + time_zone;
    act_time.tm_min = min;
    act_time.tm_sec = second;
    act_time.tm_isdst = -1;

    Dbg_Level_Print(LOG_INFO, "GetGpsTime is %d-%d-%d %d:%d:%d\n", act_time.tm_year, act_time.tm_mon, act_time.tm_mday,
                    act_time.tm_hour, act_time.tm_min, act_time.tm_sec);
    timesec = mktime(&act_time);
    tv.tv_sec = timesec;
    tv.tv_usec = 0;

    /***************************************************************************/
    if ((time_file = fopen(TIME_FILE_PATH, "r")) != NULL) {
        struct tm file_tm = {0};
        if (fgets(buffer, sizeof(buffer), time_file)) {
            sscanf(buffer, "%d-%d-%d %d:%d:%d",
                   &file_tm.tm_year,
                   &file_tm.tm_mon,
                   &file_tm.tm_mday,
                   &file_tm.tm_hour,
                   &file_tm.tm_min,
                   &file_tm.tm_sec);
            file_tm.tm_year -= 1900;
            file_tm.tm_mon -= 1;
            file_time = mktime(&file_tm);
            tv.tv_sec = file_time;
            tv.tv_usec = 0;
            Dbg_Level_Print(LOG_INFO, "Time file read with file time[%llu]\n", file_time);
        }
        fclose(time_file);
    }

    time_t system_time = get_system_time();
    if (system_time == (time_t) -1) {
        Dbg_Level_Print(LOG_INFO, "Failed to get system time\n");
    } else {
        Dbg_Level_Print(LOG_INFO, "Time system read with file time[%llu]\n", system_time);
    }
    /***************************************************************************/
    if ((rdi_settime_valid >= 3) && ((hour > 0) && (min > 0) && (second > 0) && (day > 0) && (month > 0) && (
                                         year > 0))) {
        if (difftime(timesec, file_time) > 0) {
            latest_time = timesec;
            Dbg_Level_Print(LOG_INFO, "GpsValid settimeofday is update[%llu]!\n", latest_time);

            tv.tv_sec = latest_time;
            tv.tv_usec = 0;
            if (settimeofday(&tv, NULL) < 0) {
                Dbg_Level_Print(LOG_INFO, "GpsValid settimeofday is failed\n");
                return -1;
            }
            Dbg_Level_Print(LOG_INFO, "GpsValid settimeofday is success!\n");

            time_file = fopen(TIME_FILE_PATH, "w");
            if (time_file != NULL) {
                fprintf(time_file, "%d-%d-%d %d:%d:%d\n",
                        act_time.tm_year + 1900,
                        act_time.tm_mon + 1,
                        act_time.tm_mday,
                        act_time.tm_hour,
                        act_time.tm_min,
                        act_time.tm_sec);
                fclose(time_file);
                Dbg_Level_Print(LOG_INFO, "Time file created with GPS time\n");
            }
            return 0;
        }
    }
    /***************************************************************************/
    if ((difftime(file_time, system_time) > 0)) {
        latest_time = file_time;
        Dbg_Level_Print(LOG_INFO, "FileValid settimeofday is update[%llu]!\n", latest_time);
    } else {
        latest_time = system_time;
        Dbg_Level_Print(LOG_INFO, "SystemValid settimeofday is update[%llu]!\n", latest_time);
        tv.tv_sec = latest_time;
        tv.tv_usec = 0;
        // ת��Ϊ����ʱ��ṹ��
        struct tm *local_time = localtime(&tv.tv_sec);
        Dbg_Level_Print(LOG_INFO, "UTCʱ�䣺%04d-%02d-%02d %02d:%02d:%02d\n",
                        local_time->tm_year + 1900,
                        local_time->tm_mon + 1,
                        local_time->tm_mday,
                        local_time->tm_hour,
                        local_time->tm_min,
                        local_time->tm_sec);

        time_file = fopen(TIME_FILE_PATH, "w");
        if (time_file != NULL) {
            fprintf(time_file, "%d-%d-%d %d:%d:%d\n",
                    local_time->tm_year + 1900,
                    local_time->tm_mon + 1,
                    local_time->tm_mday,
                    local_time->tm_hour,
                    local_time->tm_min,
                    local_time->tm_sec);
            fclose(time_file);
            Dbg_Level_Print(LOG_INFO, "FileValid or SystemValid  settimeofday write file successful!\n");
        }
    }

    tv.tv_sec = latest_time;
    tv.tv_usec = 0;
    if (settimeofday(&tv, NULL) < 0) {
        Dbg_Level_Print(LOG_INFO, "FileValid or SystemValid settimeofday is failed\n");
        return -1;
    }

    /***************************************************************************/
    Dbg_Level_Print(LOG_INFO, "@@@@settimeofday update is Finished!\n");
    return 0;
}

/*******************************************************************************
* ��������: Sync_GpsTime
* ��������: ͬ����ȡgps����
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Sync_GpsTime(int fd) {
    ssize_t Byte_Count = 0;
    int ReadCount = 0;
    const char *delim1 = ",";
    const char *delim2 = "=";
    char *token_rdi;
    char *token_value;
    char *token_gps;
    char *key, *value;
    char *skey, *svalue;
    char Data_Buffer[RDI_RCV_DATA_LENGTH];
    int fd_gps;
    char pActualBrd_1[] = "B2";
    T_RhhkMangerConfArg OutBoardInfo;

    if (-1 == fd) {
        Dbg_Level_Print(LOG_ERROR, "Read GpsTime Failed, fd Is InValid!");
    }

    while (1) {
        if (fd < 0) {
            fd_gps = Open_Port(1);
            if (fd_gps < 0) {
                usleep(2000);
                continue;
            }
            fd = fd_gps;
            Dbg_Level_Print(LOG_INFO, "Sync_GpsTime Open_Port(1), fd = %d!\n", fd);

            memset(&OutBoardInfo, 0, sizeof(T_RhhkMangerConfArg));
            if (0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *) &OutBoardInfo)) {
                Dbg_Level_Print(LOG_INFO, "PROCESS START AND GET CONFIGURATION INFO!\n");
            }

            Dbg_Level_Print(LOG_INFO, "Sync_GpsTime (Second Open) RequireBrd:%s, ActualBrd:%s\n", OutBoardInfo.brd_code,
                            pActualBrd_1);
            if (0 == strncmp(OutBoardInfo.brd_code, pActualBrd_1, sizeof(pActualBrd_1))) {
                Set_Option(fd, 9600, 8, 'N', 1);
            } else {
                Set_Option(fd, 115200, 8, 'N', 1);
            }
        }

        memset(Data_Buffer, 0, RDI_RCV_DATA_LENGTH * sizeof(char));
        Rhhk_SetDebugData(4, 0, 1, NULL, 0);
        Rhhk_SetDebugData(4, 1, 1, NULL, 0);
        Rhhk_SetDebugData(4, 2, 1, NULL, 0);

        Byte_Count = read(fd, (void *) Data_Buffer, RDI_RCV_DATA_LENGTH);
        if ((Byte_Count <= 0) || (NULL == strstr(Data_Buffer, "$GNRMC"))) {
            if (ReadCount >= 5) {
                Dbg_Level_Print(LOG_INFO, "Reading GpsTime Wrong(nByte:%d)(nCount:%d)!\n", Byte_Count, ReadCount);
                ReadCount = 0;
            }
            sleep(5);
            ReadCount++;
        } else {
            Dbg_Level_Print(LOG_INFO, "========Sync_GpsTime:As Follow Start========\n");
            Dbg_Level_Print(LOG_INFO, "%s\n", Data_Buffer);
            Dbg_Level_Print(LOG_INFO, "========Get_GpsTme:As Follow End========\n");

            Rhhk_SetDebugData(4, 3, 1, Data_Buffer, Byte_Count);
            if (0 == Parse_GpsBuffer((char *) Data_Buffer)) {
                Dbg_Level_Print(LOG_INFO, "Sync_GpsTime Finished!\n");
            }
            sleep(5);
            ReadCount = 0;
        }
    }
    return 0;
}

/*******************************************************************************
* ��������: Handle_ReadDroneO4FromRdiFunc
* ��������: ��gpsģ�������
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Handle_ReadDroneO4FromRdiFunc(void *strArg) {
    Dbg_Level_Print(LOG_INFO, "3 pthread read o4 packet from rdi driver!\n");
    int fd;
    fd = Open_Port(0);
    Set_Option(fd, 115200, 8, 'N', 1);
    Get_RdiData(fd);
    pthread_exit(NULL);
    return;
}

/*******************************************************************************
* ��������: Set_LocalTime
* ��������: ���ض�ȡgspģ��
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Set_LocalTime(void) {
    int fd_gps;
    fd_gps = Open_Port(1);
    Set_Option(fd_gps, 115200, 8, 'N', 1);
    Sync_GpsTime(fd_gps);
    return;
}

/*******************************************************************************
* ��������: Handle_LocalGpsTime
* ��������: ����gps�����߳�
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
int Handle_LocalGpsTime(void *strArg) {
    int fd_gps = 0;
    if (strArg == NULL) {
        Dbg_Level_Print(LOG_INFO, "Receive Null Point(strArg)!\n");
        return -1;
    }

    fd_gps = *(int *) strArg;

    Sync_GpsTime(fd_gps);
    Dbg_Level_Print(LOG_INFO, "Handle_LocalGpsTime input argument fd_gps is %d(valid)\n", fd_gps);

    return 0;
}

/*******************************************************************************
* ��������: CreatLocalGpsPthread
* ��������: ����gps�����߳�
* ��������:
* ��������: 		  ����		            ����/���	 ����
* RdiBuffer           char*                 ���� 		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
void CreatLocalGpsPthread(void) {
    pthread_t tm_rid;
    int ret;
    int fd_gps;
    int try_open_gps_count = 0;
    char pActualBrd_1[] = "B2";
    T_RhhkMangerConfArg OutBoardInfo;

    while (try_open_gps_count < 10) {
        fd_gps = Open_Port(1);
        if ((fd_gps > 0)) {
            break;
        }
        usleep(200000);
        Dbg_Level_Print(LOG_INFO, "CreatLocalGpsPthread Open_Port(1), fd_gps = %d,  try %d times!\n", fd_gps,
                        try_open_gps_count);
        try_open_gps_count++;
    }

    memset(&OutBoardInfo, 0, sizeof(T_RhhkMangerConfArg));
    if (0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *) &OutBoardInfo)) {
        Dbg_Level_Print(LOG_INFO, "PROCESS START AND GET CONFIGURATION INFO!\n");
    }

    Dbg_Level_Print(LOG_INFO, "CreatLocalGpsPthread (First Open) RequireBrd:%s, ActualBrd:%s\n", OutBoardInfo.brd_code,
                    pActualBrd_1);
    if (0 == strncmp(OutBoardInfo.brd_code, pActualBrd_1, sizeof(pActualBrd_1))) {
        Set_Option(fd_gps, 9600, 8, 'N', 1);
    } else {
        Set_Option(fd_gps, 115200, 8, 'N', 1);
    }

    //pthread_mutex_init(&mutex_tid, NULL);
    Dbg_Level_Print(LOG_INFO, "1 create pthread for Local Gps Handwith start!\n");
    ret = pthread_create(&tm_rid, NULL, Handle_LocalGpsTime, (void *) &fd_gps);
    if (ret != 0) {
        perror("CreatLocalGpsPthread:Pthread_Create Failed!");
        return;
    }
    pthread_detach(tm_rid);
    Dbg_Level_Print(LOG_INFO, "2 create pthread for CreateLocalGpsPthread finished!\n");

    //pthread_mutex_destroy(&mutex_tid);
    return 0;
}

/*******************************************************************************
* ��������: CreatRdiPthread
* ��������: ��������rdiģ�鴦��O4�߳�
* ��������:
* ��������: 		  ����					����/���	 ����
* RdiBuffer 		  char* 				����		 ���ݰ�ָ��
* ����ֵ:  int
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/
void CreatRdiPthread(void) {
    pthread_t tid_rid;
    int sockfd = 0; //invalid, after upgrade
    int ret;

    //pthread_mutex_init(&mutex_tid, NULL);

    if (sockfd < 0) {
        perror("accept sockfd is failed.\n");
        exit(EXIT_FAILURE);
    }
    Dbg_Level_Print(LOG_INFO, "1 create pthread for rdi start!\n");
    ret = pthread_create(&tid_rid, NULL, Handle_ReadDroneO4FromRdiFunc, (void *) &sockfd);
    if (ret != 0) {
        perror("StartDroneScanTask:Pthread_Create Failed!");
        return;
    }
    pthread_detach(tid_rid);
    Dbg_Level_Print(LOG_INFO, "2 create pthread for rdi finished!\n");

    //pthread_join(tid_rid, NULL);
    //pthread_mutex_destroy(&mutex_tid);
    return;
}
