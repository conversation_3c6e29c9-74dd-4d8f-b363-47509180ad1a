/*******************************************************************************
* COPYRIGHT RHHK  CO.,LTD
********************************************************************************
* �ļ�����: hl_caps_manage.cpp
* ��������: RHHK 配置操作和报告生�?
* �������:
* ��    ��: zonghui
* ����ʱ��: 204/12/20
* �޸�����    �޸���  BugID/CRID      �޸�����
* ------------------------------------------------------------------------------
* 2024/12/20  zonghui    �����ļ�
*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
//#include "NetInterface.h"
#include "hl_caps_report.h"
/******************************* 局部宏定义 ***********************************/
#define CONFIGSTRUCTSIZE 80
#define CONFIGNAMESIZE   32
#define CONFIGCONTENTSIZE 512

/******************************* 局部常数和类型定义 ***************************/
struct ConfigStruct {
    char ItemName[CONFIGNAMESIZE];
    char ItemContent[CONFIGCONTENTSIZE];
};

extern int GetConfigIntDefault(const char *p_itemname, int def);

static int arr_curr_ind = 0;
static struct ConfigStruct ArrayConfig[CONFIGSTRUCTSIZE];

/*******************************************************************************
* ��������: RHHK_Rtrim
* ��������: ɾ���������ַ�����
* ��������:
* ��������: 		����			����/���	   ����
* sstring			char*			����		   �ַ���������ַ
* ����ֵ: ��
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
void RHHK_Rtrim(char *sstring) {
    ssize_t len = 0;
    if (sstring == NULL)
        return;
    len = strlen(sstring);
    while (len > 0 && sstring[len - 1] == ' ')
        sstring[--len] = 0;
}

/*******************************************************************************
* ��������: RHHK_Ltrim
* ��������: ɾ���������ַ�����
* ��������:
* ��������: 		����			����/���	   ����
* sstring			char*			����		   �ַ���������ַ
* ����ֵ: ��
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
void RHHK_Ltrim(char *sstring) {
    char *p_tmp = sstring;
    ssize_t len = 0;
    if (sstring == NULL)
        return;
    if (*p_tmp != ' ') //没有空格直接退�?
        return;
    //找到第一个不为空格位�?
    while (*p_tmp != '\0') {
        if (*p_tmp == ' ')
            p_tmp++;
        else
            break;
    }
    if ((*p_tmp) == '\0') //全空�?
    {
        *sstring = '\0';
        return;
    }
    char *p_tmp2 = sstring; //p_tmp2 指向 sstring
    while ((*p_tmp) != '\0') {
        (*p_tmp2) = (*p_tmp);
        p_tmp++;
        p_tmp2++;
    }
    (*p_tmp2) = '\0'; //空格处理结束，末尾添加‘\0�? 表示字符串结�?
    return;
}

/*******************************************************************************
* ��������: RHHK_GetConfigIntdefault
* ��������: get config key value
* ��������:
* ��������: 		����			����/���	   ����
* p_itemname    	const char*		����		   Ŀ��ؼ�������
* def               int             ����           ȱʡֵ
* ����ֵ: ��
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int RHHK_GetConfigIntDefault(const char *p_itemname, int def) {
    int i = 0;
    for (i = 0; i < arr_curr_ind; i++) {
        if (strcmp(p_itemname, ArrayConfig[i].ItemName) == 0) {
            return atoi(ArrayConfig[i].ItemContent);
        }
    }
    return def;
}


/*******************************************************************************
* ��������: RHHK_GetConfigFloatDefault
* ��������: get config key value
* ��������:
* ��������: 		����			����/���	   ����
* p_itemname    	const char*		����		   Ŀ��ؼ�������
* def               float             ����           ȱʡֵ
* ����ֵ: ��
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
float RHHK_GetConfigFloatDefault(const char *p_itemname, float def) {
    char *endptr;
    float real_float_val = 0;
    int i = 0;
    for (i = 0; i < arr_curr_ind; i++) {
        if (strcmp(p_itemname, ArrayConfig[i].ItemName) == 0) {
            real_float_val = strtof(ArrayConfig[i].ItemContent, &endptr);
            return real_float_val;
        }
    }
    return def;
}

/*******************************************************************************
* ��������: RHHK_LoadConfigFile
* ��������: ���������ļ�
* ��������:
* ��������: 	����			����/���	   ����
* pconfName		const char* 	����		   Ŀ��ؼ�������
* ����ֵ: ��
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int RHHK_LoadConfigFile(const char *pconfName) {
    char sign[2] = "=";
    char *token;
    arr_curr_ind = 0;

    if (arr_curr_ind > 0) {
        Dbg_Level_Print(LOG_INFO, "######RHHK_LoadConfigFile######[arr_curr_ind=%d]!\n", arr_curr_ind);
        return -1;
    }

    FILE *fp;
    fp = fopen(pconfName, "r");
    if (fp == NULL) {
        Dbg_Level_Print(LOG_INFO, "######RHHK_LoadConfigFile######[pconfName=%s]!\n", pconfName);
        return -1;
    }
    unsigned int line_size = CONFIGCONTENTSIZE + 1 + CONFIGNAMESIZE + 1 + 1;
    char linebuf[line_size];
    memset(linebuf, 0, sizeof(linebuf));
    while (!feof(fp)) {
        if (fgets(linebuf, line_size, fp) == NULL)
            continue;
        if (linebuf[0] == 0)
            continue;
        if (*linebuf == ';' || *linebuf == ' ' || *linebuf == '#'
            || *linebuf == '\t' || *linebuf == '\n')
            continue;

        //去除字符串\r \n 及空�?
    procstringjmp:
        if (strlen(linebuf) > 0) {
            if (linebuf[strlen(linebuf) - 1] == 10
                || linebuf[strlen(linebuf) - 1] == 13
                || linebuf[strlen(linebuf) - 1] == 32) {
                linebuf[strlen(linebuf) - 1] = 0;
                goto procstringjmp;
            }
        }

        if (linebuf[0] == 0)
            continue;
        if (*linebuf == '[') // [开头的注释，也保存，方便以后回�?
        {
            if (arr_curr_ind < CONFIGSTRUCTSIZE) {
                strcpy(ArrayConfig[arr_curr_ind].ItemName, linebuf);
                strcpy(ArrayConfig[arr_curr_ind].ItemContent, " ");
                arr_curr_ind += 1;
            } else {
            }
            continue;
        }

        //到这里，都是合法的配置项
        char *ptmp = strchr(linebuf, '=');
        if (ptmp != NULL) {
            if (arr_curr_ind < CONFIGSTRUCTSIZE) {
                token = strtok(linebuf, sign);
                strcpy(ArrayConfig[arr_curr_ind].ItemName, token);
                strcpy(ArrayConfig[arr_curr_ind].ItemContent, ptmp + 1);
                RHHK_Rtrim(ArrayConfig[arr_curr_ind].ItemName);
                RHHK_Ltrim(ArrayConfig[arr_curr_ind].ItemName);
                RHHK_Rtrim(ArrayConfig[arr_curr_ind].ItemContent);
                RHHK_Ltrim(ArrayConfig[arr_curr_ind].ItemContent);
                arr_curr_ind += 1;
            } else {
            }
        }
    }

    fclose(fp);
    return 0;
}

/*******************************************************************************
* ��������: RHHK_GetConfigFromString
* ��������: ��ȡ����ֵ
* ��������:
* ��������: 	����			����/���	   ����
* pconfName 	const char* 	����		   Ŀ��ؼ�������
* pDefaultIP    const char*     ����           Ŀ��ȱʡIP
* ����ֵ: ��
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
const char *RHHK_GetConfigFromString(const char *p_itemname, const char *pDefault) {
    int i = 0;
    for (i = 0; i < arr_curr_ind; i++) {
        if (strcmp(p_itemname, ArrayConfig[i].ItemName) == 0) {
            return ArrayConfig[i].ItemContent;
        }
    }
    return pDefault;
}

/*******************************************************************************
* ��������: RHHK_GetConfigStructArg
* ��������: ��ȡ����ֵ
* ��������:
* ��������: 	    ����			        ����/���	   ����
* OutangerConfArg 	T_RhhkMangerConfArg* 	���		   ���ýṹ����
* ����ֵ: ��
* ����˵��:
*
* �޸�����	  �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int RHHK_GetConfigStructArg(T_RhhkMangerConfArg *OutangerConfArg) {
    const char *config_file = "/home/<USER>/config/platform.conf";
    const char *pTCPIP = "***************";
    if (0 != RHHK_LoadConfigFile(config_file)) {
        Dbg_Level_Print(LOG_INFO, "######OpenConfigFile###### is faiiled!!!\n");
        return -1;
    }
    const char *tcp_ip = RHHK_GetConfigFromString("TCPIP", pTCPIP);
    const int tcp_port = RHHK_GetConfigIntDefault("TCPPORT", 10085);
    const int debug_switch = RHHK_GetConfigIntDefault("DEBUG", 0);
    const double lon = RHHK_GetConfigFloatDefault("LON", 0.00000);
    const double lat = RHHK_GetConfigFloatDefault("LAT", 0.00000);
    const double alt = RHHK_GetConfigFloatDefault("ALT", 0.00000);
    const char *device_code = RHHK_GetConfigFromString("CODE", "MQ-2025-04-20-default");
    const char *CPU = RHHK_GetConfigFromString("CPU", "C1");
    const char *BRD = RHHK_GetConfigFromString("BRD", "B1");
    const char *PRD = RHHK_GetConfigFromString("PRD", "P1");
    const char *ol_ip = RHHK_GetConfigFromString("OLIP", "***************");
    const int ol_port = RHHK_GetConfigIntDefault("OLPORT", 5000);
    const char *username = RHHK_GetConfigFromString("USR", "rhhk");
    const char *password = RHHK_GetConfigFromString("PWD", "ksdhnewm239");
    const int pound_count = RHHK_GetConfigIntDefault("POUND", 0);
    const int pound_dji_count = RHHK_GetConfigIntDefault("PODJI", 0);
    const int usdelay = RHHK_GetConfigIntDefault("USDELAY", 0);

    if (OutangerConfArg != NULL) {
        strcpy(OutangerConfArg->tcp_ip, tcp_ip);
        OutangerConfArg->tcp_port = tcp_port;
        OutangerConfArg->debug_switch = debug_switch;
        OutangerConfArg->lon = lon;
        OutangerConfArg->lat = lat;
        OutangerConfArg->alt = alt;
        OutangerConfArg->ol_port = ol_port;
        OutangerConfArg->pound_count = pound_count;
        OutangerConfArg->pound_dji_count = pound_dji_count;
        OutangerConfArg->usdelay = usdelay;
        strncpy(OutangerConfArg->device_code, device_code, sizeof(OutangerConfArg->device_code));
        strncpy(OutangerConfArg->cpu_code, CPU, sizeof(OutangerConfArg->cpu_code));
        strncpy(OutangerConfArg->brd_code, BRD, sizeof(OutangerConfArg->brd_code));
        strncpy(OutangerConfArg->prd_code, PRD, sizeof(OutangerConfArg->prd_code));
        strncpy(OutangerConfArg->ol_ip, ol_ip, sizeof(OutangerConfArg->ol_ip));
        strncpy(OutangerConfArg->username, username, sizeof(OutangerConfArg->username));
        strncpy(OutangerConfArg->password, password, sizeof(OutangerConfArg->password));
        Dbg_Level_Print(LOG_INFO, " [TCP_SUCC]:%s:%d:%d\n", tcp_ip, tcp_port, debug_switch);
        Dbg_Level_Print(LOG_INFO, " [TCP_SUCC]:[LON:%lf], [LAT:%lf], [ALT:%lf]\n", lon, lat, alt);
        Dbg_Level_Print(LOG_INFO, " [TCP_SUCC]:[DEVICE_CODE:%s]\n", device_code);
        Dbg_Level_Print(LOG_INFO, " [TCP_SUCC]:[CPU_CODE:%s][BRD_CODE:%s][PRD_CODE:%s]\n", CPU, BRD, PRD);
        Dbg_Level_Print(LOG_INFO, " [TCP_SUCC]:[OLIP:%s][OLPORT:%d][USR:%s][PWD:%s]\n", OutangerConfArg->ol_ip,
                        OutangerConfArg->ol_port, username, password);
        Dbg_Level_Print(LOG_INFO, " [TCP_SUCC]:[POUND:%d][PODJI:%d][USDELAY:%d]\n", OutangerConfArg->pound_count,
                        OutangerConfArg->pound_dji_count, OutangerConfArg->usdelay);
        return 0;
    }

    Dbg_Level_Print(LOG_INFO, " [TCP_FAIL]:%s:%d:%d\n", tcp_ip, tcp_port, debug_switch);
    Dbg_Level_Print(LOG_INFO, " [TCP_FAIL]:[LON:%lf], [LAT:%lf], [ALT:%lf]\n", lon, lat, alt);
    Dbg_Level_Print(LOG_INFO, " [TCP_FAIL]:[DEVICE_CODE:%s]\n", device_code);
    Dbg_Level_Print(LOG_INFO, " [TCP_FAIL]:[CPU_CODE:%s][BRD_CODE:%s][PRD_CODE:%s]\n", CPU, BRD, PRD);
    Dbg_Level_Print(LOG_INFO, " [TCP_FAIL]:[OLIP:%s][OLPORT:%d][USR:%s][PWD:%s]\n", OutangerConfArg->ol_ip,
                    OutangerConfArg->ol_port, username, password);
    Dbg_Level_Print(LOG_INFO, " [TCP_FAIL]:[POUND:%d][PODJI:%d][USDELAY:%d]\n", OutangerConfArg->pound_count,
                    OutangerConfArg->pound_dji_count, OutangerConfArg->usdelay);
    return -1;
}
