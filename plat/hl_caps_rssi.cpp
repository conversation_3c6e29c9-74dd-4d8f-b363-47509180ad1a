/*******************************************************************************
/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* 文件名称: hl_caps_rssi.cpp
* 功能描述: rssi通用飞机解析
* 使用说明:
* 文件作者: zonghui
* 编写日期: 2025/02/30
* 修改历史:
*
* 修改日期    修改人  BugID/CRID      修改内容
* ------------------------------------------------------------------------------
*2024/10/30  zonghui                  创建文件
* ------------------------------------------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/time.h>
#include <errno.h>
#include <regex.h>
#include <sys/timerfd.h>
#include <sys/epoll.h>
#include <stdint.h>
#include <signal.h>
#include <sys/stat.h>
#include <sys/types.h>
#include<netinet/ip_icmp.h>
#include<netinet/in.h>
#include<arpa/inet.h>
#include<sys/socket.h>

//#include <syslog.h>
#ifndef WIN32
#include <unistd.h>
#include <thread>
#include <fcntl.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#else
#include <windows.h>
#endif
#include "DroneDetect.h"
#include "hl_caps_rssi.h"
#include "hl_caps_report.h"
#include "hl_caps_rdi.h"

#define MAX_EVENTS 10
#define DAY_SECONDS (24*3600)
#define ENABLE_UPDAGE_TOKEN  1
#define DISABLE_UPDAGE_TOKEN  0
volatile sig_atomic_t running = 1;

#define MAX_MATCHES 2
#define TIMEOUT_SEC 5  // 超时时间(秒)
#define ONLINE_SERVCE_NORMAL   1
#define ONLINE_SERVCE_FAILED  0
#define MAX_TIMERS 5
/******************************* 戮脰虏驴潞锚露篓脪氓 ***********************************/
extern float g_local_latitude;
extern float g_local_longitude;
extern int g_debug_switch;
extern char g_device_code[];
extern T_RHHKReadRdiO4Info g_RhhkReadRdiO4Info[DJI_O4_RDI_MAX_COUNT];
extern T_RhhkUpReportRssiInfo  g_UpReportRssiInfo;
extern T_RhhkGeneralDroneInfo struGeneralDroneInfo[MEAS_FREQ_MAX_COUNT];
extern DtStruct_ApiSet *g_pApiSet;
extern T_RhhkMangerConfArg OutConfInfo;
/******************************* 戮脰虏驴鲁拢脢媒潞脥脌脿脨脥露篓脪氓 ***************************/
T_RhhkUpReportRssiInfo  g_UpReportRssiInfo;
int  CountQuit = 0;
T_RhhkGeneralDroneInfo struGeneralDroneInfo[MEAS_FREQ_MAX_COUNT];
T_RhhkDjiDroneInfo  strDjiDroneInfo[DJI_PACKET_MAX_COUNT];
T_RhhkGyFilterInfo  g_strHisGyDroneFilter[GY_DRONE_UPT_MAX_COUNT];
u32 g_gyindex = 0;
T_RhhkGpsDataInfo GpsDataInfo;
static int g_GetOnlineDecryptedTimeArrive = 1;
T_RhhkDroneLib  g_DjiDroneRecordLib[DJI_DRONE_O4_LIB_MAX];
T_RhhkDebugInfo g_DectionDebugInfo[7];
timer_t timer_list[MAX_TIMERS]; // 全局保存定时器ID
int g_dji_recv_sign = 0;
int g_gen_recv_sign = 0;
/******************************* 脠芦戮脰卤盲脕驴露篓脪氓/鲁玫脢录禄炉 **************************/
extern int Handle_GpsInfo(char *pRcvBuffer, T_RhhkGpsDataInfo *ptGpsDataInfo);
extern void  Handle_RfRssiDataToPacket(T_RhhkGatherRssiInfo *pstrGatherRssiInfo);
extern int GeneralDroneHandle(T_RhhkGeneralDroneInfo *pstruGeneralDroneInfo,DJI_FLIGHT_INFO_Str *message,Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt);
extern int DjiO4DroneHandle(int dji_proxy,T_RhhkDjiAddInfo *pstrDjiO4AddInfo,DJI_FLIGHT_INFO_Str *message,Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt);
extern int DjiDroneHandle(int dji_proxy,T_RhhkDjiDroneInfo *pstruDjiDroneInfo,DJI_FLIGHT_INFO_Str *message,Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt);
extern int Get_ManuLocalGpsInfo(float *flon, float *flat, float *falt);
export char *Rhhk_MakeRequestTokenFromDji(char *username, char *password);
export char * Rhhk_ParseResponseDjiToken(const char *TokenJsonMessage);
export char * Rhhk_MakeEncryptMessageToDji(const char *TokenJsonMessage, const char *DroneEncrytedMessage);
export char *Rhhk_GetDecryptToken(int dj_sockfd, char *username, char *password);
export char *Rhhk_GetDecryptDroneInfo(int dj_sockfd, char *token, char *drone_encrypted_message, char *output_data, int *output_size);
export char *Rhhk_ParseToken(const char *json_message);
export char *Rhhk_GetPareEncryptedDroneInfo(const char *rcv_buffer, char *output_drone);
export int CreateOnlineDecryptTask(char *ipaddr, int port);
export int Rhhk_JudgeRidDetectAndUpReport(uint8_t RidSn[16], uint8_t DjiSn[16],  size_t length);
export int Rhhk_JudgeRidKeepliveDataValid(uint8_t OnlineDjiSn[16],  size_t length);
export int DjiOnlineDecryptedHandle(int dji_proxy, T_RhhkDjiAddInfo *pstrDjiO4AddInfo, DJI_FLIGHT_INFO_Str *message, T_RhhkDecryptDjiDroneInfo *pDecryptedDjiDroneInfo, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt);
export int Rhhk_GetDecryptMessageFromDji(const char *DroneDecrytedMessage, int JsonLength, T_RhhkDecryptDjiDroneInfo *ptOutputDroneData);
export int Rhhk_ParseResponseMessage(char *ReqResponse, char *OutputJson, int *OutputLength);
export long Rhhk_TransTimeToString(char *dateTimeStr);
/******************************* 潞炉脢媒脢碌脧脰 *************************************/
int Enable_Gpio(int PinNum)
{
    if ((PinNum < 0) || (PinNum > 3))
    {
        Dbg_Level_Print(LOG_INFO, "Enable Radar Antenna Pin is Beyond of limiting(0,1,2)");	   
        return -1;	
    }
    else    
    {
        //enable_gpio(PinNum);
	    return 0;
    }
}

int set_Gpio(int value, int PinNum)
{
    if ((PinNum < 0) || (PinNum > 3))
    {
        Dbg_Level_Print(LOG_INFO, "Set Radar Antenna Pin is Beyond of limiting(0,1,2)");	   
        return -1;	
    }
    else    
    {
        //set_gpio(value, PinNum);
	    return 0;
    }
}


/*******************************************************************************
* 函数名称: timer_monitor
* 函数功能: 时间响应函数
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* ip               char *
* port             int
* 返回值: check result     int
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void timer_monitor(union sigval sv)
{
    ModuleMonitorArg *req = (ModuleMonitorArg *)sv.sival_ptr;
	if ((req->sockfd > 0) && (req->size > 0) && (req->cmd != NULL))
	{
	    if ((g_gen_recv_sign == 0) || (g_dji_recv_sign == 0))
	    {
           sendto(req->sockfd, req->cmd, strlen(req->cmd), 0, (const struct sockaddr *)req->addr, req->size);
		   Dbg_Level_Print(LOG_INFO, "timer_monitor[send start][gen_sign:%d, dji_sign:%d]=>req->sockfd:%d, cmd:%s\n", 
             g_gen_recv_sign, g_dji_recv_sign, req->sockfd, req->cmd);
	    }
		else
		{
       Dbg_Level_Print(LOG_INFO, "timer_monitor[no ned send start query][gen_sign:%d, dji_sign:%d]=>req->sockfd:%d, cmd:%s\n", 
		   g_gen_recv_sign, g_dji_recv_sign, req->sockfd, req->cmd);
		   if (g_gen_recv_sign != 0)
		   {
          g_gen_recv_sign = 0;    
		   }

		   if (g_dji_recv_sign != 0)
		   {
          g_dji_recv_sign = 0;    
		   }
       Dbg_Level_Print(LOG_INFO, "timer_monitor[no ned send start reset][gen_sign:%d, dji_sign:%d]=>req->sockfd:%d, cmd:%s\n", 
		   g_gen_recv_sign, g_dji_recv_sign, req->sockfd, req->cmd);
		}
	}
	else
	{
        Dbg_Level_Print(LOG_ERROR, "timer_monitor[gen_sign:%d, dji_sign:%d] =>argment is not valid\n", g_gen_recv_sign, g_dji_recv_sign);
	}
    return;
}
/*******************************************************************************
* 函数名称: StartTimer
* 函数功能: 时间响应函数
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* td                int
* 返回值: check result     int
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int StartTimer(int tdindex, int meas_sockfd, struct sockaddr_in *pMeas_server_addr, socklen_t sin_size, char *timer_scan)
{
    ModuleMonitorArg *req = (ModuleMonitorArg *)malloc(sizeof(ModuleMonitorArg));
    if(!req) return -1;

  memset(req, 0, sizeof(ModuleMonitorArg));
	if (tdindex > MAX_TIMERS)
	{
        tdindex = tdindex % MAX_TIMERS;
	}

    req->sockfd = meas_sockfd;
    req->addr = (struct sockaddr_in *)(pMeas_server_addr);
	  req->size = sin_size;
	  strncpy(req->cmd, timer_scan, sizeof(req->cmd));

    struct sigevent sev;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setstacksize(&attr, 1024*1024); // 设置1MB栈空间

    sev.sigev_notify = SIGEV_THREAD;
    sev.sigev_notify_function = timer_monitor;
    sev.sigev_value.sival_ptr = req;
    sev.sigev_notify_attributes = &attr;

    if (timer_create(CLOCK_REALTIME, &sev, &timer_list[tdindex]) == -1)
    {
        perror("timer_create");
        free(req);
        return -1;
    }

    struct itimerspec its;
    its.it_value = {2,0};
    its.it_interval = {20,0};

    if (timer_settime(timer_list[tdindex], 0, &its, NULL) == -1)
    {
        perror("timer_settime");
        free(req);
        return -1;
    }

    return 0;
}

/*******************************************************************************
* 函数名称: check_service
* 函数功能: 核查servece
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* ip               char *
* port             int
* 返回值: check result     int
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int check_service(const char *ip, int port) 
{
    int sockfd;
    struct sockaddr_in server_addr;
    struct timeval timeout;

    // 创建socket
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        perror("socket创建失败");
        return -1;
    }

    // 设置超时
    timeout.tv_sec = TIMEOUT_SEC;
    timeout.tv_usec = 0;
    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) 
	{
        perror("设置接收超时失败");
        close(sockfd);
        return -1;
    }
    if (setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0) 
	{
        perror("设置发送超时失败");
        close(sockfd);
        return -1;
    }

    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    if (inet_pton(AF_INET, ip, &server_addr.sin_addr) <= 0)
	{
        perror("无效的IP地址");
        close(sockfd);
        return -1;
    }

	
    // 尝试连接
    if (connect(sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) 
	  {
        close(sockfd);
        return 0;  // 连接失败，服务不可用
    }

    close(sockfd);
    return 1;  // 连接成功，服务可用
}
/*******************************************************************************
* 函数名称: Judge_ServiceStatus
* 函数功能: 核查servece
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* ip               char *
* port             int
* 返回值: check result     int
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Judge_ServiceStatus(void)
{
    const char *ip = "***************";
    int port = 5000;

    Dbg_Level_Print(LOG_INFO, "Check %s:%d Online Parse Service...\n", ip, port);

    int result = check_service(ip, port);

    if (result == 1) 
	  {
        Dbg_Level_Print(LOG_INFO, "Online Parse Service, Linked Successfully!\n");
    } 
	  else if (result == 0)
	  {
        Dbg_Level_Print(LOG_INFO, "Online Parse Service is not useful;, Linked Failed!\n");
    } 
	else 
	{
        Dbg_Level_Print(LOG_INFO, "CHeck Process Wrong Happened! \n");
    }

    return result;
}

/*******************************************************************************
* 函数名称: Rhhk_TransTimeToString
* 函数功能: 将日历时间字符串转秒数
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* void        
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
long Rhhk_TransTimeToString(char *dateTimeStr) 
{
    if (dateTimeStr == NULL)
    {
        return -1;
	}
    
    //const char *dateTimeStr = "2025-06-26 13:44:08";
    struct tm tm_struct;
    time_t timestamp;

    
    sscanf(dateTimeStr, "%d-%d-%d %d:%d:%d",
           &tm_struct.tm_year, &tm_struct.tm_mon, &tm_struct.tm_mday,
           &tm_struct.tm_hour, &tm_struct.tm_min, &tm_struct.tm_sec);

    // 设置tm结构体的其他字段，例如tm_year需要减去1900，tm_mon需要减去1
    tm_struct.tm_year -= 1900;  // 因为tm_year是从1900年开始的
    tm_struct.tm_mon -= 1;      // 因为tm_mon是从0开始的
    tm_struct.tm_isdst = -1;    // 让系统决定夏令时

    // 使用mktime将tm结构体转换为时间戳
    timestamp = mktime(&tm_struct);
    if (timestamp != -1) 
	  {
        Dbg_Level_Print(LOG_INFO, "Timestamp: %ld seconds since the Epoch\n", (long)timestamp);
    } 
	  else 
    {
        Dbg_Level_Print(LOG_INFO, "Failed to convert the date and time.\n");
    }

    return timestamp;
}

/*******************************************************************************
* 函数名称: Rhhk_HandleSignal
* 函数功能: 响应定时中断停止定时器
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* sig               sig
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void Rhhk_HandleSignal(int sig)
{
    running = 0;
}

/*******************************************************************************
* 函数名称: Rhhk_TokenJob
* 函数功能: 定时中断指示获取token时机处理
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* sig               sig
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/	
void Rhhk_TokenJob(void)
{
	  time_t now = time(NULL);
	  Dbg_Level_Print(LOG_INFO, "Rhhk_GetTokenJob executed at %s", ctime(&now));
	  g_GetOnlineDecryptedTimeArrive = 1;
}

/*******************************************************************************
* 函数名称: Rhhk_GetTimeSwitch
* 函数功能: 获取token时机开关
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
*  无              
* 返回值:     switch status      
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_GetTimeSwitch(void)
{
	  return g_GetOnlineDecryptedTimeArrive;
}	

/*******************************************************************************
* 函数名称: Rhhk_ResetTimeSwitch
* 函数功能: 恢复token时机开关
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
*  无              
* 返回值:     switch status      
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void Rhhk_ResetTimeSwitch(int status)
{
	   g_GetOnlineDecryptedTimeArrive = status;
	   return;
}	

/*******************************************************************************
* 函数名称: Rhhk_UpdateTokenTask
* 函数功能: 定时更新token Timer
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* void        
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_UpdateTokenTask(void)
{
    memset(&g_DjiDroneRecordLib, 0, sizeof(g_DjiDroneRecordLib));

	  signal(SIGTERM, Rhhk_HandleSignal);
	  signal(SIGINT, Rhhk_HandleSignal);

	  int timer_fd = timerfd_create(CLOCK_REALTIME, TFD_NONBLOCK);
	  if (timer_fd == -1)
	  {
	   	perror("timerfd_create");
	  	return EXIT_FAILURE;
	  }

	  struct itimerspec its =
	  {
		  .it_interval = {.tv_sec = DAY_SECONDS, .tv_nsec = 0},
		  .it_value = {.tv_sec = DAY_SECONDS, .tv_nsec = 0}
	  };

	  if (timerfd_settime(timer_fd, 0, &its, NULL) == -1)
	  {
	   	   perror("timerfd_settime");
		     close(timer_fd);
		     return EXIT_FAILURE;
	  }

	  int epoll_fd = epoll_create1(0);
	  if (epoll_fd == -1)
    {
	   	  perror("epoll_create1");
	  	  close(timer_fd);
		    return EXIT_FAILURE;
	  }

	  struct epoll_event ev, events[MAX_EVENTS];
	  ev.events = EPOLLIN;
	  ev.data.fd = timer_fd;
	  if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, timer_fd, &ev) == -1)
	  {
	    	perror("epoll_ctl");
	    	close(timer_fd);
		    close(epoll_fd);
		    return EXIT_FAILURE;
    }

    while (running)
    {
		    int nfds = epoll_wait(epoll_fd, events, MAX_EVENTS, -1);
		    if (nfds == -1)
		    {
			      if (errno == EINTR) continue;
		      	perror("epoll_wait");
			      break;
		    }

	    	for (int i = 0; i < nfds; ++i)
		    {
		        if (events[i].data.fd == timer_fd)
			      {
				        uint64_t exp;
				        read(timer_fd, &exp, sizeof(exp));
			        	Rhhk_TokenJob();
			      }
	    	}
	}

	close(timer_fd);
	close(epoll_fd);
	return 0;
}

/*******************************************************************************
* 函数名称: Rhhk_GetDroneKeeplive
* 函数功能: 获取drone detection time for keeplive
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* void        
* 返回值: long
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
long Rhhk_GetDroneKeeplive(void)
{   
    time_t DetectionTimePoint; 
    DetectionTimePoint = time(NULL);
	return (long)DetectionTimePoint;
}

/*******************************************************************************
* 函数名称: Rhhk_JudgeDroneDateValid
* 函数功能: 检查SerialNume有效性
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* RidSn[16]         uint_t
* RecentlyDetectTime  long
* 返回值:  1--有效， 0--无效
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_JudgeDroneDateValid(uint8_t RidSn[16], long RecentlyDetectTime)
{   
    int index = 0;
    for (index = 0; index < sizeof(RidSn); index++) 
	{
        if ((RidSn[index] != 0) && (RecentlyDetectTime > 0))
        {
			return 1;
        }
    }

    return 0;
}

/*******************************************************************************
* 函数名称: Rhhk_GetDroneKeeplive
* 函数功能: 获取drone detection time for keeplive
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* void        
* 返回值: long
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void Rhhk_RecordDroneTolib(uint8_t RidSn[16])
{   
    int index = 0;
    long RecentlyDetectTime = 0;

	if (0 == Rhhk_JudgeDroneDateValid(RidSn, RecentlyDetectTime))
	{
        Dbg_Level_Print(LOG_INFO, "Rhhk_RecordDroneTolib is invalid, no need record!\n");
		return;
	}

  RecentlyDetectTime = Rhhk_GetDroneKeeplive();

	while (index++ < DJI_DRONE_O4_LIB_MAX)
	{
        if ((g_DjiDroneRecordLib[index].data_valid == 0) 
			|| (0 == memcmp(&(g_DjiDroneRecordLib[index].drone_serial_num), RidSn, sizeof(RidSn))))
        {
            g_DjiDroneRecordLib[index].data_valid = 1;
			g_DjiDroneRecordLib[index].drone_keeplive = RecentlyDetectTime;
			memcpy(g_DjiDroneRecordLib[index].drone_serial_num,RidSn,sizeof(RidSn));
			break;
		}
	}
	
	return;
}

/*******************************************************************************
* 函数名称: Handle_GpsFromFreqPatch
* 函数功能: 采集GPS数据补丁函数
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* void        
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_GpsFromFreqPatch(void)
{
    float local_lat = 0;
    float local_lon = 0;
	  float local_alt = 0;
    Get_ManuLocalGpsInfo(&local_lon, &local_lat, &local_alt);
    Dbg_Level_Print(LOG_INFO, "[Handle_GpsFromFreqPatch@12345678@]manu local gps1: lon: %f, lat:%f, alt:%f\n", local_lon, local_lat, local_alt);
	  if ((abs(GpsDataInfo.dLongitude) <= 1e-6) && (abs(GpsDataInfo.dLatitude) <= 1e-6))
	  {
	    GpsDataInfo.dLongitude = local_lon;  //经度
	    GpsDataInfo.dLatitude = local_lat;   //纬度
	    GpsDataInfo.dAltitude = local_alt;
	  }
    Dbg_Level_Print(LOG_INFO, "[Handle_GpsFromFreqPatch@12345678@]manu local gps2: lon: %f, lat:%f, alt:%f\n", GpsDataInfo.dLongitude, GpsDataInfo.dLatitude, GpsDataInfo.dAltitude);
	  return 0;
}
/*******************************************************************************
* 函数名称: Handle_GatherGpsData
* 函数功能: 采集GPS数据
* 函数参数: 
* 参数名称: 		类型		          输入/输出	   描述
* gps_sockfd        int                   输入         sock_fd
* pGps_server_addr  struct sockaddr_in *  输入         sock结构
* sin_size          socklen_t             输入         协议长度
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_GatherGpsData(int gps_sockfd,struct sockaddr_in * pGps_server_addr,socklen_t sin_size)
{
    char rcv_buffer[MEAS_BUFFER_SIZE]; //check fuction
    char snd_buffer[MEAS_BUFFER_SIZE];
    u32 u32DataLength = 0;
    ssize_t SendBufSize = 0;
    ssize_t RcvBufSize = 0;
	int rcv_nbyte = 0;
	int scan_sign = 0; //0-have not found, 1-have found
	char *start_scan = "start"; 
	int  packet_index = 0;
	Drone_Detect_GpsInfo_Rpt struGpsInfoRpt;
    T_RhhkGpsDataInfo struGpsDataInfo;

	if (pGps_server_addr != NULL)
    { 
        //清空粗频缓冲区
		memset(&struGpsInfoRpt, 0, sizeof(Drone_Detect_GpsInfo_Rpt));

		// 发送消息到服务器
		sendto(gps_sockfd, start_scan, strlen(start_scan), 0, (const struct sockaddr *)pGps_server_addr, sin_size);
		Dbg_Level_Print(LOG_INFO, "@@@[Handle]Send Gps Order:%s\n", start_scan);
	  Rhhk_SetDebugData(3, 0, 1, NULL, 0);
    Rhhk_SetDebugData(3, 1, 1, NULL, 0); 
  
        while (1)		
        {
			//清空缓冲区
			memset(rcv_buffer, 0, MEAS_BUFFER_SIZE*sizeof(char));

		    // 接收服务器的响应
		    rcv_nbyte = recvfrom(gps_sockfd, rcv_buffer, MEAS_BUFFER_SIZE, 0, (struct sockaddr *)pGps_server_addr, &sin_size);
		    if (rcv_nbyte < 0)
		    {
		       Dbg_Level_Print(LOG_INFO, "@@@recvfrom gps nbyte is %d\n", rcv_nbyte);
		    }
		    rcv_buffer[rcv_nbyte] = '\0'; // 确保字符串以 null 结尾
		    Dbg_Level_Print(LOG_INFO, "@@@[Handle]Recive Gps Result1:%s\n", rcv_buffer);

            //收到数据包大小不合法
			if (rcv_nbyte <= MEAS_RESULT_MIN_SIZE)
			{
                return -1;  
			}

            
			//判断同步消息
			if ((NULL != strstr(rcv_buffer, "ver")) || (NULL != strstr(rcv_buffer, "Heart_Beat_#")))
			{
                return -1;
			}
			Dbg_Level_Print(LOG_INFO, "@@@[Handle]Start Parse GeneralDroneInfo:%s\n", rcv_buffer);

      Rhhk_SetDebugData(3, 2, 1, NULL, 0);
			Rhhk_SetDebugData(3, 3, 1, rcv_buffer, rcv_nbyte); 
      
			//解析GPS
			Dbg_Level_Print(LOG_INFO, "@@@OrgData:%s\n", rcv_buffer);
			memset(&struGpsDataInfo, 0, sizeof(struGpsDataInfo));
			if (Handle_GpsInfo(rcv_buffer, &struGpsDataInfo) > 0)
			{
                Dbg_Level_Print(LOG_INFO, "[Handle]Handle_GpsInfo Getting Successfule!\n");
			}

			if (struGpsDataInfo.dLatitude != 0)
			{
			    memset(&GpsDataInfo, 0, sizeof(T_RhhkGpsDataInfo));
			    GpsDataInfo = struGpsDataInfo;
			}
			Dbg_Level_Print(LOG_INFO, "@@@longitude:%lf\n", GpsDataInfo.dLongitude); 
            Dbg_Level_Print(LOG_INFO, "@@@latitude:%lf\n", GpsDataInfo.dLatitude);	 
            Dbg_Level_Print(LOG_INFO, "@@@altitude:%lf\n", GpsDataInfo.dAltitude);
			
			packet_index++;
		        usleep(50);
        }
	    Dbg_Level_Print(LOG_INFO, "########[Handle]gps socker quit ############\n");
	}

    Dbg_Level_Print(LOG_INFO, "=================gps freq infomation================\n");
    return 0;
}
/*******************************************************************************
* 函数名称: Create_GpsGatherTask
* 函数功能: 创建gps采集任务
* 函数参数: 
* 参数名称: 	   类型		          输入/输出	   描述
* ip_remote        char*              输入         远端ip地址
* ip_local         char*              输入         本地ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Create_GpsGatherTask(char *ip_remote, char *ip_local)
{
    fd_set write_fds, read_fds;
    int flags;
    int sockfd;    
	int rcvBufSize = 1024 * 1024; // 1MB
    int sndBufSize = 1024 * 1024; // 1MB
    

    int opt = 1;
	struct sockaddr_in gps_server_addr;
    socklen_t sin_size = sizeof(gps_server_addr);
    
    const char *pTCPIP;
    int16_t  TCPPORT;

    // cereat UDP socket
    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) 
	{
        perror("gps udp socket creation failed");
        exit(EXIT_FAILURE);
    }
    Dbg_Level_Print(LOG_INFO, "gps udp Socket have Created successfully\n");

    // set UDP server
    memset(&gps_server_addr, 0, sizeof(gps_server_addr));
    gps_server_addr.sin_family = AF_INET;
    gps_server_addr.sin_port = htons(GPS_INFO_PORT);
    gps_server_addr.sin_addr.s_addr = inet_addr(MEAS_FREQ_ADDR);

    if(setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR|SO_REUSEPORT, &opt, sizeof(opt)))
    { 
       perror("udp setsockopt reuseadd and reuseport failed");
	   close(sockfd);
       exit(EXIT_FAILURE);
    }

    if(setsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &rcvBufSize, sizeof(rcvBufSize)))
	{ 
	   perror("udp setsockopt rcv buffer failed");
	   close(sockfd);
	   exit(EXIT_FAILURE);
	}
	
    if(setsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, &sndBufSize, sizeof(sndBufSize)))
	{ 
	   perror("udp setsockopt rcv buffer failed");
	   close(sockfd);
	   exit(EXIT_FAILURE);
	}

    while(1)
    {
        Handle_GatherGpsData(sockfd, (struct sockaddr_in *)&gps_server_addr, sin_size);
	    usleep(20);	
    }
    return 1;
}

/*******************************************************************************
* 函数名称: Handle_ConfureGpsGatherTask
* 函数功能: 创建gps采集任务
* 函数参数: 
* 参数名称: 	   类型		          输入/输出	   描述
* ip_remote        char*              输入         远端ip地址
* ip_local         char*              输入         本地ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_ConfureGpsGatherTask(void *VirtulArg)
{
    Create_GpsGatherTask("ip_remote", "ip_local"); 
	pthread_exit(NULL);
    return;
}
/*******************************************************************************
* 函数名称: CreateGatherGpsPthread
* 函数功能: 创建gps采集任务
* 函数参数: 
* 参数名称: 	   类型		          输入/输出	   描述
* ip_remote        char*              输入         远端ip地址
* ip_local         char*              输入         本地ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void CreateGatherGpsPthread(void)
{
   pthread_t  tid_rid;
   pthread_attr_t attr;
   size_t stack_size;
   size_t set_stack_size = 1024*1024*50;
   int  VirtulArg = 0; //invalid, after upgrade
   int  ret;
  
   if (pthread_attr_init(&attr) != 0)
   {
       perror("pthread_attr_init failed!");
       return EXIT_FAILURE;
   }

   if (pthread_attr_getstacksize(&attr, &stack_size) != 0)
   {
       perror("pthread_attr_getstacksize failed!");
       if (pthread_attr_destroy(&attr) != 0)
       {
           perror("pthread_attr_destroy error");
       }
       return;
   }

   if (pthread_attr_setstacksize(&attr, set_stack_size) != 0)
   {
       perror("pthread_attr_setstacksize failed!");
       if (pthread_attr_destroy(&attr) != 0)
       {
           perror("pthread_attr_destroy error");
       }
       return;
   }

   if (VirtulArg < 0)
   {
       perror("Judge Argment Invalid.\n");
       exit(EXIT_FAILURE);
   }
   Dbg_Level_Print(LOG_INFO,"1 create pthread for gps start!\n");
   ret = pthread_create(&tid_rid, &attr, Handle_ConfureGpsGatherTask, (void *)&VirtulArg);
   if (ret != 0)
   {
       perror("StartGatherGpsTask:Pthread_Create Failed!");
       return;
   }
   pthread_detach(tid_rid);
   Dbg_Level_Print(LOG_INFO, "2 create pthread for Gps finished!\n");
   Dbg_Level_Print(LOG_INFO, "####################################!\n");
#if 0
   if (pthread_attr_destroy(&attr) != 0)
   {
     perror("pthread_attr_destroy error!");
     return;
   }

   if (pthread_join(tid_rid, NULL) != 0)
   {
      perror("pthread_join failed");
      return;
   }
#endif
   return;
}


/*******************************************************************************
* 函数名称: Handle_RfRssiDataToPacket
* 函数功能: 采集解析射频参数
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* ip_remote 	   char*			  输入		   远端ip地址
* ip_local		   char*			  输入		   本地ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void  Handle_RfRssiDataToPacket(T_RhhkGatherRssiInfo *pstrGatherRssiInfo)
{
    int Index = 0;
	int maxIndex = 0;
	float maxRssiValue = 0;
	float rssi_angle[RF_MAX] = {0, 45, 90, 135, 180, 225, 270, 315};
    if (pstrGatherRssiInfo == NULL)
    {
       return;
    }

    memset(&(g_UpReportRssiInfo), 0, sizeof(T_RhhkUpReportRssiInfo));
    if (pstrGatherRssiInfo->data_valid == 1)
    {
        g_UpReportRssiInfo.freq = pstrGatherRssiInfo->freq;
	maxIndex = 0;
	maxRssiValue = pstrGatherRssiInfo->rssi_drone[0];
	while (Index < RF_MAX) 
	{
            if (pstrGatherRssiInfo->rssi_drone[Index] > maxRssiValue) 
	    {
                maxRssiValue = pstrGatherRssiInfo->rssi_drone[Index];
                maxIndex = Index;
            }
	   Index++;
        }
		
    }

    g_UpReportRssiInfo.rssi_drone = pstrGatherRssiInfo->rssi_drone[maxIndex];
    g_UpReportRssiInfo.rssi_max = pstrGatherRssiInfo->rssi_max[maxIndex];
    g_UpReportRssiInfo.rssi_angle = rssi_angle[maxIndex];

    Dbg_Level_Print(LOG_INFO, "=========RSSI TEST RESULT1===========\n");
    Dbg_Level_Print(LOG_INFO, "Index: %d,rssi_drone:%.1f,  rssi_max:%.1f, rssi_angle:%.1f\n", maxIndex, g_UpReportRssiInfo.rssi_drone, 
		    g_UpReportRssiInfo.rssi_max,g_UpReportRssiInfo.rssi_angle);
    Dbg_Level_Print(LOG_INFO, "=========RSSI TEST RESULT2===========\n");
	
    return;
}


void rssi_rhhk_callback(DJI_FLIGHT_INFO_Str *message)
{
    return;
}
/*******************************************************************************
* 函数名称: rssi_swi_callback
* 函数功能: 从回调库函数获取定向模块采集射频参数传给解析函数
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* rssi_drone 	   float*			  输入		   drone rssi
* rssi_max		   float*			  输入		   max rssi
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void rssi_swi_callback(float rssi_drone[], float rssi_max[], int64_t freq)
{
    T_RhhkGatherRssiInfo  strGatherRssiInfo;
    memset(&strGatherRssiInfo, 0, sizeof(T_RhhkGatherRssiInfo));
    for (int i = 0; i < 8; i++)
    {
        Dbg_Level_Print(LOG_INFO, "zh-rssi: %.1f  %.1f\n", rssi_drone[i], rssi_max[i]);
        strGatherRssiInfo.rssi_drone[i] = rssi_drone[i];
        strGatherRssiInfo.rssi_max[i] = rssi_max[i];
    }
    strGatherRssiInfo.freq = freq;
    strGatherRssiInfo.data_valid = 1;
    Handle_RfRssiDataToPacket(&strGatherRssiInfo);
    printf("3$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
    printf("4$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");

    CountQuit++;

    //printf("rssi =%.1f,  freq=.1f, gpio=%x\n", rssi, freq / 16, gpio_value);
}
/*******************************************************************************
* 函数名称: Handle_ConfureRssiGatherTask
* 函数功能: 启动通用模块采集模块
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* VirtualArg 	   void*			  输入		   已经被优化掉
* 返回值: int
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_ConfureRssiGatherTask(void *VirtulArg)
{
    //int antenna_code[8] = {0b000,0b001,0b010,0b011,0b100,0b101,0b110,0b111};
    int antenna_pos_code[8] = { 0b000, 0b100,0b010, 0b110,0b001,0b101,0b011,0b111 };

    Dbg_Level_Print(LOG_INFO, "3 pthread configure rssi and read rssi argument!\n");
   
    //using new method 
    Create_FreqMeas_Socket("ip_remote", "ip_local"); 

    pthread_exit(NULL);
    return;
}
/*******************************************************************************
* 函数名称: Handle_ParseAllScanFreq
* 函数功能: 解析射频
* 函数参数: 
* 参数名称: 	               类型 			       输入/输出    描述
* freq_buffer 	               char*			       输入		   基于字符的buffer
* pstruGeneralDroneInfo		   T_RhhkGeneralDroneInfo* 输出		   射频参数
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_ParseAllScanFreq(char *freq_buffer, T_RhhkGeneralDroneInfo *pstruGeneralDroneInfo)
{
    int index1 = 0;
	int index2 = 0;
	const char *delim1 = ",";
    const char *delim2 = "=";
    char *token_rdi1;
    char *token_rdi2;
    char *keystr1;
    char *keystr2;
	char rssi_scan_finish = 0;
    char rssi_scan_start = 0;
	char Data[DETCT_GE_DATA_COUNT][MEAS_FREQ_PART_LENGTH];
	const char *frep_info_descr[MEAS_FREQ_PART_COUNT] = {"device", "model", "freq", "rssi"};
	char *endptr;
	float floatValue;
	float *arr_target_freq;
	char *pGeDroneSerialNum;

    if ((freq_buffer == NULL) || (pstruGeneralDroneInfo == NULL))
    {
        return -1;
	}

	keystr1 = (char *)freq_buffer;
    while(token_rdi1 = strtok_r(keystr1, delim1, &keystr1))
	{
        keystr2 = token_rdi1;
        while(token_rdi2 = strtok_r(keystr2, delim2, &keystr2))
        {
	        if (NULL != strstr(token_rdi2, frep_info_descr[0]))
	        {
	            rssi_scan_start = 1;
	        }

	        if (rssi_scan_start == 1)
            {
	            //printf("==>:[%d]  =ks2=>%s\n", index1, token_rdi2);
	            if (NULL != strstr(token_rdi2, frep_info_descr[3]))
	            {
	                rssi_scan_finish = 1;
	            }

		        if(index1 < DETCT_GE_DATA_COUNT)
		        {
                    strcpy(Data[index1], token_rdi2);
	                index1 = index1 + 1;
		        }
	        }
        }

	    if (rssi_scan_finish == 1)
	    {
	         break;
	    }
    }
	while(index2 < index1)
    {
        if (index2 >= DETCT_O3_DATA_COUNT)
	    {
	       break;
	    }

		if (NULL != strstr(Data[index2], frep_info_descr[1]))
		{
		   // 使用 strtof 转换为 float
		   strcpy((pstruGeneralDroneInfo->arr_target_model), Data[index2+1]);	  
		   Dbg_Level_Print(LOG_INFO, "---model:%s\n", Data[index2+1]);
		   pstruGeneralDroneInfo->valid = 1; //数据有效
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[2]))
	    {
		   // 使用 strtof 转换为 float
		   floatValue = strtof(Data[index2+1], &endptr);
	       pstruGeneralDroneInfo->arr_target_freq = floatValue;    
           Dbg_Level_Print(LOG_INFO, "---freq:%s\n", Data[index2+1]);
	       pstruGeneralDroneInfo->valid = 2; //数据有效
	       index2 = index2 + 2;
	       continue;
	    }

	    if (NULL != strstr(Data[index2], frep_info_descr[3]))
	    {
		   // 使用 strtof 转换为 float
		   floatValue = strtof(Data[index2+1], &endptr);
	       pstruGeneralDroneInfo->arr_target_rssi = floatValue;    
           Dbg_Level_Print(LOG_INFO, "---rss:%s\n", Data[index2+1]);
	       pstruGeneralDroneInfo->valid = 3; //数据有效
	       index2 = index2 + 2;
	       continue;
	    }
		index2 = index2 + 1;
    }

    pGeDroneSerialNum = CreateGeneralDroneSN((char *)pstruGeneralDroneInfo->arr_target_model, 
		             pstruGeneralDroneInfo->arr_target_freq, pstruGeneralDroneInfo->arr_target_rssi);
    if (pGeDroneSerialNum != NULL)
    {
        strncpy(pstruGeneralDroneInfo->arr_target_serial, pGeDroneSerialNum, MEAS_FREQ_PART_LENGTH);
    }    
    
    snprintf(pstruGeneralDroneInfo->arr_target_serial, 50, "%s%s%d", "GY", pstruGeneralDroneInfo->arr_target_model, int(pstruGeneralDroneInfo->arr_target_freq/100)); //need delete

    Dbg_Level_Print(LOG_INFO, "[Handle] Handle_ParseAllScanFreq Parse finished! (%d)(%s)\n", pstruGeneralDroneInfo->valid, pstruGeneralDroneInfo->arr_target_serial);	
    return 0;
}
/*******************************************************************************
* 函数名称: Handle_FilterRepeatUpt
* 函数功能: 无人机上异常信息过滤
* 函数参数: 
* 参数名称: 	               类型 			                  输入/输出    描述
* pstruGeneralDroneInfo 	   T_RhhkGeneralDroneInfo*			  输入		   远端ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_FilterRepeatUpt(T_RhhkGeneralDroneInfo *pstruGeneralDroneInfo)
{
   int rept_sign = 0;
   int his_index = 0;
   time_t curtime;
   int check_len = 0;

   if (pstruGeneralDroneInfo == NULL)
   {
      return 0;
   }

   check_len = strlen(pstruGeneralDroneInfo->arr_target_model);
   if (strncmp("DJI_", pstruGeneralDroneInfo->arr_target_model, 4) == 0)
   {
       rept_sign = 0;
       return rept_sign;
   }

   if (strncmp("O3+", pstruGeneralDroneInfo->arr_target_model, 3) == 0)
   {
       rept_sign = 0;
       return rept_sign;
   }
   
   if (strncmp("03+", pstruGeneralDroneInfo->arr_target_model, 3) == 0)
   {
       rept_sign = 0;
       return rept_sign;
   }
   
   if (strncmp("0-", pstruGeneralDroneInfo->arr_target_model, 2) == 0)
   {
       rept_sign = 0;
       return rept_sign;
   }

   curtime = time(NULL);
   for (his_index = 0; his_index < GY_DRONE_UPT_MAX_COUNT; his_index++)
   {
      if (g_strHisGyDroneFilter[his_index].valid == 0)
      {
           rept_sign = 1;  //null
           break;
      }

      if (0 != strcmp(pstruGeneralDroneInfo->arr_target_model, g_strHisGyDroneFilter[his_index].gy_model))
      {
          continue; //diff model
      }
      else
      {
          if ((pstruGeneralDroneInfo->arr_target_freq - g_strHisGyDroneFilter[his_index].gy_freq) >= GY_DRONE_FREQ_THRESHOLD)
          {
             continue;  //diff freq
          }
          else
          {
              if ((curtime - g_strHisGyDroneFilter[his_index].gy_time) < GY_DRONE_INVALID_TIME)
              {
                  continue;  //live
              }
              else
              {
                  rept_sign = 1; //timeout
                  break;
              }
          }
      }
   }

   if (rept_sign == 1)
   {
      g_gyindex = ((g_gyindex >= GY_DRONE_UPT_MAX_COUNT) ? (g_gyindex % GY_DRONE_UPT_MAX_COUNT) : (g_gyindex));
      memset(&(g_strHisGyDroneFilter[g_gyindex]), 0, sizeof(g_strHisGyDroneFilter[g_gyindex]));
      strncpy(g_strHisGyDroneFilter[g_gyindex].gy_model, pstruGeneralDroneInfo->arr_target_model, GY_DRONE_UPT_NAME_LEN*sizeof(char));
      g_strHisGyDroneFilter[g_gyindex].valid = 1;
          g_strHisGyDroneFilter[g_gyindex].gy_freq = pstruGeneralDroneInfo->arr_target_freq;
      g_strHisGyDroneFilter[g_gyindex].gy_time = time(NULL);
      g_gyindex = g_gyindex + 1;
   }

   return rept_sign;   
}
/*******************************************************************************
* 函数名称: Handle_GpsInfo
* 函数功能: 创建gps采集任务
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* pRcvBuffer 	   char*			  输入		   字符buffer地址
* ptGpsDataInfo	   T_RhhkGpsDataInfo* 输出		   gps结构参数
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_GpsInfo(char *pRcvBuffer, T_RhhkGpsDataInfo *ptGpsDataInfo)
{
    const char *splite_lon = "lon=";
	const char *splite_lat = "lat=";
	const char *splite_alt = "alt=";
	const char *splite_utc = "utc=";
	const char *space_sign = ' ';
	const char *splite_mon[12] = {"Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"};
	char *current_line = pRcvBuffer;
    char *target_lon = NULL;
	char *target_lat = NULL;
	char *target_alt = NULL;
	char *target_utc = NULL;
    char *mon_ptr = NULL;
	char *day_ptr = NULL;
	char *time_start_ptr = NULL;
	char *time_end_ptr = NULL;
	char *year_ptr = NULL;
	char *token = NULL;
	struct tm tm_time;
	struct timeval tv;
	time_t timesec = 0;
    char utc[100];        
    double lat=0, lon=0, alt=0;
	const char *datetime_str = 0;
	int gps_sign = -1;
		
    if ((pRcvBuffer == NULL) || (ptGpsDataInfo == NULL))
    {
        Dbg_Level_Print(LOG_INFO, "Handle_GpsInfo input RcvBuffer Or GpsDataInfo is Null, exit\n");
		return gps_sign;
    }    
    
    //char input_string[] = "utc=Mon Apr  7 02:19:14 2025\n"
    //                       "lat=116.156171\n"
    //                       "lon=39.922567\n"
    //                       "alt=143.900000";  
    
    char *line = strtok(pRcvBuffer, "\n");
    while (line != NULL) 
	{
        char *equal_sign = strchr(line, '=');
        if (equal_sign != NULL) 
		{
            *equal_sign = '\0'; 
            char *key = line;
            char *value = equal_sign + 1;  

            while (*value == ' ') value++;

            if (strcmp(key, "utc") == 0) 
			{
                strcpy(utc, value);
				gps_sign += 1; 
            } 
			else if (strcmp(key, "lat") == 0) 
            {
                lat = atof(value);
				gps_sign += 1;
            } 
			else if (strcmp(key, "lon") == 0) 
            {
                lon = atof(value);
				gps_sign += 1;
            } 
			else if (strcmp(key, "alt") == 0) 
            {
                alt = atof(value);
				gps_sign += 1;
            }
        }
        line = strtok(NULL, "\n");
    }

    ptGpsDataInfo->dLongitude = lat;  //前端传过来经度和纬度是反的
    ptGpsDataInfo->dLatitude = lon;	  //纬度
    ptGpsDataInfo->dAltitude = alt;   //高度
	
    datetime_str = (const char *)utc;

	memset(&tm_time, 0, sizeof(tm_time));
    if (strptime(datetime_str, "%a %b %d %H:%M:%S %Y", &tm_time) == NULL) 
	{
        fprintf(stderr, "时间解析失败\n");
        return EXIT_FAILURE;
    }

    // 打印解析后的时间
    tm_time.tm_hour = tm_time.tm_hour + 8;
    Dbg_Level_Print(LOG_INFO, "解析后的时间:\n");
    Dbg_Level_Print(LOG_INFO, "年份: %d\n", tm_time.tm_year + 1900); // tm_year 从1900年开始
    Dbg_Level_Print(LOG_INFO, "月份: %d\n", tm_time.tm_mon + 1); // tm_mon 从0开始（0表示一月）
    Dbg_Level_Print(LOG_INFO, "日期: %d\n", tm_time.tm_mday);
    Dbg_Level_Print(LOG_INFO, "小时: %d\n", tm_time.tm_hour);
    Dbg_Level_Print(LOG_INFO, "分钟: %d\n", tm_time.tm_min);
    Dbg_Level_Print(LOG_INFO, "秒: %d\n", tm_time.tm_sec);
	tm_time.tm_isdst = -1;

    Dbg_Level_Print(LOG_INFO, "GetGpsTime is %d-%d-%d %d:%d:%d\n", tm_time.tm_year, tm_time.tm_mon, tm_time.tm_mday,
		    tm_time.tm_hour, tm_time.tm_min, tm_time.tm_sec);
    timesec = mktime(&tm_time); 
    tv.tv_sec = timesec;
    tv.tv_usec = 0;

    if (gps_sign >= 3)

    {
       if (settimeofday(&tv, NULL) < 0)
       {
           Dbg_Level_Print(LOG_INFO, "settimeofday is failed\n");
       }
    }   

    if (gps_sign > 0)
    {
        return gps_sign;
	}

    return gps_sign;
}

/*******************************************************************************
* 函数名称: Handle_FreqMeasData
* 函数功能: 创建gps采集任务
* 函数参数: 
* 参数名称: 	     类型 			  输入/输出    描述
* meas_sockfd 	     int			  输入		   sockfd
* pMeas_server_addr	 struct sockaddr_in*  输入		sock结构
* sin_size           int                  输入      地址长度
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_FreqMeasData(int meas_sockfd, struct sockaddr_in *pMeas_server_addr, socklen_t sin_size)
{
    char *outdata = 0;
    char rcv_buffer[MEAS_BUFFER_SIZE]; //check fuction
    char snd_buffer[MEAS_BUFFER_SIZE];
    u32 u32DataLength = 0;
    ssize_t SendBufSize = 0;
    ssize_t RcvBufSize = 0;
	int rcv_nbyte = 0;
    int flags = 0;
    int sockfd = 0;
	int scan_sign = 0; //0-have not found, 1-have found
	char *start_scan = "start -freq 1"; 
	char *lock_freq = "start -freq ";
	char *junor_freq = "device=1855, model=DJI_OC123_20M, freq=5816.5, rssi=-65.0";
	char *result_rssi = "device=1855, model=DJI_OC123_20M, freq=5815.0, rssi=-65.0, gpio=3,";
	int  packet_index = 0;
	int  lock_index = 0;
	int  antenna_index = 0;
	float *arr_target_freq[MEAS_FREQ_MAX_COUNT];
	u8   cnt_freq = 0;
	T_RhhkRssiPacketInfo  strLockFreqRssiGatherInfo;
	DJI_FLIGHT_INFO_Str strMessage;
	Drone_Detect_GpsInfo_Rpt struGpsInfoRpt;
	int retry_count = 0;
	int local_gps_count = 0;
  T_RhhkGeneralDroneInfo struLocalGpsUpReport;
  int pound_count = 0;
  int usdelay = 0;
	T_RhhkMangerConfArg OutPoundInfo;
  int pound_index = 0;
  char test_buffer[MEAS_BUFFER_SIZE];
	
	if (pMeas_server_addr != NULL)
  { 
        //清空粗频缓冲区
		memset(&struGeneralDroneInfo, 0, MEAS_FREQ_MAX_COUNT*sizeof(T_RhhkGeneralDroneInfo));
		memset(&strMessage, 0, sizeof(DJI_FLIGHT_INFO_Str));
		memset(&struGpsInfoRpt, 0, sizeof(Drone_Detect_GpsInfo_Rpt));
		//memset(&GpsDataInfo, 0, sizeof(T_RhhkGpsDataInfo));

		// 发送消息到服务器
		sendto(meas_sockfd, start_scan, strlen(start_scan), 0, (struct sockaddr *)pMeas_server_addr, sin_size);
		Dbg_Level_Print(LOG_INFO, "[Handle]Send FreqScan Order:%s\n", start_scan);
		Rhhk_SetDebugData(1, 0, 1, NULL, 0);
    Rhhk_SetDebugData(1, 1, 1, NULL, 0);
    
    if(StartTimer(0, meas_sockfd, (struct sockaddr_in *)pMeas_server_addr, sin_size, start_scan) == -1) 
		{
        Dbg_Level_Print(LOG_INFO, "Handle_FreqMeasData Create Timer failed!\n");
        return 1;
    }
    
    while(1)
    {
			//清空缓冲区
			memset(rcv_buffer, 0, MEAS_BUFFER_SIZE*sizeof(char));

            if (GpsDataInfo.dLatitude != 0.0f)
		    {
			    struGpsInfoRpt.local_latitude = GpsDataInfo.dLatitude;
                struGpsInfoRpt.local_longitude = GpsDataInfo.dLongitude;
			    struGpsInfoRpt.local_altitude = GpsDataInfo.dAltitude;
		    }
			else if (g_local_latitude  != 0.0f)
			{
                 struGpsInfoRpt.local_latitude = g_local_latitude;
                 struGpsInfoRpt.local_longitude = g_local_longitude;
				 struGpsInfoRpt.local_altitude = 0.0f;
			}
			else
			{
                 struGpsInfoRpt.local_latitude = 0.0f;
                 struGpsInfoRpt.local_longitude = 0.0f;
				 struGpsInfoRpt.local_altitude = 0.0f;
			}
			
      // 接收服务器的响应
		  rcv_nbyte = recvfrom(meas_sockfd, rcv_buffer, MEAS_BUFFER_SIZE, 0, (struct sockaddr *)pMeas_server_addr, &sin_size);
		  rcv_buffer[rcv_nbyte] = '\0'; // 确保字符串以 null 结尾
		  local_gps_count += 1;
	
      memset(&OutPoundInfo,0,sizeof(T_RhhkMangerConfArg));
      if(0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *)&OutPoundInfo))
      {
          pound_count = OutPoundInfo.pound_count;
          usdelay = OutPoundInfo.usdelay;
			}
	    Dbg_Level_Print(LOG_INFO, "[Handle]Recive (%d)[%d] FreqScan Result:%s\n", pound_count, local_gps_count, rcv_buffer);
           
      if ((pound_count > 0) && (pound_count < POUNDING_GY_COUNT))
			{
         if (pound_index < pound_count)
			   {
             memset(rcv_buffer, 0, MEAS_BUFFER_SIZE*sizeof(char));	         
             sprintf(rcv_buffer, "device=1%03d, model=GYPd%02d_Analog, freq=5755.0, rssi=-58.7, gpio=6,", pound_index, pound_index);
			   }
				 else
				 {
             pound_index = 0;
				 }
         pound_index++;
         usleep(usdelay);  
			}
      
			if (local_gps_count >= 5)
			{
         memset(&struLocalGpsUpReport, 0, sizeof(T_RhhkGeneralDroneInfo));
         GeneralDroneHandle(&struLocalGpsUpReport, (DJI_FLIGHT_INFO_Str *)&strMessage, (Drone_Detect_GpsInfo_Rpt *)&struGpsInfoRpt);              
				 local_gps_count = 0;
				 Dbg_Level_Print(LOG_INFO, "[Handle]Recive (%d) FreqScan Clear!\n", local_gps_count);
			}

			if (rcv_nbyte <= 0)
			{
                if ((errno == EAGAIN) || (errno == EWOULDBLOCK))
                {
                    Dbg_Level_Print(LOG_INFO, "[INFO GENENAL UDP TIMEOUT] Timeout detected!\n");
				}
				else
				{
                     perror("recvfrom error");
				}
				close(meas_sockfd);
                Dbg_Level_Print(LOG_INFO, "General Reconnecting attempt #%d...\n", ++retry_count);

				if ((meas_sockfd = Create_CliUdpSocket("************", 10153)) < 0)
				{
                    sleep(1);  
					continue;
				}

				sleep(1);
				Dbg_Level_Print(LOG_INFO, "General Reconnecting Successfully\n");
				sendto(meas_sockfd, start_scan, strlen(start_scan), 0, (const struct sockaddr *)pMeas_server_addr, sin_size);
		        Dbg_Level_Print(LOG_INFO, "Send Freq (General Reconnecting) Detection Scan Command:%s\n", snd_buffer);
				continue;
				
			}

            //收到数据包大小不合法
			if (rcv_nbyte <= MEAS_RESULT_MIN_SIZE)
			{
                continue;  
			}

			//判断同步消息
			if ((NULL != strstr(rcv_buffer, "ver")) || (NULL != strstr(rcv_buffer, "Heart_Beat_#")))
			{
          g_gen_recv_sign = 1;
          continue;
			}
			Dbg_Level_Print(LOG_INFO, "[Handle]Start Parse GeneralDroneInfo:%s\n", rcv_buffer);
      Rhhk_SetDebugData(1, 2, 1, NULL, 0);
			Rhhk_SetDebugData(1, 3, 1, rcv_buffer, rcv_nbyte);
      
            //解析粗频
            packet_index = packet_index % MEAS_FREQ_MAX_COUNT;
            Handle_ParseAllScanFreq((char *)rcv_buffer, &(struGeneralDroneInfo[packet_index]));
            if(packet_index >= MEAS_FREQ_MAX_COUNT)
			{
			    packet_index = 0;
                break;
			}

			Dbg_Level_Print(LOG_INFO, "[Handle]Parse General Drone Infomation As Follow:\n");
			Dbg_Level_Print(LOG_INFO, "valid:%d\n", struGeneralDroneInfo[packet_index].valid);
			Dbg_Level_Print(LOG_INFO, "freq:%f\n", struGeneralDroneInfo[packet_index].arr_target_freq);
			Dbg_Level_Print(LOG_INFO, "rssi:%f\n", struGeneralDroneInfo[packet_index].arr_target_rssi);
			Dbg_Level_Print(LOG_INFO, "model:%s\n\n", struGeneralDroneInfo[packet_index].arr_target_model);

			if ((struGeneralDroneInfo[packet_index].valid != 0) && (1 ==  Handle_FilterRepeatUpt(&(struGeneralDroneInfo[packet_index]))))
			{
				  Dbg_Level_Print(LOG_KEY, "[KEY-SND-GENERAL]model:%s\n\n", struGeneralDroneInfo[packet_index].arr_target_model);
				  Dbg_Level_Print(LOG_KEY, "[KEY-SND-GENERAL]valid:%d\n", struGeneralDroneInfo[packet_index].valid);
			    Dbg_Level_Print(LOG_KEY, "[KEY-SND-GENERAL]freq:%f\n", struGeneralDroneInfo[packet_index].arr_target_freq);
			    Dbg_Level_Print(LOG_KEY, "[KEY-SND-GENERAL]rssi:%f\n", struGeneralDroneInfo[packet_index].arr_target_rssi);
			    Dbg_Level_Print(LOG_KEY, "[KEY-SND-GENERAL]model:%s\n\n", struGeneralDroneInfo[packet_index].arr_target_model);
			    GeneralDroneHandle(&(struGeneralDroneInfo[packet_index]), (DJI_FLIGHT_INFO_Str *)&strMessage, (Drone_Detect_GpsInfo_Rpt *)&struGpsInfoRpt); 
                memset(&(struGeneralDroneInfo[packet_index]), 0, sizeof(T_RhhkGeneralDroneInfo));
				local_gps_count = 0;
        g_gen_recv_sign = 2;
        Rhhk_SetDebugData(1, 6, 1, NULL, 0);
			}	
			
			packet_index++; 
        }

	    Dbg_Level_Print(LOG_INFO, "########[Handle]rssi socker quit ############\n");
	}
    Dbg_Level_Print(LOG_INFO, "=================rssi freq infomation================\n");
    return 0;
}


/*******************************************************************************
* 函数名称: Create_FreqMeas_Socket
* 函数功能: 内部没有数据采集socket创建
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* ip_remote 	   char*			  输入		   远端ip地址
* ip_local		   char*			  输入		   本地ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Create_FreqMeas_Socket(char *ip_remote, char *ip_local)
{
    fd_set write_fds, read_fds;
    int flags;
    int sockfd;    
	int rcvBufSize = 1024 * 1024; // 1MB
    int sndBufSize = 1024 * 1024; // 1MB
    

    int opt = 1;
	struct sockaddr_in meas_server_addr;
    socklen_t sin_size = sizeof(meas_server_addr);
    
    const char *pTCPIP;
    int16_t  TCPPORT;

    memset(&meas_server_addr, 0, sizeof(meas_server_addr));
    meas_server_addr.sin_family = AF_INET;
    meas_server_addr.sin_port = htons(MEAS_FREQ_PORT);
    meas_server_addr.sin_addr.s_addr = inet_addr(MEAS_FREQ_ADDR);

    if ((sockfd = Create_CliUdpSocket("************", 10153)) < 0) 
	{
	    Dbg_Level_Print(LOG_INFO, "general udp Socket have Created failed!\n");
        exit(EXIT_FAILURE);
    }
	Dbg_Level_Print(LOG_INFO, "general udp Socket have Created successfully!\n");

    while(1)
    {
        Handle_FreqMeasData(sockfd, (struct sockaddr_in *)&meas_server_addr, sin_size);
	    usleep(100);	
    }
	
    return 1;
}


/*******************************************************************************
* 函数名称: CreatRssiSwitchPthread
* 函数功能: 通用模块采集任务创建
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* 无
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void CreatRssiSwitchPthread(void)
{
   pthread_t  tid_rid;
   int  VirtulArg = 0; //invalid, after upgrade
   int  ret;

   //pthread_mutex_init(&mutex_tid, NULL);

   if (VirtulArg < 0)
   {
       perror("Judge Argment Invalid.\n");
       exit(EXIT_FAILURE);
   }
   Dbg_Level_Print(LOG_INFO,"1 create pthread for rssi(general freq module) start!\n");
   ret = pthread_create(&tid_rid, NULL, Handle_ConfureRssiGatherTask, (void *)&VirtulArg);
   if (ret != 0)
   {
       perror("StartDroneRssiTask:Pthread_Create Failed!");
       return;
   }
   pthread_detach(tid_rid);
   Dbg_Level_Print(LOG_INFO, "2 create pthread for rssi finished!\n");

   //pthread_join(tid_rid, NULL);
   //pthread_mutex_destroy(&mutex_tid);
   return;
}

/*******************************************************************************
* 函数名称: Judge_StrStartWith
* 函数功能: 根据数据长度和起始数据判断数据有效性
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* originString 	   const char* 		  输入		   字符buffer地址
* prefix		   char*			  输入		   字符
* 返回值:          无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Judge_StrStartWith(const char *originString, char *prefix) 
{	
	int i;
    if (originString == NULL || prefix == NULL || strlen(prefix) > strlen(originString)) 
	{	
	    Dbg_Level_Print(LOG_INFO, "......\n");		
	    return -1;	
	}		
	int n = strlen(prefix);	
		
	for (i = 0; i < n; i++) 
	{	    
	    if (originString[i] != prefix[i]) 
		{		   
		    return 1;		
		}	
	}	
	return 0;
}

/*******************************************************************************
* 函数名称: Handle_ParseDjiModule
* 函数功能: Dji模块解析
* 函数参数: 
* 参数名称: 	   类型 			   输入/输出    描述
* freq_buffer 	   char*			   输入		   buffer地址
* pstrDjiDroneInfo T_RhhkDjiDroneInfo* 输入		   输出结构射频信息
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/

/****************************dji**********************************************/
int Handle_ParseDjiModule(char *freq_buffer, T_RhhkDjiDroneInfo *pstrDjiDroneInfo)
{
    int index1 = 0;
	int index2 = 0;
	const char *delim1 = ",";
    const char *delim2 = "=";
    char *token_rdi1;
    char *token_rdi2;
    char *keystr1;
    char *keystr2;
	char rssi_scan_finish = 0;
    char rssi_scan_start = 0;
	char Data[DETCT_O3_DATA_COUNT][DJI_ARG_PART_LENGTH];
	const char *frep_info_descr[DJI_ARG_PART_COUNT] = {"num", "device", "serial", "model","drone_GPS", 
		"home_GPS", "pilot_GPS", "Height", "Altitude", "EastV", "NothV", "UpV", "freq", "rssi", "distance"};
	char *endptr;
	float floatValue;
	float *arr_target_freq;
	
    if ((freq_buffer == NULL) || (pstrDjiDroneInfo == NULL))
    {
        return -1;
	}

	keystr1 = (char *)freq_buffer;
    while(token_rdi1 = strtok_r(keystr1, delim1, &keystr1))
	{
        keystr2 = token_rdi1;
        while(token_rdi2 = strtok_r(keystr2, delim2, &keystr2))
        {
	        if (NULL != strstr(token_rdi2, frep_info_descr[0]))
	        {
	            rssi_scan_start = 1;
	        }

	        if (rssi_scan_start == 1)
            {
	            //printf("==>:[%d]  =ks2=>%s\n", index1, token_rdi2);
	            if (NULL != strstr(token_rdi2, frep_info_descr[14]))
	            {
	                rssi_scan_finish = 1;
	            }

		        if(index1 < DETCT_O3_DATA_COUNT)
		        {
                    strcpy(Data[index1], token_rdi2);
	                index1 = index1 + 1;
		        }
	        }
        }

	    if (rssi_scan_finish == 1)
	    {
	         break;
	    }
    }
	
	while(index2 < index1)
    {
        if (index2 >= DETCT_O3_DATA_COUNT)
	    {
	       break;
	    }

		if (NULL != strstr(Data[index2], frep_info_descr[0]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->seq_num = atoi(Data[index2+1]);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[1]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->device_num = atoi(Data[index2+1]);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[2]))
	    {
	       strncpy(pstrDjiDroneInfo->drone_serial_num, Data[index2+1], 17);    
           //printf("---freq:%s\n", Data[index2+1]);
	       index2 = index2 + 2;
	       continue;
	    }

		if (NULL != strstr(Data[index2], frep_info_descr[3]))
	    {
		   // 使用 strtof 转换为 float
	       strncpy(pstrDjiDroneInfo->product_type_str, Data[index2+1], 32);    
           //printf("---freq:%s\n", Data[index2+1]);
	       index2 = index2 + 2;
	       continue;
	    }

	    if (NULL != strstr(Data[index2], frep_info_descr[4]))
	    {
		   // 使用 strtof 转换为 float
	       pstrDjiDroneInfo->drone_longitude= atof(Data[index2+1]);  
		   pstrDjiDroneInfo->drone_latitude = atof(Data[index2+2]);
           //printf("---freq:%s\n", Data[index2+1]);
	       index2 = index2 + 3;
	       continue;
	    }

		
	    if (NULL != strstr(Data[index2], frep_info_descr[5]))
	    {
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->home_longitude = atof(Data[index2+1]);
	       pstrDjiDroneInfo->home_latitude = atof(Data[index2+2]);  
           //printf("---freq:%s\n", Data[index2+1]);
	       index2 = index2 + 3;
	       continue;
	    }


	    if (NULL != strstr(Data[index2], frep_info_descr[6]))
	    {
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->pilot_longitude = atof(Data[index2+1]);
	       pstrDjiDroneInfo->pilot_latitude = atof(Data[index2+2]);  
           //printf("---freq:%s\n", Data[index2+1]);
	       index2 = index2 + 3;
	       continue;
	    }
		if (NULL != strstr(Data[index2], frep_info_descr[7]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->height = strtof(Data[index2+1], &endptr);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[8]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->altitude = strtof(Data[index2+1], &endptr);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[9]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->east_speed = strtof(Data[index2+1], &endptr);  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}


		if (NULL != strstr(Data[index2], frep_info_descr[10]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->north_speed = strtof(Data[index2+1], &endptr);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}


		if (NULL != strstr(Data[index2], frep_info_descr[11]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->up_speed = strtof(Data[index2+1], &endptr);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}


		if (NULL != strstr(Data[index2], frep_info_descr[12]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->freq = strtof(Data[index2+1], &endptr);	  
		   //printf("[-^-^-freq]:%s, %.1f\n", Data[index2+1], atof(Data[index2+1]));
		   index2 = index2 + 2;
		   continue;
		}


		if (NULL != strstr(Data[index2], frep_info_descr[13]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->rssi = strtof(Data[index2+1], &endptr);  
		   //printf("[-^-^-rssi]:%s, %f\n", Data[index2+1], atof(Data[index2+1]));
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[14]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiDroneInfo->distance = strtof(Data[index2+1], &endptr);
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}

		index2 = index2 + 1;
    }
    pstrDjiDroneInfo->valid = 1;
		
    return 0;
}
/*******************************************************************************
* 函数名称: Handle_ParseDjiO4Freq
* 函数功能: DjiO4模块数据解析
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* freq_buffer 	   char*			  输入		   字符buffer地址
* pstrDjiO4AddInfo T_RhhkDjiAddInfo*  输出		   射频结构化数据
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_ParseDjiO4Freq(char *freq_buffer, T_RhhkDjiAddInfo *pstrDjiO4AddInfo)
{
    int index1 = 0;
	int index2 = 0;
	const char *delim1 = ",";
    const char *delim2 = "=";
    char *token_rdi1;
    char *token_rdi2;
    char *keystr1;
    char *keystr2;
	char rssi_scan_finish = 0;
    char rssi_scan_start = 0;
	char Data[DETCT_O4_DATA_COUNT][DJI_O4_PART_LENGTH];
	const char *frep_info_descr[DJI_O4_PART_COUNT] = {"#", "device", "undefined", "freq", "rssi"};
	char *endptr;
	float floatValue;
	float *arr_target_freq;
	
    if ((freq_buffer == NULL) || (pstrDjiO4AddInfo == NULL))
    {
        return -1;
	}

	keystr1 = (char *)freq_buffer;
    while(token_rdi1 = strtok_r(keystr1, delim1, &keystr1))
	{
        keystr2 = token_rdi1;
        while(token_rdi2 = strtok_r(keystr2, delim2, &keystr2))
        {
	        if (NULL != strstr(token_rdi2, frep_info_descr[0]))
	        {
	            rssi_scan_start = 1;
	        }

	        if (rssi_scan_start == 1)
            {
	            //printf("==>:[%d]  =ks2=>%s\n", index1, token_rdi2);
	            if (NULL != strstr(token_rdi2, frep_info_descr[4]))
	            {
	                rssi_scan_finish = 1;
	            }

		        if(index1 < DETCT_O4_DATA_COUNT)
		        {
                    strcpy(Data[index1], token_rdi2);
	                index1 = index1 + 1;
		        }
	        }
        }

	    if (rssi_scan_finish == 1)
	    {
	         break;
	    }
    }
	
	while(index2 < index1)
    {
        if (index2 >= DETCT_O4_DATA_COUNT)
	    {
	       break;
	    }

		if (NULL != strstr(Data[index2], frep_info_descr[0]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiO4AddInfo->seq_num = atoi(Data[index2+1]);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[1]))
	    {
		   // 使用 strtof 转换为 float
	       pstrDjiO4AddInfo->device_num = atoi(Data[index2+1]);    
           //printf("---freq:%s\n", Data[index2+1]);
	       index2 = index2 + 2;
	       continue;
	    }

		if (NULL != strstr(Data[index2], frep_info_descr[3]))
		{
		   // 使用 strtof 转换为 float
		   pstrDjiO4AddInfo->freq = strtof(Data[index2+1], &endptr);	  
		   //printf("---freq:%s\n", Data[index2+1]);
		   index2 = index2 + 2;
		   continue;
		}

		if (NULL != strstr(Data[index2], frep_info_descr[4]))
	    {
	       pstrDjiO4AddInfo->rssi = strtof(Data[index2+1], &endptr);    
           //printf("---freq:%s\n", Data[index2+1]);
	       index2 = index2 + 2;
	       break;
	    }
		index2 = index2+1;

    }
    pstrDjiO4AddInfo->valid = 1;
		
    return 0;
}


/*******************************************************************************
* 函数名称: Handle_DjiModuleData
* 函数功能: 创建gps采集任务
* 函数参数: 
* 参数名称: 	   类型 			     输入/输出    描述
* meas_sockfd 	   int			         输入		   sockfd
* pdji_server_addr	struct sockaddr_in * 输入		   sock地址结构
* sin_size          socklen_t            输入          sock协议字段长度
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_DjiModuleData(int djiol_sockfd, int meas_sockfd, struct sockaddr_in *pdji_server_addr, socklen_t sin_size)
{
    char *outdata = NULL;
    char rcv_buffer[DJI_BUFFER_SIZE]; //check fuction
    char rcv_backup[DJI_BUFFER_SIZE]; //check fuction
    char dji_encrypted[DJI_BUFFER_SIZE];
    u32 u32DataLength = 0;
    ssize_t SendBufSize = 0;
    ssize_t RcvBufSize = 0;
	  int dji_proxy;
	  int rcv_nbyte = 0;
    int flags = 0;
    int sockfd = 0;
	  int scan_sign = 0; //0-have not found, 1-have found
	  char *start_scan = "start"; 
	  char *lock_freq = "start -freq ";
	  char *junor_freq = "device=1855, model=DJI_OC123_20M, freq=5816.5, rssi=-65.0";
	  char *result_rssi = "device=1855, model=DJI_OC123_20M, freq=5815.0, rssi=-65.0, gpio=3,";
	  int  packet_index = 0;
	  int  lock_index = 0;
  	int  antenna_index = 0;
	float *arr_target_freq[DJI_PACKET_MAX_COUNT];
	u8   cnt_freq = 0;
	T_RhhkRssiPacketInfo  strLockFreqRssiGatherInfo;
	DJI_FLIGHT_INFO_Str strMessage;
	Drone_Detect_GpsInfo_Rpt struGpsInfoRpt;
	T_RhhkDjiAddInfo strDjiO4AddInfo;
	int retry_count = 0;
	char *token_message = NULL;
	char *username = "rhhk";
	char *password = "ksdhnewm239";
	char *drone_encrypted_message = NULL;
	T_RhhkDecryptDjiDroneInfo tDecryptedDjiDroneInfo;
        char dji_token_buffer[1024];
	char dji_droneinfo[5120];
	int dji_dronesize = 0;
  char dji_OutputJson[5120]; 
	int  dji_OutputLength = 0;
  uint8_t dji_rdi_drone_sn[17];
  T_RhhkMangerConfArg OutPoundDjiInfo;
  int usdelay = 0;
	int pound_dji_index = 0;
	int pound_dji_count = 0;

	  if (pdji_server_addr != NULL)
    { 
        //清空粗频缓冲区
		    memset(&strDjiDroneInfo, 0, DJI_PACKET_MAX_COUNT*sizeof(T_RhhkDjiDroneInfo));
		    memset(&strMessage, 0, sizeof(DJI_FLIGHT_INFO_Str));
		    memset(&struGpsInfoRpt, 0, sizeof(Drone_Detect_GpsInfo_Rpt));
		    memset(&strDjiO4AddInfo, 0, sizeof(T_RhhkDjiAddInfo));
		    memset(dji_token_buffer, 0, sizeof(dji_token_buffer));
   
        if (djiol_sockfd < 3)
        {
         if ((djiol_sockfd = CreateOnlineDecryptTask("***************", 5000)) < 3) 
	       {
             Dbg_Level_Print(LOG_ERROR, "dji udp2-201 first times Socket have Created failed!(sockfd:%d), Need ReCreate Socket!\n", djiol_sockfd);          
         }  
	       else
         {
             Dbg_Level_Print(LOG_INFO, "dji udp2-201 third Socket have Created successfully!(djol_sockfd=%d)\n", djiol_sockfd);
	       }
		  }

		  // 发送消息到服务器
		  sendto(meas_sockfd, start_scan, strlen(start_scan), 0, (struct sockaddr *)pdji_server_addr, sin_size);
		  Dbg_Level_Print(LOG_INFO, "Dji Send Freq Detection Scan Command:%s\n", start_scan);
		  Rhhk_SetDebugData(0, 0, 1, NULL, 0);
		  Rhhk_SetDebugData(0, 1, 1, NULL, 0);
        
        if(StartTimer(1, meas_sockfd, (struct sockaddr_in *)pdji_server_addr, sin_size, start_scan) == -1) 
		    {
            Dbg_Level_Print(LOG_INFO, "Handle_DjiModuleData Create Timer failed!\n");
            return 1;
        }
   
        while(1)
        {
			     //清空缓冲区
			     memset(rcv_buffer, 0, DJI_BUFFER_SIZE*sizeof(char));
			     memset(rcv_backup, 0, DJI_BUFFER_SIZE*sizeof(char));
			     memset(dji_encrypted, 0, DJI_BUFFER_SIZE*sizeof(char));
			     memset(dji_droneinfo, 0, sizeof(dji_droneinfo));
           memset(dji_OutputJson, 0, sizeof(dji_OutputJson));
           memset(&tDecryptedDjiDroneInfo, 0, sizeof(tDecryptedDjiDroneInfo));    
      
           if (djiol_sockfd < 3)
           {
              if ((djiol_sockfd = CreateOnlineDecryptTask("***************", 5000)) < 3) 
              {
                  Dbg_Level_Print(LOG_ERROR, "dji udp2-201 second times Socket have Created failed!(sockfd:%d), Need ReCreate Socket!\n", djiol_sockfd);
              }  
	            else
              {
                  Dbg_Level_Print(LOG_INFO, "dji udp2-201 second times Socket have Created successfully!(djol_sockfd=%d)\n", djiol_sockfd);
	            }
	        }
            
		    // 接收服务器的响应
		    rcv_nbyte = recvfrom(meas_sockfd, rcv_buffer, DJI_BUFFER_SIZE, 0, (struct sockaddr *)pdji_server_addr, &sin_size);
		    rcv_buffer[rcv_nbyte] = '\0'; // 确保字符串以 null 结尾
		    Dbg_Level_Print(LOG_INFO, "Receive Freq Result:[rcv_size=%d], [rcv_buffer=%s])\n", rcv_nbyte, rcv_buffer);

			  if (rcv_nbyte <= 0)
			  {
                if ((errno == EAGAIN) || (errno == EWOULDBLOCK))
                {
                    Dbg_Level_Print(LOG_INFO, "[INFO DJI UDP TIMEOUT] Timeout detected!\n");
				}
				else
				{
                     perror("recvfrom error");
				}
				close(meas_sockfd);
                Dbg_Level_Print(LOG_INFO, "Dji Udp Reconnecting attempt #%d...\n", ++retry_count);

				if ((meas_sockfd = Create_CliUdpSocket("************", 10152)) < 0)
				{
                    sleep(1);  
					continue;
				}

				sleep(1);
				Dbg_Level_Print(LOG_INFO, "Dji Udp Reconnecting Successfully\n");
				sendto(meas_sockfd, start_scan, strlen(start_scan), 0, (const struct sockaddr *)pdji_server_addr, sin_size);
		        Dbg_Level_Print(LOG_INFO, "Send Freq (Dji Reconnecting) Detection Scan Command:%s\n", start_scan);
				continue;
				
			}

            //收到数据包大小不合法
			if (rcv_nbyte <= MEAS_RESULT_MIN_SIZE)
			{
                continue;  
			}

            if (GpsDataInfo.dLatitude != 0.0f)
		    {
			    struGpsInfoRpt.local_latitude = GpsDataInfo.dLatitude;
                struGpsInfoRpt.local_longitude = GpsDataInfo.dLongitude;
			    struGpsInfoRpt.local_altitude = GpsDataInfo.dAltitude;
		    }
			else if (g_local_latitude  != 0.0f)
			{
                 struGpsInfoRpt.local_latitude = g_local_latitude;
                 struGpsInfoRpt.local_longitude = g_local_longitude;
				 struGpsInfoRpt.local_altitude = 0.0f;
			}
			else
			{
                 struGpsInfoRpt.local_latitude = 0.0f;
                 struGpsInfoRpt.local_longitude = 0.0f;
				 struGpsInfoRpt.local_altitude = 0.0f;
			}
			Rhhk_SetDebugData(0, 2, 1, NULL, 0);
			Rhhk_SetDebugData(0, 3, 1, rcv_buffer, rcv_nbyte);
      	
      memset(&OutPoundDjiInfo,0,sizeof(T_RhhkMangerConfArg));
			if(0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *)&OutPoundDjiInfo))
			{
				pound_dji_count = OutPoundDjiInfo.pound_dji_count;
        usdelay = OutPoundDjiInfo.usdelay;
			}
      //checke Recive Dji
			g_dji_recv_sign = 1;
      Dbg_Level_Print(LOG_INFO, "[Handle][dji_sign:%d] Recive Dji(%d)[%d] FreqScan Result:%s\n", g_dji_recv_sign, pound_dji_count, pound_dji_index,rcv_buffer); 
            
			
      if ((pound_dji_count > 0) && (pound_dji_count < POUNDING_GY_COUNT))
			{
			     if (pound_dji_index < pound_dji_count)
			     {
			         memset(rcv_buffer, 0, MEAS_BUFFER_SIZE*sizeof(char));	
               sprintf(rcv_buffer, "num=%02d, device=25%02d, serial=F4XFC22CS007T%03d, model=DjiPd%d-Mini 3 Pro, uuid=, drone_GPS=121.497734,31.329200," \
		                    "home_GPS=121.513325,31.334913, pilot_GPS=121.513325,31.334913, Height=0, Altitude=31.0,EastV=0.0, NothV=0.0,UpV=0.0, freq=5776.5," \
		                    "rssi=-80, distance=0.0km,", pound_dji_index, pound_dji_index, pound_dji_index, pound_dji_index);
			     }
				   else
				   {
               pound_dji_index = 0;
				   }
           pound_dji_index++;       
           usleep(usdelay);       
			  } 
       
        //判断O4
        if (NULL != strstr(rcv_buffer, "byte"))
        {
                dji_proxy = DJI_DRONE_O4_UP_LEVEL;
		    memcpy(rcv_backup, rcv_buffer, sizeof(rcv_backup));
				Handle_ParseDjiO4Freq(rcv_backup, &strDjiO4AddInfo);
				Dbg_Level_Print(LOG_INFO, "Handle_ParseDjiO4Freq:[valid=%d]\n", strDjiO4AddInfo.valid);
				if (strDjiO4AddInfo.valid == 1)
				{
          Rhhk_SetDebugData(2, 4, 1, NULL, 0);
					Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O4]####################RID_O4_Context:Start[lon:%ld,lat:%ld]\n",struGpsInfoRpt.local_longitude,struGpsInfoRpt.local_latitude);
					DjiO4DroneHandle(dji_proxy, &strDjiO4AddInfo, (DJI_FLIGHT_INFO_Str *)&strMessage, (Drone_Detect_GpsInfo_Rpt *)&struGpsInfoRpt); 
				}

				if (djiol_sockfd >= 3)
				{
				    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4-1]djiol_sockfd is exsit! (%d)\n", djiol_sockfd);
            Rhhk_SetDebugData(5, 0, 1, NULL, 0);
             
            if (ONLINE_SERVCE_FAILED == Judge_ServiceStatus())
					  {
						   close(djiol_sockfd);
               djiol_sockfd = -1;    //Set Socket Invalid  
               Rhhk_ResetTimeSwitch(ENABLE_UPDAGE_TOKEN);
               Dbg_Level_Print(LOG_ERROR, "[KEY-ONLINE-O4]Judge_ServiceStatus Check Online Server:Port Failed, ReCreate TCP LINK!\n");
						   continue;                                                                     
						}
            
				    //if (strlen((char *)dji_token_buffer) == 0) //need to upgrade
					  if (ENABLE_UPDAGE_TOKEN == Rhhk_GetTimeSwitch())
					  {
                Rhhk_SetDebugData(5, 1, 1, NULL, 0);  
					      memset((char *)dji_token_buffer, 0, sizeof(dji_token_buffer));
				        token_message = Rhhk_GetDecryptToken(djiol_sockfd, username, password);
                if (token_message == NULL)
                {
                    close(djiol_sockfd);
						        djiol_sockfd = -1; 
                    Rhhk_ResetTimeSwitch(ENABLE_UPDAGE_TOKEN);
                    Dbg_Level_Print(LOG_ERROR, "[KEY-ONLINE-O4]djiol get token failed! ReCreate Sokect GetToken!\n");
							      continue;
					      }                        
						    strncpy((char *)dji_token_buffer, token_message, sizeof(dji_token_buffer));
						    Rhhk_SetDebugData(5, 2, 1, NULL, 0);
						    Rhhk_SetDebugData(5, 3, 1, token_message, sizeof(dji_token_buffer));
                Rhhk_ResetTimeSwitch(DISABLE_UPDAGE_TOKEN);
					  }        
                                                             
				    drone_encrypted_message  = Rhhk_GetPareEncryptedDroneInfo((const char *)rcv_buffer, (char *)dji_encrypted);
				    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4-2]djiol_online_token (%s)\n", dji_token_buffer);
				    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4-3]djiol_online_rcv_buffer (%s)\n", rcv_buffer);
				    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4-3]djiol_online_encrypted (%s)\n", dji_encrypted);
				   
				    //if ((stren((char *)dji_token_buffer))&& (strlen((char *)dji_encrypted > 0)))
				    {	
              Rhhk_SetDebugData(6, 0, 1, NULL, 0);       
              //Rhhk_GetDecryptDroneInfo(djiol_sockfd, (char *)dji_token_buffer, dji_encrypted, dji_droneinfo, &dji_dronesize);
              Rhhk_GetDecryptDroneInfo(djiol_sockfd, (char *)dji_token_buffer, dji_encrypted, dji_droneinfo, &dji_dronesize);
              Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]####################ONLINE_O4Online_Context:Start,(dji_dronesize:%d)\n",dji_dronesize);                              
					    if (dji_dronesize > 400)
					    {	
                  Rhhk_SetDebugData(6, 1, 1, NULL, 0);                   
					        Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]$$$$$$$$$$$$$$$$$$$$ONLINE_O4line_Decrypted:%s\n", dji_droneinfo);
                  dji_OutputLength = sizeof(dji_OutputJson);                                                                                                                                  Rhhk_ParseResponseMessage((char *)dji_droneinfo, (char *)dji_OutputJson, &dji_OutputLength);           
					        
                  if (Rhhk_GetDecryptMessageFromDji((const char *)dji_OutputJson, dji_OutputLength, &tDecryptedDjiDroneInfo) == 0)                                                            { 
                      Rhhk_SetDebugData(6, 2, 1, NULL, 0);
                      Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4-4]@@@@@@@@@@@@@@@@@@@@(parse json successful)!\n");
                      memcpy(dji_rdi_drone_sn, &(g_RhhkReadRdiO4Info[0].drone_serial_num[4]), (sizeof(dji_rdi_drone_sn)));
                      //if (0 != Rhhk_JudgeRidDetectAndUpReport(dji_rdi_drone_sn,tDecryptedDjiDroneInfo.drone_serial_num,sizeof(tDecryptedDjiDroneInfo.drone_serial_num)))
                      if (0 != Rhhk_JudgeRidKeepliveDataValid(tDecryptedDjiDroneInfo.drone_serial_num,  sizeof(tDecryptedDjiDroneInfo.drone_serial_num)))
						          {
                          Rhhk_SetDebugData(6, 3, 1, dji_OutputJson, dji_OutputLength);                
    									    Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4-4]####################(SN1:%c)[lon:%ld,lat:%ld]\n",tDecryptedDjiDroneInfo.drone_serial_num[0], struGpsInfoRpt.local_longitude, struGpsInfoRpt.local_latitude);
					                Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4-4]####################(SN2:%c)\n",g_RhhkReadRdiO4Info[0].drone_serial_num[4]);
						              DjiOnlineDecryptedHandle(dji_proxy, &strDjiO4AddInfo, (DJI_FLIGHT_INFO_Str *)&strMessage, (T_RhhkDecryptDjiDroneInfo *)&tDecryptedDjiDroneInfo, (Drone_Detect_GpsInfo_Rpt *)&struGpsInfoRpt);
						          }
                  }
					    }
              else
              {         
                  close(djiol_sockfd);
                  djiol_sockfd = -1;    
                  Rhhk_ResetTimeSwitch(ENABLE_UPDAGE_TOKEN);                          
                  Dbg_Level_Print(LOG_ERROR, "[KEY-ONLINE-O4]Judge_ServiceStatus Third Check Online Server:Port Failed, ReCreate TCP LINK!\n");
						      continue;
              }              
              dji_dronesize	= 0;
		 	     }
				}
				else
				{
            if (djiol_sockfd > 0)
				    {
				        close(djiol_sockfd);
				    }
					  djiol_sockfd = -1;    //Set Socket Invalid
					  Rhhk_ResetTimeSwitch(ENABLE_UPDAGE_TOKEN);
            Dbg_Level_Print(LOG_ERROR, "[KEY-ONLINE-O4]Judge_ServiceStatus Second Check Online Server:Port Failed, ReCreate TCP LINK!\n");
					  Dbg_Level_Print(LOG_KEY, "[KEY-ONLINE-O4]djiol_sockfd of online socket is no exsit! (%d)\n", djiol_sockfd); 
				}
        memset(&(strDjiO4AddInfo), 0, sizeof(T_RhhkDjiAddInfo));
			}
			else
			{
            dji_proxy = DIJ_DRONE_O3_DOWN_LEVEL;
				    //解析粗频
	          packet_index = packet_index % DJI_PACKET_MAX_COUNT;
				    if(packet_index >= DJI_PACKET_MAX_COUNT)
				    {
				        packet_index = 0;
	              break;
				    }
								
	          Handle_ParseDjiModule((char *)rcv_buffer, &(strDjiDroneInfo[packet_index]));
				    Dbg_Level_Print(LOG_INFO, "Handle_ParseDjiModule:[valid=%d]\n", strDjiDroneInfo[packet_index].valid);
				    if ((strDjiDroneInfo[packet_index].valid != 0) && (Judge_StrStartWith((const char *)strDjiDroneInfo[packet_index].product_type_str, "0-") == 1))
				    {
					      Dbg_Level_Print(LOG_KEY, "[KEY-MAKE-O3]Make_O3_Context:Start\n");
					      DjiDroneHandle(dji_proxy, &(strDjiDroneInfo[packet_index]), (DJI_FLIGHT_INFO_Str *)&strMessage, (Drone_Detect_GpsInfo_Rpt *)&struGpsInfoRpt); 
					      memset(&(strDjiDroneInfo[packet_index]), 0, sizeof(T_RhhkDjiDroneInfo));
				        Rhhk_SetDebugData(0, 5, 1, NULL, 0);
            }
			}
			
			packet_index++; 
        }

	    if (djiol_sockfd >= 0)
      {
          close(djiol_sockfd);
      }
         
	    Dbg_Level_Print(LOG_INFO, "######## dji socker quit ############\n");
	}
    Dbg_Level_Print(LOG_INFO, "=================dji freq infomation================\n");
    return 0;
}
/*******************************************************************************
* 函数名称: Create_CliUdpSocket
* 函数功能: 创建内部模块采集socket任务
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* ip_remote 	   char*			  输入		   远端ip地址
* ip_local		   char*			  输入		   本地ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Create_CliUdpSocket(char *ip, int port)
{
    fd_set write_fds, read_fds;
    int flags;
    int sockfd;    
	int rcvBufSize = 1024 * 1024; // 1MB
    int sndBufSize = 1024 * 1024; // 1MB
    

    int opt = 1;
	struct sockaddr_in dji_server_addr;
    socklen_t sin_size = sizeof(dji_server_addr);
	struct timeval tv;

    if (ip == NULL)
    {
        Dbg_Level_Print(LOG_INFO, "udp Socket ip is null\n");
        return -1; 
	}
	
    const char *pTCPIP;
    int16_t  TCPPORT;

    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) 
	{
        perror("dji udp socket creation failed");
        exit(EXIT_FAILURE);
    }
    Dbg_Level_Print(LOG_INFO, "dji udp Socket have Created successfully\n");

    // set UDP server
    //int flags = fcntl(sockfd, F_GETFL, 0);
    //fcntl(sockfd, F_SETFL, flags | O_NONBLOCK);

    // 绑定客户端端口
    memset(&dji_server_addr, 0, sizeof(dji_server_addr));
    dji_server_addr.sin_family = AF_INET;
    dji_server_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    dji_server_addr.sin_port = htons(0);
    
    if (bind(sockfd, (struct sockaddr*)&dji_server_addr, sizeof(dji_server_addr)) < 0) 
	  {
        perror("bind() failed");
        close(sockfd);
        return -1;
    }

    // 设置接收超时
    tv.tv_sec = 15;
    tv.tv_usec = 0;
    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv)) < 0) 
	  {
        perror("setsockopt() failed");
        close(sockfd);
        return -1;
    }

    if(setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR|SO_REUSEPORT, &opt, sizeof(opt)))
    { 
       perror("dji setsockopt reuseadd and reuseport failed");
	   close(sockfd);
       exit(EXIT_FAILURE);
    }

    if(setsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &rcvBufSize, sizeof(rcvBufSize)))
	  { 
	   perror("dji setsockopt rcv buffer failed");
	   close(sockfd);
	   exit(EXIT_FAILURE);
	  }
	
    if(setsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, &sndBufSize, sizeof(sndBufSize)))
	  { 
	   perror("dji setsockopt rcv buffer failed");
	   close(sockfd);
	   exit(EXIT_FAILURE);
	  }

    return sockfd;
}


/*******************************************************************************
* 函数名称: Create_DjiModule_Socket
* 函数功能: Dji模块采集任务
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* ip_remote 	   char*			  输入		   远端ip地址
* ip_local		   char*			  输入		   本地ip地址
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Create_DjiModule_Socket(char *ip_remote, char *ip_local)
{
    fd_set write_fds, read_fds;
    int flags;
    int sockfd, djiol_sockfd;    
	  int rcvBufSize = 1024 * 1024; // 1MB
    int sndBufSize = 1024 * 1024; // 1MB
    char *olip = "***************";
	  int   olport = 5000;
	  char *token = NULL;
    T_RhhkMangerConfArg OutOnlineConfInfo;

    int opt = 1;
	  struct sockaddr_in dji_server_addr;
    socklen_t sin_size = sizeof(dji_server_addr);
    
    // set UDP server
    memset(&dji_server_addr, 0, sizeof(dji_server_addr));
    dji_server_addr.sin_family = AF_INET;
    dji_server_addr.sin_port = htons(DJI_MODULE_PORT);
    dji_server_addr.sin_addr.s_addr = inet_addr(DJI_MODULE_ADDR);

    if ((sockfd = Create_CliUdpSocket("************", 10152)) < 0) 
    {
	      Dbg_Level_Print(LOG_ERROR, "dji udp1-18 Socket have Created failed!\n");
        exit(EXIT_FAILURE);
    }
    Dbg_Level_Print(LOG_INFO, "dji udp1-18 Socket have Created successfully!\n");

    memset(&OutOnlineConfInfo, 0,sizeof(T_RhhkMangerConfArg));
    if(0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *)&OutOnlineConfInfo))
    {    
       if ((strlen(OutOnlineConfInfo.ol_ip) > 7) && (OutOnlineConfInfo.ol_port > 0))
       {
          olip = (char *)OutOnlineConfInfo.ol_ip;
		      olport = OutOnlineConfInfo.ol_port;
		      Dbg_Level_Print(LOG_INFO, "dji udp2-201 Get Configure Info[newolip:%s, newolport:%d]!\n", OutOnlineConfInfo.ol_ip, OutOnlineConfInfo.ol_port);
	     }
    }	 

	  Dbg_Level_Print(LOG_INFO, "dji udp2-201 Socket have Created[olip:%s, olport:%d]!\n", olip, olport);
 
    if ((djiol_sockfd = CreateOnlineDecryptTask(olip, olport)) < 3) 
    {
        Dbg_Level_Print(LOG_ERROR, "dji udp2-201 Socket have Created failed[olip:%s, olport:%d]!\n", olip, olport);     
    }
    else
    {
        Dbg_Level_Print(LOG_INFO, "dji udp2-201 first Socket have Created successfully![olip:%s, olport:%d, djol_sockfd:%d]!\n", olip, olport,djiol_sockfd);
	  }
     
    while(1)
    { 
        if (djiol_sockfd < 3)
        {
          if ((djiol_sockfd = CreateOnlineDecryptTask(olip, olport)) < 3) 
	        {
              Dbg_Level_Print(LOG_ERROR, "dji udp2-201 second Socket have Created failed[olip:%s, olport:%d]!\n", olip, olport);
          }
          else
          {
              Dbg_Level_Print(LOG_INFO, "dji udp2-201 second Socket have Created successfully[olip:%s, olport:%d, djol_sockfd:%d]!\n", olip, olport,djiol_sockfd);
          } 
       }
        
        Handle_DjiModuleData(djiol_sockfd, sockfd, (struct sockaddr_in *)&dji_server_addr, sin_size);
	      usleep(100);	
    }
	
    return 1;
}

/*******************************************************************************
* 函数名称: Handle_DjiDroneGatherTask
* 函数功能: Dji线程处理函数
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* VirtulArg 	   VirtuArg*		  输入		   已优化
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_DjiDroneGatherTask(void *VirtulArg)
{
    //int antenna_code[8] = {0b000,0b001,0b010,0b011,0b100,0b101,0b110,0b111};
    int antenna_pos_code[8] = { 0b000, 0b100,0b010, 0b110,0b001,0b101,0b011,0b111 };

    Dbg_Level_Print(LOG_INFO, "3 pthread configure rssi and read rssi argument!\n");
   
    //using new method 
    Create_DjiModule_Socket("ip_remote", "ip_local"); 
	
    pthread_exit(NULL);
    return;
}

/*******************************************************************************
* 函数名称: Create_DjiModule_Pthread
* 函数功能: Dji模块主任务创建
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* void 	   
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void Create_DjiModule_Pthread(void)
{
   pthread_t  tid_rid;
   int  VirtulArg = 0; //invalid, after upgrade
   int  ret;

   //pthread_mutex_init(&mutex_tid, NULL);

   if (VirtulArg < 0)
   {
       perror("Dji Judge Argment Invalid.\n");
       exit(EXIT_FAILURE);
   }
   Dbg_Level_Print(LOG_INFO, "1 create pthread for dji module start!\n");
   ret = pthread_create(&tid_rid, NULL, Handle_DjiDroneGatherTask, (void *)&VirtulArg);
   if (ret != 0)
   {
       perror("StartDjiDroneTask:Pthread_Create Failed!");
       return;
   }
   pthread_detach(tid_rid);
   Dbg_Level_Print(LOG_INFO, "2 create dji pthread for rssi finished!\n");

   //pthread_join(tid_rid, NULL);
   //pthread_mutex_destroy(&mutex_tid);
   return;
}


/*******************************************************************************
* 函数名称: Handle_MakeVersionInfo
* 函数功能: Dji模块主任务创建
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* void	   
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Handle_MakeVersionInfo(void)
{
   FILE *pchn_fp, *wrif_fp;    
   char buffer[128];    
   char serial[64] = {0};
   char wri_buffer[1024] = {0};
   char CompilationTime[64] = {0};
   char device_info[64] = {0};

   char month_str[4];    
   int day, year;    
   char date_str[11];

   const char *months[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
   const char* wrifilename = "/home/<USER>/DeviceInfo.txt";    
   const char* content = "这是要写入的内容\n第二行内容";        
   
   char *ProductName = "产品名称:无线电探测系统";
   char *ProductType = "规格型号:谛听L-Q型";
   char *DeviceInfo  = "设备编码:";
   char *DeviceSerial = "设备序号:";
   char *VendorName  = "厂    商:北京融合汇控科技有限公司";
   char *Compilation = "编译时间:";
   char *ContactInformation = "联系方式:<EMAIL>";
   

   pchn_fp = popen("sudo lshw -class system | grep \"serial:\" | head -n 1 | awk '{print $2}'", "r");    
   if (pchn_fp == NULL) 
   {        
       perror("popen failed");        
	   return 1;    
   }    

   if (fgets(buffer, sizeof(buffer), pchn_fp) != NULL) 
   {        
       sscanf(buffer, "%63s", serial);  // 防止溢出        
	     Dbg_Level_Print(LOG_INFO, "Hardware Serial: %s\n", serial);    
   } 
   else 
   {        
	     Dbg_Level_Print(LOG_INFO, "No serial number found.\n");    
   }    
   pclose(pchn_fp);


   memset(device_info, 0, sizeof(device_info));
   sprintf(device_info, "%s%s", DeviceInfo, &(g_device_code[0]));

   memset(wri_buffer, 0, sizeof(wri_buffer));
   memset(date_str, 0, sizeof(date_str));
   memset(CompilationTime, 0, sizeof(CompilationTime));
   sscanf(__DATE__, "%3s %d %d", month_str, &day, &year);
   int month = 0;    
   for (int i = 0; i < 12; i++) 
   {        
       if (strcmp(month_str, months[i]) == 0) 
	   {            
	       month = i + 1;            
		   break;        
	   }    
   }
   snprintf(date_str, sizeof(date_str), "%04d-%02d-%02d", year, month, day);   
   sprintf(CompilationTime, "%s %s", date_str, __TIME__);
   sprintf(wri_buffer, "%s\n%s\n%s\n%s%s\n%s\n%s%s\n%s\n", ProductName, ProductType, device_info,\
   	         DeviceSerial, serial, VendorName, Compilation, CompilationTime, ContactInformation);
   
   
   FILE* file = fopen(wrifilename, "w");    
   if (file == NULL) 
   {        
   	    perror("open file failed");        
        return EXIT_FAILURE;    
   }        
   // 写入内容    
   if (fputs(wri_buffer, file) == EOF) 
   {        
   	   perror("write file failed");        
	   fclose(file);        
	   return EXIT_FAILURE;    
   }        
   fclose(file);    
   Dbg_Level_Print(LOG_INFO, "successful write: %s\n", wrifilename);    
   return EXIT_SUCCESS;

}

/******************************************************************************
* 函数名称: Rhhk_MakeRequestTokenFromDji
* 函数功能: 构建token请求报文
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* username         char *             
* password         char * 
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
char *Rhhk_MakeRequestTokenFromDji(char *username, char *password)
{
    char MessageBuffer[1024];
  
    if ((username == NULL) || (password == NULL))
    {
        Dbg_Level_Print(LOG_INFO, "Rhhk_MakeReqestMessageForDjiToken input argument username or password is NULL!\n");
		return NULL;
	}

	memset(MessageBuffer, 0, sizeof(MessageBuffer));
	sprintf(MessageBuffer, "HTTP GET: http://***************:5000/api/login?username=%s&password=%s", username, password);

    return MessageBuffer;
}


/******************************************************************************
* 函数名称: Rhhk_ParseResponseDjiToken
* 函数功能: 解析token报文
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* TokenMessage     char *             
* 返回值:          token
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
char * Rhhk_ParseResponseDjiToken(const char *TokenJsonMessage)
{
    if (TokenJsonMessage == NULL)
    {
        Dbg_Level_Print(LOG_ERROR, "Rhhk_ParseResponseDjiToken input argument TokenJsonMessage is NULL!\n");
		return NULL;
	}

    cJSON* root = cJSON_Parse(TokenJsonMessage);    
	if (!root) 
	{        
		Dbg_Level_Print(LOG_ERROR, "JSON Parse Failed!\n");        
		return NULL;    
	}    
	// 检查success字段    
	cJSON* success = cJSON_GetObjectItem(root, "success");    
	if (0 != strcmp(success->valuestring, "true")) 
	{        
	    Dbg_Level_Print(LOG_ERROR, "Get Message Failed: %s\n", cJSON_GetStringValue(cJSON_GetObjectItem(root, "msg")));        
		cJSON_Delete(root);        
		return NULL;    
	}    
	// 提取data对象    
	cJSON* data = cJSON_GetObjectItem(root, "data");    
	if (data) 
	{               
	    cJSON* token = cJSON_GetObjectItem(data, "token");        
		if (token) 
		{
			Dbg_Level_Print(LOG_INFO, "Token: %s\n", token->valuestring);
			cJSON_Delete(root);
			return token->valuestring;
		}

		// 解析orders数组        
		cJSON* orders = cJSON_GetObjectItem(data, "orders");        
		if (orders) 
		{                        
			int size;
			size = cJSON_GetArraySize(orders);	
			{                
			    Dbg_Level_Print(LOG_INFO, " - %d\n", size);            
			}        
		}    
	}    
	cJSON_Delete(root);

	return NULL;
}


/******************************************************************************
* 函数名称: Rhhk_MakeEncryptMessageToDji
* 函数功能: 解析token报文
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* TokenMessage     char *             
* 返回值:          token
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
char * Rhhk_MakeEncryptMessageToDji(const char *TokenJsonMessage, const char *DroneEncrytedMessage)
{
    char EncrptMessageBuffer[4096]; 
    if ((TokenJsonMessage == NULL) || (DroneEncrytedMessage == NULL))
    {
        Dbg_Level_Print(LOG_ERROR, "Rhhk_ParseResponseDjiToken input argument TokenJsonMessage is NULL!\n");
		return NULL;
	}

	memset(EncrptMessageBuffer, 0, sizeof(EncrptMessageBuffer));
	sprintf(EncrptMessageBuffer, "HTTP GET: http://***************:5000/api/yd/decryptl?hex=%s&token=%s", DroneEncrytedMessage, TokenJsonMessage);
	
	return EncrptMessageBuffer;
}


/******************************************************************************
* 函数名称: Rhhk_GetDecryptMessageFromDji
* 函数功能: 解析token报文返回结构化信息
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* DroneDecrytedMessage     char *             
* 返回值:                  T_RhhkDecryptDjiDroneInfo*
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_GetDecryptMessageFromDji(const char *DroneDecrytedMessage, int JsonLength, T_RhhkDecryptDjiDroneInfo *ptOutputDroneData)
{
    DtChar *out = NULL;
    DtAdapt *json = NULL ;
    DtAdapt *object = NULL;
    DtAdapt *item = NULL;
	  T_RhhkDecryptDjiDroneInfo tDjiEncryptDroneInfo;	
	  char *waitforkey = "no_key";
	  char *nosupport = "This package is not supported!";

	  const char *DjiKeyInfo[] = 
    {
        "sn", "uuid", "type", "lon", "lat", "alt", "height", "x", "y", "z", "yaw",
		"gps_time", "pilot_lon", "pilot_lat", "home_lon", "home_lat", "model" 
    };

    if ((DroneDecrytedMessage == NULL) || (ptOutputDroneData == NULL))
    {
        Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji input argument DroneDecrytedMessage is NULL!\n");
		    return -1;
	  }

    if (JsonLength <= 0)
    {
        Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji input Json Message len is %d!\n", JsonLength);
        return -1;
	  }

	  json=g_pApiSet->m_pApi_Parse(DroneDecrytedMessage);
    if (!json) 
    {
        Dbg_Level_Print(LOG_ERROR, "Error before: [%s]\n", g_pApiSet->m_pApi_GetErrorPtr());
		return -1;
    }
    else
    {
		memset(&tDjiEncryptDroneInfo, 0, sizeof(T_RhhkDecryptDjiDroneInfo));	
		#if 0
        item = g_pApiSet->m_pApi_GetObjectItem(json, "msg");
		if(NULL == item)
		{
		    out = g_pApiSet->m_pApi_Print(json);
			Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji is waitting for encrypt key, %s\n", out);
			return -1;
		}
		
        if (0 == strcmp(item->valuestring, waitforkey))
        {
            Dbg_Level_Print(LOG_INFO, "Rhhk_GetDecryptMessageFromDji is waitting for encrypt key (%s)!\n", item->valuestring);
			return -1;
		}
		else if(0 == strcmp(item->valuestring, nosupport))
		{
            Dbg_Level_Print(LOG_INFO, "Rhhk_GetDecryptMessageFromDji no upport to detect this type drone!\n",item->valuestring);
			return -1;
		}
		#endif
		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[0]);
		if(NULL == item)
		{
		      out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node0: %s\n", out);
			  return -1;
		}
        strncpy(tDjiEncryptDroneInfo.drone_serial_num, item->valuestring, sizeof(tDjiEncryptDroneInfo.drone_serial_num));

		
        item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[1]);
		if(NULL == item)
		{
		      out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node1: %s\n", out);
			  return -1;
		}
        strncpy(tDjiEncryptDroneInfo.drone_uuid, item->valuestring, sizeof(tDjiEncryptDroneInfo.drone_uuid));
        
		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[2]);
		if(NULL == item)
		{
		      out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node2: %s\n", out);
			  return -1;
		}
        tDjiEncryptDroneInfo.product_type = item->valueint;
	
		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[3]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node3: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.drone_longitude = item->valuedouble;

	    item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[4]);
	    if(NULL == item)
	    {
	 	     out = g_pApiSet->m_pApi_Print(json);
		     Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node4: %s\n", out);
		     return -1;
	    }
	    tDjiEncryptDroneInfo.drone_latitude = item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[5]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node5: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.drone_altitude = item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[6]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node6: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.drone_height = item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[7]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node7: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.east_speed = item->valuedouble;


		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[8]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node8: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.north_speed, item->valuedouble;


		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[9]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node9: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.up_speed, item->valuedouble;

        item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[10]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node10: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.drone_yaw, item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[11]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node10: %s\n", out);
			  return -1;
		}
		strncpy(tDjiEncryptDroneInfo.gpstime, item->valuestring, sizeof(tDjiEncryptDroneInfo.gpstime));

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[12]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node11: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.pilot_longitude = item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[13]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node12: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.pilot_latitude = item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[14]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node13: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.home_longitude = item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[15]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node14: %s\n", out);
			  return -1;
		}
		tDjiEncryptDroneInfo.home_latitude = item->valuedouble;

		item = g_pApiSet->m_pApi_GetObjectItem(json, DjiKeyInfo[16]);
		if(NULL == item)
		{
			  out = g_pApiSet->m_pApi_Print(json);
			  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptMessageFromDji can not find node15: %s\n", out);
			  return -1;
		}
		strncpy(tDjiEncryptDroneInfo.product_type_str, item->valuestring, sizeof(tDjiEncryptDroneInfo.product_type_str));
    }
	memcpy(ptOutputDroneData, &tDjiEncryptDroneInfo, sizeof(tDjiEncryptDroneInfo));
	Dbg_Level_Print(LOG_INFO, "======js7finished====drone_serial_num:%s\n", tDjiEncryptDroneInfo.drone_serial_num);
    return 0;
}

/******************************************************************************
* 函数名称: CreateOnlineDecryptTask
* 函数功能: 创建Dji无人机任务
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* ipaddr           char *             
* 返回值:          void
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int CreateOnlineDecryptTask(char *ipaddr, int port)
{
    // [步骤二：创建socket并连接到服务器]
    int sockfd;
    struct sockaddr_in server_addr;

    signal(SIGPIPE, SIG_IGN);

    if (ipaddr == NULL)
    {
        Dbg_Level_Print(LOG_ERROR, "CreateOnlineDecryptTask ipaddr is null!\n");
	      return -1;
    }

    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0)
    {
        Dbg_Level_Print(LOG_ERROR, "Dji Decrypt-1 Create Socket Failed!(sockfd=%d)\n", sockfd);
        return -1;
    }

    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    inet_pton(AF_INET, ipaddr, &server_addr.sin_addr);
    
    if (connect(sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0)
    {
        Dbg_Level_Print(LOG_ERROR, "Dji Decrypt-2 Socket Connect Socket Failed!(sockfd=%d)\n", sockfd);
        close(sockfd);
        return -1;
    }

    return sockfd;
}

/******************************************************************************
* 函数名称: Rhhk_GetDecryptToken
* 函数功能: 解析token报文返回结构化信息
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* DroneDecrytedMessage     char *             
* 返回值:                  T_RhhkDecryptDjiDroneInfo*
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
char *Rhhk_GetDecryptToken(int dj_sockfd, char *username, char *password)
{
  char *token = NULL;
	char *olip = "***************";
	char *ol_username = "rhhk";
	char *ol_password = "ksdhnewm239";
	int  olport = 5000;
  char request_login[256] = {0};
	char request_addr[256] = {0};
	T_RhhkMangerConfArg OutOnlineParse;
  int NSendByte = 0;
 
  if ((username == NULL) || (password == NULL))
  {
     Dbg_Level_Print(LOG_ERROR, "username or password is NULL\n");
	 	 return NULL;
	}

	if (dj_sockfd <= 0)
  {
    Dbg_Level_Print(LOG_ERROR, "dj_sockfd is failed!\n");
		return NULL;
	}

  memset(&OutOnlineParse, 0,sizeof(T_RhhkMangerConfArg));
  if(0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *)&OutOnlineParse))
  {    
     if ((strlen(OutOnlineParse.ol_ip) > 7) && (OutOnlineParse.ol_port > 0))
     {
        olip = (char *)OutOnlineParse.ol_ip;
		    olport = OutOnlineParse.ol_port;
		    ol_username = OutOnlineParse.username;
	      ol_password = OutOnlineParse.password;
		    Dbg_Level_Print(LOG_INFO, "dji udp2-201 Get Online Parse[newolip:%s, newolport:%d]!\n", OutOnlineParse.ol_ip, OutOnlineParse.ol_port);
	   }
  }	 

	memset(request_login, 0, sizeof(request_login));
	memset(request_addr, 0, sizeof(request_addr));
	sprintf(request_login, "GET /api/login?username=%s&password=%s HTTP/1.1\r\n", ol_username, ol_password);
	sprintf(request_addr, "Host: %s:%d\r\n", olip, olport);

	// [步骤三：构造HTTP GET请求]
	char request[1024];
	snprintf(request, sizeof(request), request_login);
	strcat(request, request_addr);
	strcat(request, "User-Agent: Custom-Agent/1.0\r\n");
	strcat(request, "Connection: keep-alive\r\n");
	strcat(request, "\r\n"); // 空行表示请求头结束

	// [步骤四：发送HTTP GET请求]
	//if (send(dj_sockfd, request, strlen(request), 0) < 0)
	//{
	//	Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptToken send token request is failed!\n");
		//close(dj_sockfd);
		//exit(EXIT_FAILURE);
	//	return NULL;
	//}
 
  NSendByte = send(dj_sockfd, request, strlen(request), MSG_NOSIGNAL);
	if ((NSendByte < 0) || (errno == EPIPE))
	{
		Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptToken Connection Socket Pipe broken!\n");
		close(dj_sockfd);
		return NULL;
	}

	// [步骤五：接收服务器响应]
	char buffer[4096];
	int bytes_received;
  Dbg_Level_Print(LOG_INFO, "[######GET TOKEN REQUEST######]:%s\n", request);
	Dbg_Level_Print(LOG_INFO, "服务器响应:\n");
	if((bytes_received = recv(dj_sockfd, buffer, sizeof(buffer) - 1, 0)) > 0)
	{
		buffer[bytes_received] = '\0';
		Dbg_Level_Print(LOG_INFO, "%s", buffer);

    token = Rhhk_ParseToken((const char *)buffer);
		return token;
	}
  else
	{
		Dbg_Level_Print(LOG_INFO, "接收失败: dj_sockfd online get token is failed\n");
	}

  return NULL;
}
/******************************************************************************
* 函数名称: Rhhk_GetDecryptDroneInfo
* 函数功能: 解析获取解码后的信息
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* dj_sockfd        int
* token            char *
* drone_encrypted_message    char *
* 返回值:          char*
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
char *Rhhk_GetDecryptDroneInfo(int dj_sockfd, char *token, char *drone_encrypted_message, char *output_data, int *output_size)
{
  char request_addr[256] = {0};
	char *olip = "***************";
	int  olport = 5000; 
  T_RhhkMangerConfArg OnlineRequestInfo;
  int NSendByte = 0;
 
  if ((token == NULL) || (drone_encrypted_message == NULL)|| (output_data == NULL))
  {
    Dbg_Level_Print(LOG_ERROR, "token or drone_encrypted_message or output_data is NULL\n");
		return NULL;
	}

  if (dj_sockfd < 3)
  {
      Dbg_Level_Print(LOG_ERROR, "dj_sockfd (%d) is failed!, Pause Parse Process\n", dj_sockfd);
		  return NULL;
	}
	
  memset(&OnlineRequestInfo, 0,sizeof(T_RhhkMangerConfArg));
  if(0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *)&OnlineRequestInfo))
  {    
     if ((strlen(OnlineRequestInfo.ol_ip) > 7) && (OnlineRequestInfo.ol_port > 0))
     {
          olip = (char *)OnlineRequestInfo.ol_ip;
		      olport = OnlineRequestInfo.ol_port;
		      Dbg_Level_Print(LOG_INFO, "dji udp2-201 Get Online Parse[newolip:%s, newolport:%d]!\n", OnlineRequestInfo.ol_ip, OnlineRequestInfo.ol_port);
	   }
  }	 
  memset(request_addr, 0, sizeof(request_addr));
	sprintf(request_addr, "Host: %s:%d\r\n", olip, olport);
 
	// [步骤三：构造HTTP GET请求]     
	char request[5120];                 
	snprintf(request, sizeof(request), "GET /api/yd/decryptl?hex=%s&token=%s HTTP/1.1\r\n", drone_encrypted_message, token);
  strcat(request, request_addr);
	strcat(request, "User-Agent: Custom-Agent/1.0\r\n");
	strcat(request, "Connection: keep-alive\r\n");
	strcat(request, "\r\n"); // 空行表示请求头结束
	Dbg_Level_Print(LOG_KEY, "SendDecryptedRequest:[%s]\n", request);
 
	// [步骤四：发送HTTP GET请求]
	//if (send(dj_sockfd, request, strlen(request), 0) < 0)
	//{
	//	Dbg_Level_Print(LOG_ERROR, "Tcp Client (sockfd:%d) Send Failed[len:%d]\n", dj_sockfd, strlen(request));
  //  return NULL;
	//}
  //Dbg_Level_Print(LOG_INFO, "[######GET PARSE REQUEST######]:%s\n", request);
  NSendByte = send(dj_sockfd, request, strlen(request), MSG_NOSIGNAL); 
  if ((NSendByte < 0) || (errno == EPIPE))
	{
		  Dbg_Level_Print(LOG_ERROR, "Rhhk_GetDecryptDroneInfo Connection Socket Pipe broken!\n");
		  return NULL;
	}
  Dbg_Level_Print(LOG_INFO, "[######GET PARSE REQUEST######]:%s\n", request);
  
	// [步骤五：接收服务器响应]
	char buffer[4096];
	int bytes_received;

	Dbg_Level_Print(LOG_INFO, "Recv Response:(sockfd=%d)\n", dj_sockfd);
	if ((bytes_received = recv(dj_sockfd, buffer, sizeof(buffer) - 1, 0)) > 0)
	{
		 buffer[bytes_received] = '\0';
	   Dbg_Level_Print(LOG_INFO, "DjiO4_Online:%s, Recv_Len:%d)\n", buffer, bytes_received);
     memcpy(output_data, buffer, bytes_received);
		*output_size = bytes_received;
		return (char *)buffer;
	}

	if (bytes_received <= 0)
	{
      Dbg_Level_Print(LOG_ERROR, "Recv Response failed from Socket:%d!\n", dj_sockfd);   
	}

    // [步骤六：关闭socket并退出程序]
    //close(dj_sockfd);
    //exit(EXIT_SUCCESS);
    return NULL;
}

/******************************************************************************
* 函数名称: Rhhk_ParseToken
* 函数功能: 解析Token
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* json_message     char*
* 返回值:          token               char*  
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
char *Rhhk_ParseToken(const char *json_message)
{
    if (json_message == NULL)
    {
        Dbg_Level_Print(LOG_ERROR, "json_message is NULL\n");
		return NULL;
	}

    Dbg_Level_Print(LOG_INFO, "%s\n", json_message);
    const char *pattern = "\"token\":\"([^\"]+)\"";

    regex_t regex;
    regmatch_t matches[MAX_MATCHES];

    // 编译正则表达式
    if (regcomp(&regex, pattern, REG_EXTENDED) != 0) 
	{
        fprintf(stderr, "正则表达式编译失败\n");
        return NULL;
    }

    // 执行匹配
    if (regexec(&regex, json_message, MAX_MATCHES, matches, 0) == 0) 
	{
        if (matches[1].rm_so != -1) 
		{  
            int start = matches[1].rm_so;
            int end = matches[1].rm_eo;
            int len = end - start;

            char *token = (char *)malloc(len + 1);
            strncpy(token, json_message + start, len);
            token[len] = '\0';

            //Dbg_Level_Print(LOG_INFO, "[token info]: %s\n", token);
            return token;
        }
    } 
	else 
	{
        Dbg_Level_Print(LOG_INFO, "No found token info\n");
    }
    return NULL;
}


/******************************************************************************
* 函数名称: Rhhk_GetPareEncryptedDroneInfo
* 函数功能: 获取纯加密后无人机字节流信息
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* rcv_buffer       char*
* 返回值:          droneinfo          char*  
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
char *Rhhk_GetPareEncryptedDroneInfo(const char *rcv_buffer, char *output_drone)
{
	if ((rcv_buffer == NULL) || (output_drone == NULL))
	{
		Dbg_Level_Print(LOG_ERROR, "Rhhk_GetPareEncryptedDroneInfo: rcv_buffer is NULL\n");
		return NULL;
	}

	regex_t regex;
	regmatch_t matches[2];
	char pattern[] = "byte,([0-9a-fA-F,]+)";

	if (regcomp(&regex, pattern, REG_EXTENDED)) 
	{
		fprintf(stderr, "rex express is failed!\n");
		return NULL;
	}

	if (regexec(&regex, rcv_buffer, 2, matches, 0) == 0) 
	{
		int start = matches[1].rm_so;
		int end = matches[1].rm_eo;
		char hex_data[end-start+1];

		strncpy(hex_data, rcv_buffer + start, end - start);
		hex_data[end-start] = '\0';

		// 移除所有逗号
		char* p = hex_data;
		char* q = hex_data;
		while (*q) 
		{
			if (*q != ',') 
			{
				*p++ = *q;
			}
			q++;
		}
		*p = '\0';

		//Dbg_Level_Print(LOG_INFO, "result: %s\n", hex_data);
		regfree(&regex);
		strncpy(output_drone, hex_data, DJI_BUFFER_SIZE*sizeof(char));
		return hex_data;
		
	} 
	else 
	{
	    Dbg_Level_Print(LOG_INFO, "Rhhk_GetPareEncryptedDroneInfo(need to upgrade)!\n");
	}
	regfree(&regex);
	return NULL;
}


/******************************************************************************
* 函数名称: Rhhk_JudgeRidDetectAndUpReport
* 函数功能: 判断RID探测和上报
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* RidSn           char *             
* DjiSn           char *
* 返回值:         int(1--上报， 0--不上报)
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_JudgeRidDetectAndUpReport(uint8_t RidSn[16], uint8_t DjiSn[16],  size_t length)
{
    char *token = NULL;
    if (length > 17)
    {
        Dbg_Level_Print(LOG_ERROR, "RidSn or DjiSn is beyond of maxlength(16)!\n");
		    return 0;
  	}
	  	 
    if (0 != memcmp(RidSn, DjiSn, length))
    { 
       return 1;  
	 }
	 return 0;
}	

/******************************************************************************
* 函数名称: Rhhk_JudgeRidKeepliveDataValid
* 函数功能: 判断RID探测和上报
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述            
* OnlineDjiSn           char *
* 返回值:         int(1--上报， 0--不上报)
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_JudgeRidKeepliveDataValid(uint8_t OnlineDjiSn[16],  size_t length)
{
    int index = 0;
	long CurrentOnlineDetectTime = 0;
    char *token = NULL;
    if (length > 17)
    {
        Dbg_Level_Print(LOG_ERROR, "OnlineDjiSn is beyond of maxlength(16)!\n");
		return 0;
	}

    CurrentOnlineDetectTime = Rhhk_GetDroneKeeplive();
		
    while (index++ < DJI_DRONE_O4_LIB_MAX)
	{
        if ((g_DjiDroneRecordLib[index].data_valid == 1) 
			&& (0 == memcmp(&(g_DjiDroneRecordLib[index].drone_serial_num), OnlineDjiSn, length))
			&& (CurrentOnlineDetectTime - g_DjiDroneRecordLib[index].drone_keeplive) > DJI_DRONE_O4_KEEPLIVE_TIME)
        {
            return 1;
		}
	}

	if (index >= DJI_DRONE_O4_LIB_MAX)
	{
        return 1;
	}

	return 0;
}	

/******************************************************************************
* 函数名称: Rhhk_ParseResponseMessage
* 函数功能: 解析Req响应结果
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* ReqResponse      char *             input
* OutputJson       char *             output
* OutputLength     int                in/output
* 返回值:         int(-1--failed， 0--success)
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_ParseResponseMessage(char *ReqResponse, char *OutputJson, int *OutputLength)
{
    if ((ReqResponse == NULL) || (OutputJson == NULL) || (OutputLength == NULL))
    {
        Dbg_Level_Print(LOG_ERROR, "ReqResponse or OutputJson or is NULL\n");
		    *OutputLength = 0;
		    return -1;
	  }

    char *start = NULL;
    char *end  = NULL;
	  int index = 0;
	  int length = 0;
	  int find_sign = 0;

    while(index++ < strlen(ReqResponse))
    {
	    if ((strchr((ReqResponse+index), '{')) != NULL)
	    {
		    start = strchr((ReqResponse+index), '{');
			find_sign += 1;
	    }

	    if ((end = strchr((ReqResponse+index), '}')) != NULL)
	    {
	        length = (end - start + 1);
			find_sign += 2;
		    break;
	    }
    }

	  if ((*OutputLength > length) && (find_sign == 3))
	  {
        memcpy(OutputJson, start, length);
		    *OutputLength = length;
	  }
    else
    {
        *OutputLength = 0;
		    Dbg_Level_Print(LOG_ERROR, "Rhhk_ParseResponseMessage json length is beyond of maxvalue(%d)\n", length);
	  }
	
	return 0;
}

/******************************************************************************
* 函数名称: Rhhk_JudgeDirExistAndCreate
* 函数功能: 判断目录是否存在，创建并赋予权限
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* dir_path         char *             input
* 返回值:          int(-1--failed， 0--success)
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_JudgeDirExistAndCreate(const char *dir_path) 
{    
    if (dir_path == NULL)
    {
        Dbg_Level_Print(LOG_ERROR, "Rhhk_JudgeDirExistAndCreate  dir_path is NULL\n");
		return -1;
	}       
     
    struct stat st = {0};    
	if (stat(dir_path, &st) == -1) 
	{        
	    // 目录不存在，创建目录        
	    if (mkdir(dir_path, 0755) == -1) 
		{           
		    perror("创建目录失败");            
			return 1;        
		}        
		Dbg_Level_Print(LOG_INFO, "目录创建成功: %s\n", dir_path);    
	} 
	else 
	{        
	    Dbg_Level_Print(LOG_INFO, "目录已存在: %s\n", dir_path);    
	}        
	return 0;
}

/******************************************************************************
* 函数名称: Rhhk_UdpToTcp
* 函数功能: 设备转发至平台
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* arg              T_RhhkThreadArgs   input        sockstruct
* 返回值:          void                
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void *Rhhk_UdpToTcp(void *arg) 
{
    T_RhhkThreadArgs *args = (T_RhhkThreadArgs *)arg;
    char buffer[BUFFER_SIZE];
    
    while(1) 
	  {
        socklen_t addr_len = sizeof(args->u1_device_addr);
        int recv_len = recvfrom(args->udp_sock, buffer, BUFFER_SIZE, 0, 
                               (struct sockaddr*)&args->u1_device_addr, &addr_len);
        if(recv_len > 0) 
		    {
		        #if 0 //tcp 方式备用方案
            //send(args->tcp_sock, buffer, recv_len, 0);
			      #endif
            sendto(args->tcp_sock, buffer, recv_len, 0, (struct sockaddr*)&args->u2_device_addr, sizeof(args->u2_device_addr));
            Dbg_Level_Print(LOG_INFO,"Backwarded %d bytes from UDP to TCP, content:%s\n", recv_len, buffer);
        }
        usleep(1000);
    }
    
    if ((args->udp_sock >= 0) && (args->tcp_sock >= 0)) 
	  {
        close(args->udp_sock);
        close(args->tcp_sock);
		    args->udp_sock = -1;
		    args->tcp_sock = -1;
	  }
    return NULL;
}
/******************************************************************************
* 函数名称: Rhhk_TcpToUdp
* 函数功能: 平台转发至设备
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* arg              T_RhhkThreadArgs   input        sockstruct
* 返回值:          void                
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void *Rhhk_TcpToUdp(void *arg) 
{
    T_RhhkThreadArgs *args = (T_RhhkThreadArgs *)arg;
    char buffer[BUFFER_SIZE];
    
    while(1) 
	  {
        socklen_t addr_len = sizeof(args->u2_device_addr);
		    #if 0 //tcp 方式备用方案
        //int recv_len = recv(args->tcp_sock, buffer, BUFFER_SIZE, 0);
		    #endif
		    int recv_len = recvfrom(args->tcp_sock, buffer, BUFFER_SIZE, 0, 
                               (struct sockaddr*)&args->u2_device_addr, &addr_len);
        if(recv_len > 0) 
		    {
            sendto(args->udp_sock, buffer, recv_len, 0, (struct sockaddr*)&args->u1_device_addr, sizeof(args->u1_device_addr));
            Dbg_Level_Print(LOG_INFO,"Forwarded %d bytes from TCP to UDP, content:%s\n", recv_len, buffer);
        }
        usleep(1000);
    }
    
    if ((args->udp_sock >= 0) && (args->tcp_sock >= 0)) 
	  {
        close(args->udp_sock);
        close(args->tcp_sock);
		    args->udp_sock = -1;
		    args->tcp_sock = -1;
	  }
    return NULL;
}

/******************************************************************************
* 函数名称: Create_NavigationDeception
* 函数功能: 创建导航诱骗
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* 无               void               input         没有入参
* 返回值:          void                
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void Create_NavigationDeception(void)
{
	// 创建UDP套接字
    int udp_sock = socket(AF_INET, SOCK_DGRAM, 0);
    struct sockaddr_in udp_addr; 
    udp_addr.sin_family = AF_INET;
    udp_addr.sin_port = htons(UDP_PORT);
    udp_addr.sin_addr.s_addr = INADDR_ANY;
    
    bind(udp_sock, (struct sockaddr*)&udp_addr, sizeof(udp_addr));
    
    
    // 创建UDP套接字2
    int udp_sock_dev = 0;
    if ((udp_sock_dev = socket(AF_INET, SOCK_DGRAM, 0)) < 0) 
	  {
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }

    struct sockaddr_in server_addr, client_addr;    
	  socklen_t client_len = sizeof(client_addr);
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(TCP_PORT); //UDP and TCP 端口相同

    // 绑定套接字
    if (bind(udp_sock_dev, (const struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) 
	  {
        perror("bind failed");
        exit(EXIT_FAILURE);
    }
   
    Dbg_Level_Print(LOG_INFO, "UDP Server listening on TcpPort:%d, UdpPort:%d,...\n", TCP_PORT, UDP_PORT);
	
    // 准备线程参数
    T_RhhkThreadArgs args;
    args.udp_sock = udp_sock;
    args.tcp_sock = udp_sock_dev;
    args.device_addr = {0};
        

    // 创建双向转发线程
    pthread_t udp_thread, tcp_thread;
    if (pthread_create(&udp_thread, NULL, Rhhk_UdpToTcp, &args) == 0)
    {
        Dbg_Level_Print(LOG_INFO, "pthread_create: Rhhk_UdpToTcp Successful!\n");
    }
    
    if (pthread_create(&tcp_thread, NULL, Rhhk_TcpToUdp, &args) == 0)
    {
        Dbg_Level_Print(LOG_INFO, "pthread_create: Rhhk_TcpToUdp Successful!\n");
	  }

    pthread_detach(udp_thread);
    pthread_detach(tcp_thread);

    #if 0 //tcp 方式备用方案
    // 创建TCP服务器
    int tcp_sock = socket(AF_INET, SOCK_STREAM, 0);
    struct sockaddr_in tcp_addr;
    tcp_addr.sin_family = AF_INET;
    tcp_addr.sin_port = htons(TCP_PORT);
    tcp_addr.sin_addr.s_addr = INADDR_ANY;
    
    bind(tcp_sock, (struct sockaddr*)&tcp_addr, sizeof(tcp_addr));
    listen(tcp_sock, MAX_CLIENTS);

    Dbg_Level_Print(LOG_INFO,"DECEPTION GATEWAY STARTED. UDP on port %d, TCP on port %d\n", UDP_PORT, TCP_PORT);

    while(1) 
	  {
        // 等待TCP客户端连接
        struct sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);
        int client_sock = accept(tcp_sock, (struct sockaddr*)&client_addr, &client_len);
        
        Dbg_Level_Print(LOG_INFO,"NEW ACCEPT SOCKET TCP CONNECTION FROM %s:%d\n", 
               inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));

        // 准备线程参数
        T_RhhkThreadArgs args;
        args.udp_sock = udp_sock;
        args.tcp_sock = client_sock;
        args.device_addr = {0};
  

        // 创建双向转发线程
        pthread_t udp_thread, tcp_thread;
        if (pthread_create(&udp_thread, NULL, Rhhk_UdpToTcp, &args) == 0)
        {
            Dbg_Level_Print(LOG_INFO, "pthread_create: Rhhk_UdpToTcp Successful!\n");
	      }
             
        if (pthread_create(&tcp_thread, NULL, Rhhk_TcpToUdp, &args) == 0)
        {
            Dbg_Level_Print(LOG_INFO, "pthread_create: Rhhk_TcpToUdp Successful!\n");
      	}

        pthread_detach(udp_thread);
        pthread_detach(tcp_thread);
    }

    close(udp_sock);
    close(tcp_sock);
    #endif
    
    Dbg_Level_Print(LOG_INFO, "Create_NavigationDeception: MainProcess Finished!\n");
    
    return 0;
}

/******************************************************************************
* 函数名称: Rhhk_InitDebugData
* 函数功能: 判断目录是否存在，创建并赋予权限
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* 无               void               input
* 返回值:          int(-1--failed， 0--success)
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_InitDebugData(void)
{
    memset(&g_DectionDebugInfo, 0, sizeof(g_DectionDebugInfo));
}
/******************************************************************************
* 函数名称: Rhhk_WriteDebugData
* 函数功能: 判断目录是否存在，创建并赋予权限
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* 无               void               input
* 返回值:          int(-1--failed， 0--success)
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_WriteDebugData(void)
{
    int module_index = 0;
	FILE *fid_debug = NULL;
	char *ModName[7] = {"DIGI   ", "GENERAL", "RIDO4  ", "GPSSOCK ", "GPSDEV ", "OLTOKEN", "OLDECRY"};
	char *DebugMsgHead[7] = {"======================Digi    Module Debug Info======================\n",
		                     "======================Gen     Module Debug Info======================\n",
		                     "======================RidO4   Module Debug Info======================\n",
		                     "======================GpsSock Module Debug Info======================\n",
		                     "======================GpsDev  Module Debug Info======================\n",
		                     "======================OLToken Module Debug Info======================\n",
		                     "======================OLDecry Module Debug Info======================\n"};
	fid_debug = fopen("/home/<USER>/drone_detection_info.log", "a");
	if (fid_debug == NULL)
	{
        Dbg_Level_Print(LOG_ERROR, "Rhhk_WriteDebugData: Create drone_detection_info.log is failed\n");
        return -1;
	}
                
	for (module_index = 0; module_index < 7; module_index++)
	{
	    fprintf(fid_debug, DebugMsgHead[module_index]);
		  fprintf(fid_debug, "MOD:[%d]\tINIT:[%d]\tSND:[%d]\tRCV:[%d]\tRCV_TIME:[%s]\n", ModName[module_index], g_DectionDebugInfo[module_index].init_status, 
    g_DectionDebugInfo[module_index].send_req_cnt, g_DectionDebugInfo[module_index].recv_msg_cnt, g_DectionDebugInfo[module_index].recv_timeStr);
		  fprintf(fid_debug, "RCV_BUFFER:[%s]\n", g_DectionDebugInfo[module_index].latest_recv_buffer);
		  fprintf(fid_debug, "SND_O4_CNT:[%d]\n", g_DectionDebugInfo[module_index].online_o4_rpt_cnt);
		  fprintf(fid_debug, "SND_O3_CNT:[%d]\n", g_DectionDebugInfo[module_index].digi_o3_rpt_cnt);
		  fprintf(fid_debug, "SND_GE_CNT:[%d]\n", g_DectionDebugInfo[module_index].general_rpt_cnt);
	}
  fprintf(fid_debug, "======================ALL Module Debug Finished======================\n");
	fclose(fid_debug);
}

/******************************************************************************
* 函数名称: Rhhk_SetDebugData
* 函数功能: 设置状态参数
* 函数参数: 
* 参数名称: 	   类型 			  输入/输出    描述
* module_number    u8                 input        模块编号
* argment_number   u8                 input        参数编号编号
* buffer           char*              input        参数地址
* buffer_size      int                input        参数长度
* buffer_size    
* 返回值:          int(-1--failed， 0--success)
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
int Rhhk_SetDebugData(u8 module_number, u8 argment_number, u32 argment_value, char *buffer, int buffer_size)
{
    int length = 0;
    if((module_number >= 0)&&(module_number < 7))
    {
        if ((argment_number >= 0) && (argment_number < 8))
		{
            g_DectionDebugInfo[module_number].init_status = ((argment_number==0)?argment_value:g_DectionDebugInfo[module_number].init_status);
	        g_DectionDebugInfo[module_number].send_req_cnt = ((argment_number==1)?(g_DectionDebugInfo[module_number].send_req_cnt+1):g_DectionDebugInfo[module_number].send_req_cnt);
	        g_DectionDebugInfo[module_number].recv_msg_cnt = ((argment_number==2)?(g_DectionDebugInfo[module_number].recv_msg_cnt+1):g_DectionDebugInfo[module_number].recv_msg_cnt);
	        if ((argment_number==3)&&(buffer != NULL))
	        {
	            length = ((buffer_size > DJI_BUFFER_SIZE)?(DJI_BUFFER_SIZE-1):buffer_size);
				memset(g_DectionDebugInfo[module_number].latest_recv_buffer, 0, sizeof(g_DectionDebugInfo[module_number].latest_recv_buffer));
				memset(g_DectionDebugInfo[module_number].recv_timeStr, 0, sizeof(g_DectionDebugInfo[module_number].recv_timeStr));
			    strncpy(g_DectionDebugInfo[module_number].latest_recv_buffer, buffer, length);
	            strncpy(g_DectionDebugInfo[module_number].recv_timeStr, Get_Current_Time(), sizeof(g_DectionDebugInfo[module_number].recv_timeStr));
	        }
			g_DectionDebugInfo[module_number].online_o4_rpt_cnt = ((argment_number==4)?(g_DectionDebugInfo[module_number].online_o4_rpt_cnt+1):g_DectionDebugInfo[module_number].online_o4_rpt_cnt);
			g_DectionDebugInfo[module_number].digi_o3_rpt_cnt = ((argment_number==5)?(g_DectionDebugInfo[module_number].digi_o3_rpt_cnt+1):g_DectionDebugInfo[module_number].digi_o3_rpt_cnt);
			g_DectionDebugInfo[module_number].general_rpt_cnt = ((argment_number==6)?(g_DectionDebugInfo[module_number].general_rpt_cnt+1):g_DectionDebugInfo[module_number].general_rpt_cnt);
		}
    }
	
	return 0;
}


