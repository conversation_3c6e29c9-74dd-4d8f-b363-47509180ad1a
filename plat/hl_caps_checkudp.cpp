/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* �ļ�����:  hl_caps_checkudp.c
* ��������:  ���Գ���
* ��    ��:
* ��д����:  2024/08/29
* ˵    ��:
* �޸���ʷ:
* �޸�����    �޸���  BugID/CRID      �޸�����
* ------------------------------------------------------------------------------
* 2024/08/29  zonghui    �����ļ�
*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "./hl_caps_report.h"

int main(int argc, char **argv)
{
    int sockfd;
    char buffer[5000];
    char *message = NULL;
    int ServerPort = 9085;
    const char *ServerIP = "***********";
    socklen_t SinSize = sizeof(struct sockaddr_in);
    int RcvLen = 0;
    struct sockaddr_in server_addr;
    
    #ifdef UDP_MODE
    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0)
    {
        perror("Creat udp Client Failed!\n");
	exit(1);
    }

    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(ServerPort);
    server_addr.sin_addr.s_addr = inet_addr(ServerIP);
    memset(server_addr.sin_zero, 0, sizeof(server_addr.sin_zero));

    while(1)
    {
      RcvLen = recvfrom(sockfd, buffer, sizeof(buffer), 0, (struct sockaddr *)&server_addr, &SinSize);
      if (RcvLen < 0)
      {
          perror("udp have no data from server!");
          continue;
      }

      buffer[RcvLen] = 0;
      Dbg_Level_Print(LOG_INFO, "udp client Received Json Buffer:\n %s\n", buffer);
      usleep(20);
    }	    
    #else
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0)
    {
        perror("Creat tcp Client Failed!\n");
	exit(1);
    }

    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(ServerPort);
    server_addr.sin_addr.s_addr = inet_addr(ServerIP);
    memset(server_addr.sin_zero, 0, sizeof(server_addr.sin_zero));
    
    if(inet_pton(AF_INET, ServerIP, &server_addr.sin_addr) <= 0)
    {
        perror("tcp inet_pton failed!");
	exit(1);
    }

    if(connect(sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0)
    {
        perror("tcp connect failed!");
	exit(1);
    }

    while(1)
    {
      RcvLen = read(sockfd, buffer, sizeof(buffer));
      if (RcvLen < 0)
      {
          perror("tcp have no data from server!");
          continue;
      }

      buffer[RcvLen] = 0;
      Dbg_Level_Print(LOG_INFO, "tcp Received Json Buffer:%s\n", buffer);
      usleep(20);
    }
    
    #endif

    close(sockfd);
    return 0;
}
