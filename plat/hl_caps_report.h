/*******************************************************************************
* COPYRIGHT Beijing RHHK Equipment CO.,LTD
********************************************************************************
* �ļ�����:  hl_caps_report.h
* ��������:  CAPS�ڲ����ݽṹ���塢������������
* ��    ��:
* ��д����:  2021/07/26
* ˵    ��:
* �޸���ʷ:
* �޸�����    �޸���  BugID/CRID      �޸�����
* ------------------------------------------------------------------------------
* 2024/10/29  zonghui    �����ļ�
*******************************************************************************/
/******************************** ͷ�ļ�������ͷ ******************************/
#ifndef HL_CAPS_REPORT_H
#define HL_CAPS_REPORT_H

/******************************** �����ļ����� ********************************/ 
#include "hl_caps_adapt.h"
#include <stdarg.h>
#include <stdio.h>
#include "DroneDetect.h"
#ifndef WIN32
#include <unistd.h>
#else
#include <windows.h>
#endif
/******************************** ��ͳ������� ********************************/ 
#ifndef WIN32
#define  GNB_SELFTEST_PROCESS_FILE   "/data/App/SH_Detection/drone_rx_demo/log"
#else
#define  GNB_SELFTEST_PROCESS_FILE   "../log" 
#endif

#define  GNB_SELFTEST_CONFIG_BUFSIZE         2048
#define  GNB_SELFTEST_DEBUG_INFO                 1

#define   CAPS_CREATE_PROCESS_CONFIG_SWITCH_ON      1
#define   CAPS_CREATE_PROCESS_CONFIG_SWITCH_OFF    0

#define   CAPS_CREATE_ARG_CONFIG_SWITCH_ON      1
#define   CAPS_CREATE_ARG_ARG_CONFIG_SWITCH_OFF    0

#define   CAPS_ALL_SCENE_FUNC_MAX_COUNT      10

#define  CAPS_TESTCASE_SEND_FINISHED          1
#define  CAPS_TESTCASE_SEND_NOFINISHED   0

#define  CAPS_TESTCASE_PRINT_BUF_LEN       10240    

#define  CAPS_SINGLE_CASE   0
#define  CAPS_MULTI_CASE     1

#define  CAPS_SCENE_SA     0
#define  CAPS_SCENE_NSA  1

#define  CAPS_COUNTER_ENABLE    1
#define  CAPS_COUNTER_DISABLE   0

#define  CAPS_PLMNID_LEN              6

#define  CAPS_SRC_GNB_FINISH    1
#define  CAPS_TGT_GNB_FINISH    2

#define   CAPS_INTRA_REPORT_WAIT_TIMES   100

//#define UDP_MODE         1 
#define OSP_OK           0
#define OSP_ERROR        -1
#define HL_TRUE          1
#define HL_FALSE         0
#define NULLPTR          NULL
#define CAPS_SCENE_NAME_BYTE_COUNT 1
#define CAPS_TEST_CASE_SCENE_COUNT 5
#define FRAME_HEAD_LEN     2
#define FRAME_TAIL_LEN     2
#define DEFENSE_ARGS_LEN   4
#define OUTPUT_RESERVE_LEN 137
#define INPUT_RESERVE_LEN  179
#define SWITCH_OPEN         1
#define SWITCH_CLOSE        2
#define DRONE_ARG_STRING_LEN  100
#define DRONE_GENERAL_TYPE_NUM  100 
#define MAX_UPREPORT_BUFFER_COUNT   200
#define MAX_UPREPORT_TYPE 5

/*****************************  Private Typedef *******************************/
typedef  unsigned int   u32;
typedef  signed int     s32;
typedef  unsigned short u16;
typedef  signed short   s16;
typedef  unsigned char  u8;
typedef  signed char    s8;

/******************************** �������� ************************************/
#pragma pack(1)
typedef DJI_FLIGHT_INFO_Str T_DroneDetectInfoType; 
#pragma pack(0)

typedef struct __ManagerConf
{
    char  tcp_ip[16];
	  char  device_code[32];
    int   tcp_port;
	  int   debug_switch;
	  double lon;
	  double lat;
	  double alt;
    char  cpu_code[16];
	  char  brd_code[16];
	  char  prd_code[16];
    char  ol_ip[16];
	  int   ol_port;
	  char  username[16];
	  char  password[16]; 
    int pound_count; 
    int pound_dji_count;
    int usdelay;
}T_RhhkMangerConfArg;
/******************************** ���������� ************************************/
static u32 g_caps_default_print_level = 7;

/******************************** ȫ�ֱ������� ********************************/
/*declear global var for gnb selftest*/
//extern GNB_SELFTEST_Case_DataStruct g_gNB_SelfTest_Datastruct[GNB_SELFTEST_CASE_MAX_COUNT];

/******************************** �ⲿ����ԭ������ ****************************/
//extern s32 CAPS_TestCaseTrigger(CAPS_TimerPara *pstruTimerPara);
extern char * DelString(char *cJsonSrcStr, char *dstStr, char *cJsonDstStr);
extern void  Dbg_Level_Print(u32 print_level, char *format, ...);
extern s32 CAPS_SetApi(DtStruct_ApiSet *pApiSet);
extern s32 CAPS_JudgeFileExist(const char* pfilename);
extern int TransformDroneData(u32 g_u32IfRecordSwitch, DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt);
extern int CPAS_Start_Socket(char *ip_remote, char *ip_local,  char *outdata, u32 u32DataLength);
extern void SetDroneDectectData(DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt);
extern int RHHK_GetConfigStructArg(T_RhhkMangerConfArg *OutangerConfArg);
extern int Judge_WhetherGeneralDrone(DJI_FLIGHT_INFO_Str *pstruDJIFlightInfo);
extern char *CreateGeneralDroneSN(char *model, float freq, float rssi);
extern char *QueryGeneralDroneType(DJI_FLIGHT_INFO_Str *message);
extern void DebugDroneDectectData(DJI_FLIGHT_INFO_Str *message);
/******************************** ͷ�ļ�������β ******************************/
#endif 
/******************************** ͷ�ļ����� **********************************/
