/*******************************************************************************
* COPYRIGHT RHHK  CO.,LTD
********************************************************************************
* �ļ�����: hl_caps_rdi.cpp
* ��������: RHHK RIDģ�����ݽ���
* �������:
* ��	��: zonghui
* ����ʱ��: 2024/12/20
* �޸�����	  �޸���  BugID/CRID	  �޸�����
* ------------------------------------------------------------------------------
* 2024/12/25  zonghui	 �����ļ�
* ------------------------------------------------------------------------------
*******************************************************************************/

/******************************** Í·ÎÄ¼þ±£»¤¿ªÍ· ******************************/
#ifndef HL_CAPS_RDI_H
#define HL_CAPS_RDI_H

/******************************** °üº¬ÎÄ¼þÉùÃ÷ ********************************/
#include <stdarg.h>
#include <stdio.h>
#ifndef WIN32
#include <unistd.h>
#else
#include <windows.h>
#endif
#include <stdint.h>
#include "../interface.h"
/******************************** ºêºÍ³£Á¿¶¨Òå ********************************/
#define SWITCH_CLOSE        2
#define DRONE_ARG_STRING_LEN  100
#define RDI_RCV_DATA_LENGTH  3000
#define DJI_O4_RDI_COUNT     40
#define DJI_O4_RDI_LENGTH    100
#define DJI_O4_RDI_MAX_COUNT  20

/******************************** ¶¨ÒåÀàÐÍ ************************************/
typedef  unsigned int   u32;
typedef  signed int     s32;
typedef  unsigned short u16;
typedef  signed short   s16;
typedef  unsigned char  u8;
typedef  signed char    s8;

typedef struct __ReadRdiInfo
{
    char rdi_packet_valid;	
    char rdi_ssid[32];//²úÆ·ÐÍºÅ
    uint8_t drone_serial_num[30]; //ÎÞÈË»úÎ¨Ò»ÐòÁÐºÅ
    char product_type_str[32];//²úÆ·ÐÍºÅ
    uint8_t UA_type; //[0]Not Declared [1]Aeroplane,[2]Helicopter,[3]Gyroplane,[4]Hybrid Lift,[5]Ornithopter,[6]Glider[7]Kite [8]Free Balloon
    double drone_longtitude;//·É»ú¾­¶È
    double drone_latitude;//·É»úÎ³¶È
    double pilot_longtitude;  //·ÉÊÖ¾­¶È
    double pilot_latitude;  //·ÉÊÖÎ³¶È
    double drone_Hspeed;
    double drone_Vspeed;
    int16_t direction;
    float altitude;//º£°Î
}T_RHHKReadRdiO4Info;

/******************************** º¯ÊýºêÀàÐÍ ************************************/

/******************************** È«¾Ö±äÁ¿ÉùÃ÷ ********************************/

/******************************** Íâ²¿º¯ÊýÔ­ÐÍÉùÃ÷ ****************************/
extern void CreatRdiPthread(void);
extern int Judge_WhetherDroneIsO4(DJI_FLIGHT_INFO_Str *pstruDJIFlightInfo);
extern int QueryO4DroneByModel(char *product_type_str);
/******************************** Í·ÎÄ¼þ±£»¤½áÎ² ******************************/
#endif
/******************************** Í·ÎÄ¼þ½áÊø **********************************/

