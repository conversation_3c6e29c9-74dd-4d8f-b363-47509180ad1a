/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* 文件名称:  DroneDetect.cpp
* 功能描述:  无人机适配层包类型区分和解析
* 版    本:
* 编写日期:  2024/10/29
* 说    明:
* 修改历史:
* 修改日期    修改人  BugID/CRID      修改内容
* ------------------------------------------------------------------------------
* 2024/10/29  zonghui    创建文件
*******************************************************************************/

#include "DroneDetect.h"
#include "hl_caps_report.h"

uint32_t g_LogSwithStatus = 1;
extern double local_longitude, local_latitude;
extern u32 g_u32IfRecordSwitch;
T_DroneInfo g_tDroneInfo;

/*******************************************************************************
* 函数名称: SetDroneDectectData
* 函数功能: 根据协议包类型区分无人机类型
* 函数参数:
* 参数名称:         类型                     输入/输出     描述
* message          DJI_FLIGHT_INFO_Str       输入          无人机数据包
* pstruGpsInfoRpt  Drone_Detect_GpsInfo_Rpt  输入          GPS信息
* 返回值: 无
* 函数说明:
*
* 修改日期    版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void SetDroneDectectData(DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt) {
    time_t timep;
    struct tm *currentTime;
    currentTime = localtime(&timep);

    if (NULL == message) {
        return;
    }

    TransformDroneData(SWITCH_OPEN, (DJI_FLIGHT_INFO_Str *) message, (Drone_Detect_GpsInfo_Rpt *) pstruGpsInfoRpt);
    DebugDroneDectectData((DJI_FLIGHT_INFO_Str *) message);

    if (message->decode_flag) {
        if (message->packet_type == PACKET_DJI_ENCYPT) {
            T_DecodeDjiEncypt *ptDroneInfo = NULL;
            ptDroneInfo = (T_DecodeDjiEncypt *) &g_tDroneInfo;
            uint64_t id = 0;
            for (int i = 0; i < 6; i++) {
                id <<= 8;
                id += message->drone_serial_num[i];
            }
            ptDroneInfo->decode_flag = message->decode_flag;
            ptDroneInfo->packet_type = message->packet_type;
            ptDroneInfo->id = id;
            ptDroneInfo->freq = message->freq / 1e6;
            ptDroneInfo->rssi = message->rssi;
            if (g_LogSwithStatus) {
                FILE *fid0 = fopen("../log/scan_result0.log", "a");
                fprintf(fid0, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                        currentTime->tm_mday,
                        currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                fprintf(fid0, "DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                fprintf(fid0, "PacketType = %d\n", ptDroneInfo->packet_type);
                fprintf(fid0, "DroneID=%ld\n", ptDroneInfo->id);
                fprintf(fid0, "DroneFreq=%.1f\n", ptDroneInfo->freq);
                fprintf(fid0, "DroneRssi=%.1f\n\n", ptDroneInfo->rssi);
                fclose(fid0);
            }
        } else {
            T_DecodeDjiNoneEncypt *ptDroneInfo = NULL;
            ptDroneInfo = (T_DecodeDjiNoneEncypt *) &g_tDroneInfo;
            ptDroneInfo->decode_flag = message->decode_flag;
            ptDroneInfo->packet_type = message->packet_type;
            ptDroneInfo->drone_longitude = message->drone_longitude;
            ptDroneInfo->drone_latitude = message->drone_latitude;
            ptDroneInfo->height = message->height;
            ptDroneInfo->altitude = message->altitude;
            ptDroneInfo->east_speed = message->east_speed;
            ptDroneInfo->north_speed = message->north_speed;
            ptDroneInfo->up_speed = message->up_speed;
            ptDroneInfo->home_longitude = message->home_longitude;
            ptDroneInfo->home_latitude = message->home_latitude;
            ptDroneInfo->pilot_longitude = message->pilot_longitude;
            ptDroneInfo->pilot_latitude = message->pilot_latitude;
            memcpy(ptDroneInfo->drone_serial_num, message->drone_serial_num, sizeof(ptDroneInfo->drone_serial_num));
            ptDroneInfo->product_type = message->product_type;
            memcpy(ptDroneInfo->product_type_str, message->product_type_str, sizeof(ptDroneInfo->product_type_str));
            ptDroneInfo->freq = message->freq / 1e6;
            ptDroneInfo->rssi = (int) message->rssi;
            ptDroneInfo->freq = message->freq / 1e6;
            ptDroneInfo->efingerprint = message->efingerprint;
            ptDroneInfo->bandwidth = (int) (message->bandwidth / 1e6);

            GpsData gps;
            float distance = 0;
            if (message->drone_longitude != 0) {
                if (gps.dLongitude != 0) local_longitude = gps.dLongitude;
                if (gps.dLongitude != 0) local_latitude = gps.dLatitude;
                distance = gps_distance(local_longitude, local_latitude, message->drone_longitude,
                                        message->drone_latitude);
            }
            ptDroneInfo->distance = distance;

            if (g_LogSwithStatus) {
                FILE *fid1 = fopen("../log/scan_result1.log", "a");
                fprintf(fid1, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                        currentTime->tm_mday,
                        currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                fprintf(fid1, "DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                fprintf(fid1, "PacketType = %d\n", ptDroneInfo->packet_type);
                fprintf(fid1, "DroneGPS(longitude|latitude):%f/%f \n", ptDroneInfo->drone_longitude,
                        ptDroneInfo->drone_latitude);
                fprintf(fid1, "DroneHeight|Altitude %.1f|%.1f\n", ptDroneInfo->height, ptDroneInfo->altitude);
                fprintf(fid1, "DroneEast|Noth|Up Speed %.1f|%.1f|%.1f\n", ptDroneInfo->east_speed,
                        ptDroneInfo->north_speed, ptDroneInfo->up_speed);
                fprintf(fid1, "Home(longitude|latitude)=%.1f/%.1f\n", ptDroneInfo->home_longitude,
                        ptDroneInfo->home_latitude);
                fprintf(fid1, "Pilot(longitude|latitude)=%.1f/%.1f\n", ptDroneInfo->pilot_longitude,
                        ptDroneInfo->pilot_latitude);
                fprintf(fid1, "DroneSerial: %s\n", ptDroneInfo->drone_serial_num);
                fprintf(fid1, "DroneModelType(%d)(%s)\n", ptDroneInfo->product_type, ptDroneInfo->product_type_str);
                fprintf(fid1, "DroneFreq=%.1f\n", ptDroneInfo->freq);
                fprintf(fid1, "DroneRssi=%.1f\n", ptDroneInfo->rssi);
                fprintf(fid1, "DroneEfingerprint=%ld\n", ptDroneInfo->efingerprint);
                fprintf(fid1, "DroneBW=%f\n", ptDroneInfo->bandwidth);
                fprintf(fid1, "DroneDistance=%.2f\n\n", ptDroneInfo->distance);
                fclose(fid1);
            }
        }
    } else if (message->packet_type == PACKET_OPEN_DRONE_ID) {
        T_PacketOpenDroneID *ptDroneInfo = NULL;
        ptDroneInfo = (T_PacketOpenDroneID *) &g_tDroneInfo;
        ptDroneInfo->decode_flag = message->decode_flag;
        ptDroneInfo->packet_type = message->packet_type;
        memcpy(ptDroneInfo->drone_id, message->drone_id, sizeof(ptDroneInfo->drone_id));
        ptDroneInfo->freq = (int) (message->freq / 1e6);
        ptDroneInfo->rssi = (int) (message->rssi);
        ptDroneInfo->drone_longitude = message->drone_longitude;
        ptDroneInfo->drone_latitude = message->drone_latitude;
        ptDroneInfo->pilot_longitude = message->pilot_longitude;
        ptDroneInfo->pilot_latitude = message->pilot_latitude;
        ptDroneInfo->speed = message->speed;
        ptDroneInfo->up_speed = message->up_speed;
        ptDroneInfo->direction = message->direction;
        memcpy(ptDroneInfo->wifi_ssid, message->wifi_ssid, sizeof(ptDroneInfo->wifi_ssid));
        ptDroneInfo->wifi_source_address[0] = message->wifi_source_address[0];
        ptDroneInfo->wifi_source_address[1] = message->wifi_source_address[1];
        ptDroneInfo->wifi_source_address[2] = message->wifi_source_address[2];
        ptDroneInfo->wifi_source_address[3] = message->wifi_source_address[3];
        ptDroneInfo->wifi_source_address[4] = message->wifi_source_address[4];
        ptDroneInfo->wifi_source_address[5] = message->wifi_source_address[5];

        if (g_LogSwithStatus) {
            FILE *fid2 = fopen("../log/scan_result2.log", "a");
            fprintf(fid2, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                    currentTime->tm_mday,
                    currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
            fprintf(fid2, "DecodeFlag = %d\n", ptDroneInfo->decode_flag);
            fprintf(fid2, "PacketType = %d\n", ptDroneInfo->packet_type);
            fprintf(fid2, "PacketOpenDroneID = %p\n", (void *) ptDroneInfo->drone_id);
            fprintf(fid2, "PacketOpenDroneFreq = %.1f\n", ptDroneInfo->freq);
            fprintf(fid2, "PacketOpenDroneRssi = %.1f\n", ptDroneInfo->rssi);
            fprintf(fid2, "PacketOpenDrone(longitude|latitude) = %f/%f\n", ptDroneInfo->drone_longitude,
                    ptDroneInfo->drone_latitude);
            fprintf(fid2, "PackOpenPilot(longitude|latitude)=%.1f/%.1f\n", ptDroneInfo->pilot_longitude,
                    ptDroneInfo->pilot_latitude);
            fprintf(fid2, "PacketOpen Speed|Up Speed %.1f|%.1f\n", ptDroneInfo->speed, ptDroneInfo->up_speed);
            fprintf(fid2, "PacketOpenDirect = %d\n", ptDroneInfo->direction);
            fprintf(fid2, "PacketOpenSsid =%s\n", ptDroneInfo->wifi_ssid);
            fprintf(fid2, "PacketOpenMAC=%02x:%02x:%02x:%02x:%02x:%02x\n\n", ptDroneInfo->wifi_source_address[0],
                    ptDroneInfo->wifi_source_address[1],
                    ptDroneInfo->wifi_source_address[2], ptDroneInfo->wifi_source_address[3],
                    ptDroneInfo->wifi_source_address[4],
                    ptDroneInfo->wifi_source_address[5]);
            fclose(fid2);
        }
    } else {
        switch (message->packet_type) {
            case PACKET_VEDIO_DJI: {
                T_Packet_DJIDroneID *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_DJIDroneID *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = (int) (message->freq / 1e6);
                ptDroneInfo->rssi = (int) (message->rssi);

                if (g_LogSwithStatus) {
                    FILE *fid3 = fopen("../log/scan_result3.log", "a");
                    fprintf(fid3, "%d-%d-%d %d:%d:%d\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid3, "PacketVedioDjiDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid3, "PacketVedioDjiPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid3, "PacketVedioDjiFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid3, "PacketVedioDjiRssi=%.1f\n", ptDroneInfo->rssi);
                    fclose(fid3);
                }
                break;
            }
            case PACKET_VEDIO_DAOTONG:
            case PACKET_VEDIO_Yuneec_H52e: {
                T_Packet_VedioDaotongOrYuneecH52e *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioDaotongOrYuneecH52e *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                ptDroneInfo->efingerprint = message->efingerprint;

                if (g_LogSwithStatus) {
                    FILE *fid4 = fopen("../log/scan_result4.log", "a");
                    fprintf(fid4, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid4, "PacketVedioDaotongDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid4, "PacketVedioDaotongPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid4, "PacketVedioDaotongFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid4, "PacketVedioDaotongRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid4, "PacketVedioDaotongBandwidth=%.1f\n", ptDroneInfo->bandwidth);
                    fprintf(fid4, "PacketVedioDaotongEfingerprint=%ld\n\n", ptDroneInfo->efingerprint);
                    fclose(fid4);
                }

                break;
            }
            case PACKET_VEDIO_LightBrdige_DJI: {
                T_Packet_VedioLightBrdigeDji *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioLightBrdigeDji *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;

                if (g_LogSwithStatus) {
                    FILE *fid5 = fopen("../log/scan_result5.log", "a");
                    fprintf(fid5, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid5, "PacketVedioLightBrdigeDjiDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid5, "PacketVedioLightBrdigeDjiPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid5, "PacketVedioLightBrdigeDjiFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid5, "PacketVedioLightBrdigeDjiRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid5, "PacketVedioLightBrdigeDjiBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid5);
                }

                break;
            }
            case PACKET_VEDIO_LightBrdige_OTHERS: {
                T_Packet_VedioLightBrdigeOthers *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioLightBrdigeOthers *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;

                if (g_LogSwithStatus) {
                    FILE *fid6 = fopen("../log/scan_result6.log", "a");
                    fprintf(fid6, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid6, "PacketVedioLightBrdigeOthDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid6, "PacketVedioLightBrdigeOthPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid6, "PacketVedioLightBrdigeOthFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid6, "PacketVedioLightBrdigeOthRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid6, "PacketVedioLightBrdigeOthBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid6);
                }
                break;
            }
            case PACKET_VEDIO_WIFI_DJI_5M: {
                T_Packet_VedioWifiDji5M *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioWifiDji5M *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;

                if (g_LogSwithStatus) {
                    FILE *fid7 = fopen("../log/scan_result7.log", "a");
                    fprintf(fid7, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid7, "PacketPacketVedioWifiDji5MDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid7, "PacketVedioWifiDji5MPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid7, "PacketVedioWifiDji5MFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid7, "PacketVedioWifiDji5MRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid7, "PacketVedioWifiDji5MBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid7);
                }

                break;
            }
            case PACKET_VEDIO_WIFI_DJI: {
                T_Packet_VedioWifiDji *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioWifiDji *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;

                if (g_LogSwithStatus) {
                    FILE *fid8 = fopen("../log/scan_result8.log", "a");
                    fprintf(fid8, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid8, "PacketVedioWifiDji5MDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid8, "PacketVedioWifiDji5MPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid8, "PacketVedioWifiDji5MFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid8, "PacketVedioWifiDji5MRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid8, "PacketVedioWifiDji5MBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid8);
                }
                break;
            }
            case PACKET_VEDIO_HARBOSEN: {
                T_Packet_VedioHarbosen *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioHarbosen *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;

                if (g_LogSwithStatus) {
                    FILE *fid9 = fopen("../log/scan_result9.log", "a");
                    fprintf(fid9, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid9, "PacketVedioHarbosenDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid9, "PacketVedioHarbosenPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid9, "PacketVedioHarbosenFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid9, "PacketVedioHarbosenRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid9, "PacketVedioHarbosenBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid9);
                }

                break;
            }
            case PACKET_VEDIO_ANALOG: {
                if (message->bandwidth == 5e6) //type:5e6
                {
                    T_Packet_VedioAnalogAndBw5e6 *ptDroneInfo = NULL;
                    ptDroneInfo = (T_Packet_VedioAnalogAndBw5e6 *) &g_tDroneInfo;
                    ptDroneInfo->decode_flag = message->decode_flag;
                    ptDroneInfo->packet_type = message->packet_type;
                    ptDroneInfo->freq = message->freq / 1e6;
                    ptDroneInfo->rssi = message->rssi;
                    ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;


                    if (g_LogSwithStatus) {
                        FILE *fid10 = fopen("../log/scan_result10.log", "a");
                        fprintf(fid10, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                                currentTime->tm_mday,
                                currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                        fprintf(fid10, "PacketVedioAnalog5e6DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                        fprintf(fid10, "PacketVedioAnalog5e6PacketType = %d\n", ptDroneInfo->packet_type);
                        fprintf(fid10, "PacketVedioAnalog5e6Freq=%.1f\n", ptDroneInfo->freq);
                        fprintf(fid10, "PacketVedioAnalog5e6Rssi=%.1f\n", ptDroneInfo->rssi);
                        fprintf(fid10, "PacketVedioAnalog5e6Bandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                        fclose(fid10);
                    }
                }


                if (message->bandwidth == 4.2e6) //type: 4.2e6
                {
                    T_Packet_VedioAnalogAndBw5e6 *ptDroneInfo = NULL;
                    ptDroneInfo = (T_Packet_VedioAnalogAndBw5e6 *) &g_tDroneInfo;
                    ptDroneInfo->decode_flag = message->decode_flag;
                    ptDroneInfo->packet_type = message->packet_type;
                    ptDroneInfo->freq = message->freq / 1e6;
                    ptDroneInfo->rssi = message->rssi;
                    ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;

                    if (g_LogSwithStatus) {
                        FILE *fid11 = fopen("../log/scan_result11.log", "a");
                        fprintf(fid11, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                                currentTime->tm_mday,
                                currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                        fprintf(fid11, "PacketVedioAnalog4.2e6DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                        fprintf(fid11, "PacketVedioAnalog4.2e6PacketType = %d\n", ptDroneInfo->packet_type);
                        fprintf(fid11, "PacketVedioAnalog4.2e6Freq=%.1f\n", ptDroneInfo->freq);
                        fprintf(fid11, "PacketVedioAnalog4.2e6Rssi=%.1f\n", ptDroneInfo->rssi);
                        fprintf(fid11, "PacketVedioAnalog4.2e6Bandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                        fclose(fid11);
                    }
                }

                break;
            }
            case PACKET_VEDIO_WIFI_PARROT: {
                T_Packet_VedioWifiParrot *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioWifiParrot *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid12 = fopen("../log/scan_result12.log", "a");
                    fprintf(fid12, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid12, "PacketVedioWifiParrotDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid12, "PacketVedioWifiParrotPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid12, "PacketVedioWifiParrotFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid12, "PacketVedioWifiParrotRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid12, "PacketVedioWifiParrotBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid12);
                }

                break;
            }
            case PACKET_VEDIO_WIFI_POWEREGG: {
                T_Packet_VedioWifiPowerEgg *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioWifiPowerEgg *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid13 = fopen("../log/scan_result13.log", "a");
                    fprintf(fid13, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid13, "PacketVedioWifiPowerEggDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid13, "PacketVedioWifiPowerEggPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid13, "PacketVedioWifiPowerEggFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid13, "PacketVedioWifiPowerEggRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid13, "PacketVedioWifiPowerEggBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid13);
                }

                break;
            }
            case PACKET_VEDIO_WIFI_SJF11: {
                T_Packet_VedioWifiSjf11 *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioWifiSjf11 *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid14 = fopen("../log/scan_result14.log", "a");
                    fprintf(fid14, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid14, "PacketVedioWifiSJF11DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid14, "PacketVedioWifiSJF11PacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid14, "PacketVedioWifiSJF11Freq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid14, "PacketVedioWifiSJF11Rssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid14, "PacketVedioWifiSJF11Bandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid14);
                }

                break;
            }
            case PACKET_VEDIO_ENHANCED_WIFI: {
                T_Packet_VedioEnhancedWifi *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioEnhancedWifi *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid15 = fopen("../log/scan_result15.log", "a");
                    fprintf(fid15, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid15, "PacketVedioEnhancedWifyDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid15, "PacketVedioEnhancedWifyPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid15, "PacketVedioEnhancedWifyFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid15, "PacketVedioEnhancedWifyRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid15, "PacketVedioEnhancedWifyBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid15);
                }

                break;
            }
            case PACKET_VEDIO_Avatar: {
                T_Packet_VedioAvatar *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioAvatar *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid16 = fopen("../log/scan_result16.log", "a");
                    fprintf(fid16, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid16, "PacketVedioAvatarDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid16, "PacketVedioAvatarPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid16, "PacketVedioAvatarFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid16, "PacketVedioAvatarRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid16, "PacketVedioAvatarBandwidth=%.1f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid16);
                }

                break;
            }
            case PACKET_VEDIO_ZONGHENG: {
                T_Packet_VedioZongheng *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioZongheng *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid17 = fopen("../log/scan_result17.log", "a");
                    fprintf(fid17, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid17, "PacketVedioZonghengDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid17, "PacketVedioZonghengPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid17, "PacketVedioZonghengFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid17, "PacketVedioZonghengRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid17, "PacketVedioZonghengBandwidth=%f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid17);
                }

                break;
            }
            case PACKET_DJI_M200: {
                T_Packet_DjiM200 *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_DjiM200 *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;

                if (g_LogSwithStatus) {
                    FILE *fid18 = fopen("../log/scan_result18.log", "a");
                    fprintf(fid18, "%d-%d-%d %d:%d:%d\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid18, "PacketDjiM200DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid18, "PacketDjiM200PacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid18, "PacketDjiM200Freq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid18, "PacketDjiM200Rssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid18, "PacketDjiM200Bandwidth=%f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid18);
                }

                break;
            }
            case PACKET_HAIKAN_WEISHI: {
                T_Packet_HaikanWeishi *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_HaikanWeishi *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid19 = fopen("../log/scan_result19.log", "a");
                    fprintf(fid19, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid19, "PacketHaikanWeishiDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid19, "PacketHaikanWeishiPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid19, "PacketHaikanWeishiFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid19, "PacketHaikanWeishiRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid19, "PacketHaikanWeishiBandwidth=%f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid19);
                }

                break;
            }
            case PACKET_VEDIO_DJI_O4: {
                T_Packet_VedioDji04 *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioDji04 *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid20 = fopen("../log/scan_result9.log", "a");
                    fprintf(fid20, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid20, "PacketVedioDjiO4DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid20, "PacketVedioDjiO4PacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid20, "PacketVedioDjiO4Freq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid20, "PacketVedioDjiO4Rssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid20, "PacketVedioDjiO4Bandwidth=%f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid20);
                }

                break;
            }
            case PACKET_VEDIO_LTE_TYPE: {
                T_Packet_VedioLteType *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioLteType *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid21 = fopen("../log/scan_result21.log", "a");
                    fprintf(fid21, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid21, "PacketVedioLteTypeDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid21, "PacketVedioLteTypePacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid21, "PacketVedioLteTypeFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid21, "PacketVedioLteTypeRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid21, "PacketVedioLteTypeBandwidth=%f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid21);
                }

                break;
            }
            case PACKET_VEDIO_LightBridge: {
                T_Packet_VedioLightBrdigeDji *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioLightBrdigeDji *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                ptDroneInfo->bandwidth = (int) message->bandwidth / 1e6;
                if (g_LogSwithStatus) {
                    FILE *fid22 = fopen("../log/scan_result22.log", "a");
                    fprintf(fid22, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid22, "PacketVedioLightBridgeDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid22, "PacketVedioLightBridgePacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid22, "PacketVedioLightBridgeFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid22, "PacketVedioLightBridgeRssi=%.1f\n", ptDroneInfo->rssi);
                    fprintf(fid22, "PacketVedioLightBridgeBandwidth=%f\n\n", ptDroneInfo->bandwidth);
                    fclose(fid22);
                }

                break;
            }
            case PACKET_DATALINK_P900: {
                T_Packet_DatalinkP900 *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_DatalinkP900 *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                if (g_LogSwithStatus) {
                    FILE *fid23 = fopen("../log/scan_result23.log", "a");
                    fprintf(fid23, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid23, "PacketDatalinkP900DecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid23, "VPacketDatalinkP900PacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid23, "PacketDatalinkP900Freq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid23, "PacketDatalinkP900Rssi=%.1f\n\n", ptDroneInfo->rssi);
                    fclose(fid23);
                }

                break;
            }
            case PACKET_VEDIO_FEIMI: {
                T_Packet_VedioFeimi *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_VedioFeimi *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                if (g_LogSwithStatus) {
                    FILE *fid24 = fopen("../log/scan_result24.log", "a");
                    fprintf(fid24, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid24, "PacketVedioFeimiDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid24, "PacketVedioFeimiPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid24, "PacketVedioFeimiFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid24, "PacketVedioFeimiRssi=%.1f\n\n", ptDroneInfo->rssi);
                    fclose(fid24);
                }

                break;
            }
            case PACKET_DJI_ID: {
                T_Packet_DJIDroneID *ptDroneInfo = NULL;
                ptDroneInfo = (T_Packet_DJIDroneID *) &g_tDroneInfo;
                ptDroneInfo->decode_flag = message->decode_flag;
                ptDroneInfo->packet_type = message->packet_type;
                ptDroneInfo->freq = message->freq / 1e6;
                ptDroneInfo->rssi = message->rssi;
                if (g_LogSwithStatus) {
                    FILE *fid25 = fopen("../log/scan_result25 .log", "a");
                    fprintf(fid25, "[%d-%d-%d %d:%d:%d]\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1,
                            currentTime->tm_mday,
                            currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec);
                    fprintf(fid25, "PacketDjiIDDecodeFlag = %d\n", ptDroneInfo->decode_flag);
                    fprintf(fid25, "PacketDjiIDPacketType = %d\n", ptDroneInfo->packet_type);
                    fprintf(fid25, "PacketDjiIDFreq=%.1f\n", ptDroneInfo->freq);
                    fprintf(fid25, "PacketDjiIDRssi=%.1f\n\n", ptDroneInfo->rssi);
                    fclose(fid25);
                }

                break;
            }
            default:
                break;
        }
    }
    return;
}
