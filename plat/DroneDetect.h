/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* �ļ�����:  DroneDetect.h
* ��������:  ����ģ����֧�ֵ����˻������ͽṹ
* ��    ��:
* ��д����:  2024/10/29
* ˵    ��:
* �޸���ʷ:
* �޸�����    �޸���  BugID/CRID      �޸�����
* ------------------------------------------------------------------------------
* 2024/10/29  zonghui    �����ļ�
*******************************************************************************/
#ifndef _DRONEDETECT_H_
#define _DRONEDETECT_H_
#include "stdio.h"
#include "stdint.h"
#include "stdlib.h"
#include "../interface.h"
#include "math.h"
#include "string.h"
#include "time.h"

typedef struct _tag_DecodeDjiEncypt
{
    char decode_flag;//是否CRC校验通过
    uint16_t packet_type; //包类型
    uint64_t  id;
    int64_t freq;//频率
    float bandwidth;
    float rssi; //信号电平
}T_DecodeDjiEncypt;

typedef struct _tag_DecodeDjiNoneEncypt
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     double drone_longitude;//飞机经度
     double drone_latitude;//飞机纬度
     float altitude;//海拔
     float height; //高度
     float north_speed;//向北速度
     float east_speed;//向东速度
     float up_speed; //向上速度
     double pilot_longitude;  //飞手经度
     double pilot_latitude;  //飞手纬度
     double home_longitude;//返航经度 
     double home_latitude;//返航纬度
     uint8_t drone_serial_num[17]; //无人机唯一序列号
     char product_type_str[32];//产品型号
     uint8_t product_type;//产品型号代码
     int64_t freq;//频率
     float rssi; //信号电平
     uint64_t efingerprint;//电子指纹
     float bandwidth; //带宽
     float distance;
}T_DecodeDjiNoneEncypt;
typedef struct _tag_PacketOpenDroneID
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     uint8_t drone_id[22];
     int64_t freq;//频率
     float rssi; //信号电平
     double drone_longitude;//飞机经度
     double drone_latitude;//飞机纬度
     double pilot_longitude;  //飞手经度
     double pilot_latitude;  //飞手纬度
     float speed;
     float up_speed; //向上速度
     int16_t direction;
     char wifi_ssid[50];// WIFI SSID
     uint8_t wifi_source_address[6];
}T_PacketOpenDroneID;
typedef struct _tag_Packet_DJIDroneID
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
}T_Packet_DJIDroneID;
typedef struct _tag_Packet_VedioDJIDrone
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioDJIDrone;
typedef struct _tag_Packet_VedioLightBrdigeDji
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioLightBrdigeDji;
typedef struct _tag_Packet_VedioLightBrdigeOthers
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioLightBrdigeOthers;
typedef struct _tag_Packet_VedioDaotongOrYuneecH52e
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
      int64_t freq;//频率
     uint64_t efingerprint;//电子指纹
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioDaotongOrYuneecH52e;
typedef struct _tag_Packet_VedioWifiDji
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioWifiDji;
typedef struct _tag_Packet_VedioWifiDji5M
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioWifiDji5M;
typedef struct _tag_Packet_Vedioharbosen
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioHarbosen;
typedef struct _tag_Packet_VedioAnalogAndBw5e6
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioAnalogAndBw5e6;
typedef struct _tag_Packet_VedioWifiParrot
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioWifiParrot;
typedef struct _tag_Packet_VedioWifiPowerEgg
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioWifiPowerEgg;
typedef struct _tag_Packet_VedioWifiSjf11
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioWifiSjf11;
typedef struct _tag_Packet_VedioEnhancedWifi
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioEnhancedWifi;
typedef struct _tag_Packet_DatalinkP900
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_DatalinkP900;
typedef struct _tag_Packet_VedioFeimi
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioFeimi;
typedef struct _tag_Packet_VedioAvatar
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioAvatar;
typedef struct _tag_Packet_VedioZongheng
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioZongheng;
typedef struct _tag_Packet_DjiM200
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_DjiM200;
typedef struct _tag_Packet_HaikanWeishi
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_HaikanWeishi;
typedef struct _tag_Packet_VedioDji04
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioDji04;
typedef struct _tag_Packet_VedioLteType
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_VedioLteType;
typedef struct _tag_Packet_WifiFlag
{
     char decode_flag;//是否CRC校验通过
     uint16_t packet_type; //包类型
     int64_t freq;//频率
     float rssi; //信号电平
     float bandwidth; //带宽
}T_Packet_WifiFlag;
typedef union
{
     T_DecodeDjiEncypt tDecodeDjiEncypt;
     T_DecodeDjiNoneEncypt tDecodeDjiNoneEncypt;
     T_PacketOpenDroneID tPacketOpenDroneID;
     T_Packet_DJIDroneID tPacket_DJIDroneID;
     T_Packet_VedioDJIDrone tPacket_VedioDJIDrone;
     T_Packet_VedioLightBrdigeDji tPacket_VedioLightBrdigeDji;
     T_Packet_VedioLightBrdigeOthers tPacket_VedioLightBrdigeOthers;
     T_Packet_VedioDaotongOrYuneecH52e tPacket_VedioDaotongOrYuneecH52e;
     T_Packet_VedioWifiDji tPacket_VedioWifiDji;
     T_Packet_VedioWifiDji5M tPacket_VedioWifiDji5M;
     T_Packet_VedioHarbosen tPacket_VedioHarbosen;
     T_Packet_VedioAnalogAndBw5e6 tPacket_VedioAnalogAndBw5e6;
     T_Packet_VedioWifiParrot tPacket_VedioWifiParrot;
     T_Packet_VedioWifiPowerEgg tPacket_VedioWifiPowerEgg;
     T_Packet_VedioWifiSjf11 tPacket_VedioWifiSjf11;
     T_Packet_VedioEnhancedWifi tPacket_VedioEnhancedWifi;
     T_Packet_DatalinkP900 tPacket_DatalinkP900;
     T_Packet_VedioFeimi tPacket_VedioFeimi;
     T_Packet_VedioAvatar tPacket_VedioAvatar;
     T_Packet_VedioZongheng tPacket_VedioZongheng;
     T_Packet_DjiM200 tPacket_tPacket_DjiM200;
     T_Packet_HaikanWeishi  tPacket_HaikanWeishi;
     T_Packet_VedioDji04 tPacket_VedioDji04;
     T_Packet_VedioLteType tPacket_VedioLteType;
     T_Packet_WifiFlag tPacket_WifiFlag;
}T_DroneInfo;

export double gps_distance(double lon1, double lat1, double lon2, double lat2);

#endif
