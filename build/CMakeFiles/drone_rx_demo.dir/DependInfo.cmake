
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gpio_control.cpp" "CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gps.cpp" "CMakeFiles/drone_rx_demo.dir/gps.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/gps.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main.cpp" "CMakeFiles/drone_rx_demo.dir/main.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/main.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main2.cpp" "CMakeFiles/drone_rx_demo.dir/main2.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/main2.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/DroneDetect.cpp" "CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_adapt.cpp" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_config.cpp" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_manage.cpp" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rdi.cpp" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_report.cpp" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o.d"
  "/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rssi.cpp" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o" "gcc" "CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
