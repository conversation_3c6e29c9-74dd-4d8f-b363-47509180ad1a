# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /data/App0730_Tag20250815/SH_Detection/drone_rx_demo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build

# Include any dependencies generated for this target.
include CMakeFiles/drone_rx_demo.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/drone_rx_demo.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/drone_rx_demo.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/drone_rx_demo.dir/flags.make

CMakeFiles/drone_rx_demo.dir/main.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/main.cpp.o: ../main.cpp
CMakeFiles/drone_rx_demo.dir/main.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/drone_rx_demo.dir/main.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/main.cpp.o -MF CMakeFiles/drone_rx_demo.dir/main.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/main.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main.cpp

CMakeFiles/drone_rx_demo.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/main.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main.cpp > CMakeFiles/drone_rx_demo.dir/main.cpp.i

CMakeFiles/drone_rx_demo.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/main.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main.cpp -o CMakeFiles/drone_rx_demo.dir/main.cpp.s

CMakeFiles/drone_rx_demo.dir/main2.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/main2.cpp.o: ../main2.cpp
CMakeFiles/drone_rx_demo.dir/main2.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/drone_rx_demo.dir/main2.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/main2.cpp.o -MF CMakeFiles/drone_rx_demo.dir/main2.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/main2.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main2.cpp

CMakeFiles/drone_rx_demo.dir/main2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/main2.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main2.cpp > CMakeFiles/drone_rx_demo.dir/main2.cpp.i

CMakeFiles/drone_rx_demo.dir/main2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/main2.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/main2.cpp -o CMakeFiles/drone_rx_demo.dir/main2.cpp.s

CMakeFiles/drone_rx_demo.dir/gps.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/gps.cpp.o: ../gps.cpp
CMakeFiles/drone_rx_demo.dir/gps.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/drone_rx_demo.dir/gps.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/gps.cpp.o -MF CMakeFiles/drone_rx_demo.dir/gps.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/gps.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gps.cpp

CMakeFiles/drone_rx_demo.dir/gps.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/gps.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gps.cpp > CMakeFiles/drone_rx_demo.dir/gps.cpp.i

CMakeFiles/drone_rx_demo.dir/gps.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/gps.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gps.cpp -o CMakeFiles/drone_rx_demo.dir/gps.cpp.s

CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o: ../plat/DroneDetect.cpp
CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o -MF CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/DroneDetect.cpp

CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/DroneDetect.cpp > CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.i

CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/DroneDetect.cpp -o CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.s

CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o: ../gpio_control.cpp
CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o -MF CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gpio_control.cpp

CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gpio_control.cpp > CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.i

CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/gpio_control.cpp -o CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.s

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o: ../plat/hl_caps_adapt.cpp
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o -MF CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_adapt.cpp

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_adapt.cpp > CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.i

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_adapt.cpp -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.s

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o: ../plat/hl_caps_config.cpp
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o -MF CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_config.cpp

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_config.cpp > CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.i

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_config.cpp -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.s

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o: ../plat/hl_caps_report.cpp
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o -MF CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_report.cpp

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_report.cpp > CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.i

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_report.cpp -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.s

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o: ../plat/hl_caps_manage.cpp
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o -MF CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_manage.cpp

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_manage.cpp > CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.i

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_manage.cpp -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.s

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o: ../plat/hl_caps_rdi.cpp
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o -MF CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rdi.cpp

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rdi.cpp > CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.i

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rdi.cpp -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.s

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o: CMakeFiles/drone_rx_demo.dir/flags.make
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o: ../plat/hl_caps_rssi.cpp
CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o: CMakeFiles/drone_rx_demo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o -MF CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o.d -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o -c /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rssi.cpp

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rssi.cpp > CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.i

CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/plat/hl_caps_rssi.cpp -o CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.s

# Object files for target drone_rx_demo
drone_rx_demo_OBJECTS = \
"CMakeFiles/drone_rx_demo.dir/main.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/main2.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/gps.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o" \
"CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o"

# External object files for target drone_rx_demo
drone_rx_demo_EXTERNAL_OBJECTS =

drone_rx_demo: CMakeFiles/drone_rx_demo.dir/main.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/main2.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/gps.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/build.make
drone_rx_demo: CMakeFiles/drone_rx_demo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX executable drone_rx_demo"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/drone_rx_demo.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/drone_rx_demo.dir/build: drone_rx_demo
.PHONY : CMakeFiles/drone_rx_demo.dir/build

CMakeFiles/drone_rx_demo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/drone_rx_demo.dir/cmake_clean.cmake
.PHONY : CMakeFiles/drone_rx_demo.dir/clean

CMakeFiles/drone_rx_demo.dir/depend:
	cd /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /data/App0730_Tag20250815/SH_Detection/drone_rx_demo /data/App0730_Tag20250815/SH_Detection/drone_rx_demo /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles/drone_rx_demo.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/drone_rx_demo.dir/depend

