# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /data/App0730_Tag20250815/SH_Detection/drone_rx_demo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /data/App0730_Tag20250815/SH_Detection/drone_rx_demo/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named drone_rx_demo

# Build rule for target.
drone_rx_demo: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 drone_rx_demo
.PHONY : drone_rx_demo

# fast build rule for target.
drone_rx_demo/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/build
.PHONY : drone_rx_demo/fast

gpio_control.o: gpio_control.cpp.o
.PHONY : gpio_control.o

# target to build an object file
gpio_control.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.o
.PHONY : gpio_control.cpp.o

gpio_control.i: gpio_control.cpp.i
.PHONY : gpio_control.i

# target to preprocess a source file
gpio_control.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.i
.PHONY : gpio_control.cpp.i

gpio_control.s: gpio_control.cpp.s
.PHONY : gpio_control.s

# target to generate assembly for a file
gpio_control.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/gpio_control.cpp.s
.PHONY : gpio_control.cpp.s

gps.o: gps.cpp.o
.PHONY : gps.o

# target to build an object file
gps.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/gps.cpp.o
.PHONY : gps.cpp.o

gps.i: gps.cpp.i
.PHONY : gps.i

# target to preprocess a source file
gps.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/gps.cpp.i
.PHONY : gps.cpp.i

gps.s: gps.cpp.s
.PHONY : gps.s

# target to generate assembly for a file
gps.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/gps.cpp.s
.PHONY : gps.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/main.cpp.s
.PHONY : main.cpp.s

main2.o: main2.cpp.o
.PHONY : main2.o

# target to build an object file
main2.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/main2.cpp.o
.PHONY : main2.cpp.o

main2.i: main2.cpp.i
.PHONY : main2.i

# target to preprocess a source file
main2.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/main2.cpp.i
.PHONY : main2.cpp.i

main2.s: main2.cpp.s
.PHONY : main2.s

# target to generate assembly for a file
main2.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/main2.cpp.s
.PHONY : main2.cpp.s

plat/DroneDetect.o: plat/DroneDetect.cpp.o
.PHONY : plat/DroneDetect.o

# target to build an object file
plat/DroneDetect.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.o
.PHONY : plat/DroneDetect.cpp.o

plat/DroneDetect.i: plat/DroneDetect.cpp.i
.PHONY : plat/DroneDetect.i

# target to preprocess a source file
plat/DroneDetect.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.i
.PHONY : plat/DroneDetect.cpp.i

plat/DroneDetect.s: plat/DroneDetect.cpp.s
.PHONY : plat/DroneDetect.s

# target to generate assembly for a file
plat/DroneDetect.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/DroneDetect.cpp.s
.PHONY : plat/DroneDetect.cpp.s

plat/hl_caps_adapt.o: plat/hl_caps_adapt.cpp.o
.PHONY : plat/hl_caps_adapt.o

# target to build an object file
plat/hl_caps_adapt.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.o
.PHONY : plat/hl_caps_adapt.cpp.o

plat/hl_caps_adapt.i: plat/hl_caps_adapt.cpp.i
.PHONY : plat/hl_caps_adapt.i

# target to preprocess a source file
plat/hl_caps_adapt.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.i
.PHONY : plat/hl_caps_adapt.cpp.i

plat/hl_caps_adapt.s: plat/hl_caps_adapt.cpp.s
.PHONY : plat/hl_caps_adapt.s

# target to generate assembly for a file
plat/hl_caps_adapt.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_adapt.cpp.s
.PHONY : plat/hl_caps_adapt.cpp.s

plat/hl_caps_config.o: plat/hl_caps_config.cpp.o
.PHONY : plat/hl_caps_config.o

# target to build an object file
plat/hl_caps_config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.o
.PHONY : plat/hl_caps_config.cpp.o

plat/hl_caps_config.i: plat/hl_caps_config.cpp.i
.PHONY : plat/hl_caps_config.i

# target to preprocess a source file
plat/hl_caps_config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.i
.PHONY : plat/hl_caps_config.cpp.i

plat/hl_caps_config.s: plat/hl_caps_config.cpp.s
.PHONY : plat/hl_caps_config.s

# target to generate assembly for a file
plat/hl_caps_config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_config.cpp.s
.PHONY : plat/hl_caps_config.cpp.s

plat/hl_caps_manage.o: plat/hl_caps_manage.cpp.o
.PHONY : plat/hl_caps_manage.o

# target to build an object file
plat/hl_caps_manage.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.o
.PHONY : plat/hl_caps_manage.cpp.o

plat/hl_caps_manage.i: plat/hl_caps_manage.cpp.i
.PHONY : plat/hl_caps_manage.i

# target to preprocess a source file
plat/hl_caps_manage.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.i
.PHONY : plat/hl_caps_manage.cpp.i

plat/hl_caps_manage.s: plat/hl_caps_manage.cpp.s
.PHONY : plat/hl_caps_manage.s

# target to generate assembly for a file
plat/hl_caps_manage.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_manage.cpp.s
.PHONY : plat/hl_caps_manage.cpp.s

plat/hl_caps_rdi.o: plat/hl_caps_rdi.cpp.o
.PHONY : plat/hl_caps_rdi.o

# target to build an object file
plat/hl_caps_rdi.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.o
.PHONY : plat/hl_caps_rdi.cpp.o

plat/hl_caps_rdi.i: plat/hl_caps_rdi.cpp.i
.PHONY : plat/hl_caps_rdi.i

# target to preprocess a source file
plat/hl_caps_rdi.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.i
.PHONY : plat/hl_caps_rdi.cpp.i

plat/hl_caps_rdi.s: plat/hl_caps_rdi.cpp.s
.PHONY : plat/hl_caps_rdi.s

# target to generate assembly for a file
plat/hl_caps_rdi.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rdi.cpp.s
.PHONY : plat/hl_caps_rdi.cpp.s

plat/hl_caps_report.o: plat/hl_caps_report.cpp.o
.PHONY : plat/hl_caps_report.o

# target to build an object file
plat/hl_caps_report.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.o
.PHONY : plat/hl_caps_report.cpp.o

plat/hl_caps_report.i: plat/hl_caps_report.cpp.i
.PHONY : plat/hl_caps_report.i

# target to preprocess a source file
plat/hl_caps_report.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.i
.PHONY : plat/hl_caps_report.cpp.i

plat/hl_caps_report.s: plat/hl_caps_report.cpp.s
.PHONY : plat/hl_caps_report.s

# target to generate assembly for a file
plat/hl_caps_report.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_report.cpp.s
.PHONY : plat/hl_caps_report.cpp.s

plat/hl_caps_rssi.o: plat/hl_caps_rssi.cpp.o
.PHONY : plat/hl_caps_rssi.o

# target to build an object file
plat/hl_caps_rssi.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.o
.PHONY : plat/hl_caps_rssi.cpp.o

plat/hl_caps_rssi.i: plat/hl_caps_rssi.cpp.i
.PHONY : plat/hl_caps_rssi.i

# target to preprocess a source file
plat/hl_caps_rssi.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.i
.PHONY : plat/hl_caps_rssi.cpp.i

plat/hl_caps_rssi.s: plat/hl_caps_rssi.cpp.s
.PHONY : plat/hl_caps_rssi.s

# target to generate assembly for a file
plat/hl_caps_rssi.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/drone_rx_demo.dir/build.make CMakeFiles/drone_rx_demo.dir/plat/hl_caps_rssi.cpp.s
.PHONY : plat/hl_caps_rssi.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... drone_rx_demo"
	@echo "... gpio_control.o"
	@echo "... gpio_control.i"
	@echo "... gpio_control.s"
	@echo "... gps.o"
	@echo "... gps.i"
	@echo "... gps.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... main2.o"
	@echo "... main2.i"
	@echo "... main2.s"
	@echo "... plat/DroneDetect.o"
	@echo "... plat/DroneDetect.i"
	@echo "... plat/DroneDetect.s"
	@echo "... plat/hl_caps_adapt.o"
	@echo "... plat/hl_caps_adapt.i"
	@echo "... plat/hl_caps_adapt.s"
	@echo "... plat/hl_caps_config.o"
	@echo "... plat/hl_caps_config.i"
	@echo "... plat/hl_caps_config.s"
	@echo "... plat/hl_caps_manage.o"
	@echo "... plat/hl_caps_manage.i"
	@echo "... plat/hl_caps_manage.s"
	@echo "... plat/hl_caps_rdi.o"
	@echo "... plat/hl_caps_rdi.i"
	@echo "... plat/hl_caps_rdi.s"
	@echo "... plat/hl_caps_report.o"
	@echo "... plat/hl_caps_report.i"
	@echo "... plat/hl_caps_report.s"
	@echo "... plat/hl_caps_rssi.o"
	@echo "... plat/hl_caps_rssi.i"
	@echo "... plat/hl_caps_rssi.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

