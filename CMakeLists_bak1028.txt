cmake_minimum_required(VERSION 3.1.0)

#add_definitions(-DDUAL_BOARD)
#add_definitions(-DSINGLE_BOARD)

set (CMAKE_C_COMPILER "/usr/bin/aarch64-linux-gnu-gcc")
set (CMAKE_CXX_COMPILER "/usr/bin/aarch64-linux-gnu-g++")

project(drone_rx_demo)
add_executable(${PROJECT_NAME} main.cpp main2.cpp gps.cpp)

set (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
set (CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=c99")

target_link_libraries(drone_rx_demo
        /data/App/SH_Detection/drone_rx_demo/so/libERadiodll.so
        /data/App/SH_Detection/drone_rx_demo/so/libsignal.so
        /data/App/SH_Detection/drone_rx_demo/so/libwifidll.so
        /usr/lib/aarch64-linux-gnu/libiio.so
        /usr/lib/aarch64-linux-gnu/libad9361.so
     )
