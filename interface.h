/*******************************************************************************
* COPYRIGHT Beijing RHHK Equipment CO.,LTD
********************************************************************************
* 文件名称:  interface.h
* 功能描述:  CAPS内部数据结构定义、变量函数声明
* 版    本:
* 编写日期:  2024/12/26
* 说    明:
* 修改历史:
* 修改日期    修改人  BugID/CRID      修改内容
* ------------------------------------------------------------------------------
* 2024/10/25  zonghui    创建文件
*******************************************************************************/
#ifndef _INTERFACE_H_
#define _INTERFACE_H_
#pragma once

#include "stdint.h"
#include <stdio.h>
#include <time.h>
#include <stdarg.h> 

#define DUAL_BOARD 1// 1是二合一双模块，0是单模块

   
enum
{ 
	PACKET_DJI_ID = 1,    // incldue: all OcuSync protocal DJI drone
	PACKET_VEDIO_DJI, // incldue: all OcuSync protocal DJI drone
	PACKET_VEDIO_DAOTONG, //include: daotong nano, nano+, lite, lite+, evo2_v3
	PACKET_VEDIO_LightBrdige_DJI, //include: DJI P4,   Inspire 2,
	PACKET_VEDIO_LightBrdige_OTHERS,//include daotong evo2_v2, Feimi, Yuneec H850 RTK,Zino Mini Pro
	PACKET_VEDIO_Yuneec_H52e,//include yuneec h52e
	PACKET_VEDIO_WIFI_DJI, //include  Spark, PHANTOM 3, PHANTOM 3s,  TELLO 
	PACKET_VEDIO_WIFI_DJI_5M, //include DJI Mini, Mini SE, Air 1
	PACKET_VEDIO_WIFI_PARROT, //parrot
	PACKET_VEDIO_WIFI_XIAOMI, //xiaomi
	PACKET_VEDIO_WIFI_SJF11, //世季F11
	PACKET_VEDIO_WIFI_POWEREGG,//PowerEgg
	PACKET_VEDIO_WIFI_OTHERS,
	PACKET_VEDIO_HARBOSEN,//黑鹰1号和Zino
	PACKET_VEDIO_ANALOG,
	PACKET_VEDIO_ENHANCED_WIFI,//增强型wifi,这种特殊的wifi，很多无人机使用，正常wifi很少有使用
	PACKET_DATALINK_P900, //数传，P840 P900 纵横
	PACKET_VEDIO_FEIMI,// X8SE 使用增强型wifi，
	PACKET_VEDIO_Avatar,// 阿凡达图传芯片
	PACKET_VEDIO_ZONGHENG,//临舟科技, 纵横科技，天航智远
	PACKET_DJI_ENCYPT, 
	PACKET_DJI_M200,
	PACKET_HAIKAN_WEISHI, //海康威视MX6120A
	PACKET_VEDIO_DJI_O4, // DJI OcuSync 4
	PACKET_RSSI_MEASURE_END = 99,
	PACKET_VEDIO_OTHERS = 100,
	PACKET_SCAN_ROUND_END, //每扫描一轮结束发一个PACKET_SCAN_ROUND_END空包
	PACKET_HEART_BEAT_1,  // big device
	PACKET_HEART_BEAT_2, //  dji small device
	PACKET_OPEN_DRONE_ID,
	PACKET_VEDIO_LTE_TYPE,
	PACKET_VEDIO_LightBridge, //包含所有LightBrdige信号类型, 大疆P4，evo2_v2, Feimi,  普宙无人机，
};

#pragma pack(push)
#pragma pack(4)
typedef struct {
	uint16_t packet_type; //包类型
	uint16_t seq_num;  //包序号
	uint16_t state_info; //状态信息
	uint8_t drone_serial_num[17]; //无人机唯一序列号
	double drone_longitude;//飞机经度
	double drone_latitude;//飞机纬度
	float altitude;//海拔
	float height; //高度
	float north_speed;//向北速度
	float east_speed;//向东速度
	float up_speed; //向上速度
	int16_t pitch_angle; // only for ver1
	int16_t roll_angle; // only for ver1  翻滚角度
	int16_t yaw_angle; //航向
	uint64_t gpstime; //only for ver2// gps时间
	double pilot_longitude;  //飞手经度
	double pilot_latitude;  //飞手纬度
	double home_longitude;//返航经度
	double home_latitude;//返航纬度
	uint8_t product_type;//产品型号代码
	char product_type_str[32];//产品型号
	uint8_t uuid_length;//通用唯一识别码长度
	uint8_t uuid[18];// 通用唯一识别码
	uint8_t license[10];//执照
	char wifi_ssid[50];// WIFI SSID
	char wifi_vendor[50];
	uint8_t wifi_destination_address[6];
	uint8_t wifi_source_address[6];
	uint8_t wifi_bss_id_address[6];
	uint64_t efingerprint;//电子指纹
	int64_t freq;//频率
	float rssi; //信号电平
	float sync_corr; //同步头的相关值
	float bandwidth; //带宽
	char decode_flag;//是否CRC校验通过
	char wifi_flag;
	uint16_t device_num;
	uint16_t antenna_code;//天线码 (测向使用)
	int32_t _10ms_offset;
	///// the following is for open drone id
	uint8_t protocal_version;
	uint8_t UA_type; //[0]Not Declared [1]Aeroplane,[2]Helicopter,[3]Gyroplane,[4]Hybrid Lift,[5]Ornithopter,[6]Glider[7]Kite [8]Free Balloon
	uint8_t drone_id[22];
	int16_t direction;
	float speed;
	uint8_t dji_byte176[176];
	uint16_t  reserved[14];
}DJI_FLIGHT_INFO_Str;

typedef struct {
	double local_longitude;  //经度
	double local_latitude;   //纬度
	double local_altitude;   //高度
}Drone_Detect_GpsInfo_Rpt;


typedef enum {
	LOG_ERROR = 1,
	LOG_KEY,
	LOG_WARN,
	LOG_INFO,
    LOG_DEBUG,
    LOG_END
}LogLevel;
 
#pragma pack(pop)

#ifdef _WIN32
#define ADD_EXPORTS
/* You should define ADD_EXPORTS *only* when building the DLL. */
#ifdef ADD_EXPORTS
#define ADDAPI __declspec(dllexport)
#else
#define ADDAPI __declspec(dllimport)
#endif

/* Define calling convention in one place, for convenience. */
#define ADDCALL __cdecl

#else /* _WIN32 not defined. */

/* Define with no value on non-Windows OSes. */
#define ADDAPI
#define ADDCALL

#endif

typedef struct _tag_GpsData
{
	double dLongitude;  //经度
	double dLatitude;   //纬度
	double dAltitude;   //高度
	int hour;
	int minute;
	int second;
}GpsData, *LPGpsData;
int start_gps(char *ip_remote, char *ip_local);
int get_gps(GpsData *gps);

typedef void(*drone_cb_fn)(DJI_FLIGHT_INFO_Str *message);
typedef void(*spectrum_cb_fn)(float *spectrum, int64_t start_freq, int length, float resolution);
typedef void(*rssi_cb_fun)(float rssi_drone[], float rssi_max[], int64_t freq);
typedef void(*iq_cb_fn)(int16_t *iq, int iq_num, int64_t freq);

#ifdef __cplusplus
extern "C"
{
#endif

	extern ADDAPI void config_ip(char *ipstring);
	extern ADDAPI int ADDCALL  GetDeviceNum();
	extern ADDAPI int ADDCALL  OpenDevice(int antenna);

	//freq参数设置为0，
	//gain参数设置最大70
	//sample_rate  15.36e6  13.44e6, 11.52e6
	//drone_cb_fn是回调函数
	//extern ADDAPI void ADDCALL  start_drone_scan(int64_t freq, int gain, int sample_rate, drone_cb_fn cb); //	不推荐使用，
	extern ADDAPI void ADDCALL  start_drone_scan2(int64_t freq, int gain, int sample_rate, drone_cb_fn cb, spectrum_cb_fn cb_psd);// 推荐用这个
	extern ADDAPI void ADDCALL  start_drone_scan3(int64_t freq_list[], int freq_list_len, int gain, int samperate, drone_cb_fn cb, spectrum_cb_fn cb_psd);
	extern ADDAPI void ADDCALL stop_device();
	extern ADDAPI void ADDCALL close_device();
	extern ADDAPI int Set_RX_Antenna(int antenna);
	extern ADDAPI void ADDCALL startIQ(iq_cb_fn iq_cb);
	extern ADDAPI int get_spectrum(float psd[],  int64_t *center_freq);	//	不推荐使用，请使用spectrum_callback
	extern ADDAPI int get_state(float *x);
	extern ADDAPI void enable_gpio(int flag);// 1 enable, 0 disable;
	extern ADDAPI void set_gpio(int value,int pin=-1);
	extern ADDAPI void set_gpio2(int value, int pin = -1);
	extern ADDAPI void rssi_measure(int64_t freq, int gain, int measure_time, int sample_rate, int antenna_code[], rssi_cb_fun cb, drone_cb_fn cbd);
	extern ADDAPI int ADDCALL config_second_device(char *ip_remote, char *ip_local, int64_t freq, int gain);
	extern ADDAPI void set_scan_time(int ms);
	extern ADDAPI void ADDCALL  start_spectrum_scan(int64_t f_low, int64_t f_high,float resolution, int gain, spectrum_cb_fn spectrum_call);

	extern ADDAPI void Reset_ScanList(); //重置扫描列表 必须加在start_drone_scan 之后
	extern ADDAPI int AddOneFreq2ScanList(int64_t freq);//扫描列表中增加一个频点,必须加在start_drone_scan 之后
	extern ADDAPI int  Get_ScanList(int64_t *freq);//获取扫描列表
	extern ADDAPI void set_rf_gain(int gain);
	extern ADDAPI void set_deviceA_dji_idfun(int enable_disable); //1=enable, 0=disable
	extern ADDAPI int get_device_state();// 0  异常 (未连接等原因) //  1待机  //2   工作中
	extern ADDAPI int get_analog_image(uint8_t *imag, int *row, int *col); // videos : rows x cols
	
#ifdef __cplusplus
}
#endif
#endif


