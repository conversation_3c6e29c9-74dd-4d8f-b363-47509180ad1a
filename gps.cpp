/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* �ļ�����:  gps.cpp
* ��������:  ����gps���ľ�γ�Ȳ���ͻ�������Ӧ
* ��    ��:
* ��д����:  2024/03/15
* ˵    ��:
* �޸���ʷ:
* �޸�����    �޸���  BugID/CRID      �޸�����
* ------------------------------------------------------------------------------
* 2025/4/25  zonghui    �����ļ�
*******************************************************************************/
#include <iostream>
#include "interface.h"
#include "string.h"

using namespace  std;
#ifdef _WIN32
#include <winsock2.h>
#include <WS2tcpip.h>

#pragma warning(disable:4996)
//��Ӷ�̬���lib
#pragma comment(lib, "ws2_32.lib")

static SOCKET m_Socket;
static SOCKADDR_IN m_BindAddress;   //�󶨵�ַ
static SOCKADDR_IN m_RemoteAddress; //Զ�̵�ַ
static int m_RemoteAddressLen;

int start_gps(char *ip_remote, char *ip_local)
{

	// socket����
	WSADATA  wsaData;
	if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
		cout << "WSAStartup error:" << GetLastError() << endl;
		return false;
	}

	// socket����
	m_Socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
	if (m_Socket == INVALID_SOCKET)
	{
		closesocket(m_Socket);
		m_Socket = INVALID_SOCKET;
		return false;
	}

	// ��ռ��<ip, port>

	int port = 9026;
	int remoteport = 9023;
	m_BindAddress.sin_family = AF_INET;
	m_BindAddress.sin_addr.S_un.S_addr = inet_addr(ip_local);
	m_BindAddress.sin_port = htons(port);
	auto ret = bind(m_Socket, (sockaddr*)&m_BindAddress, sizeof(SOCKADDR));
	if (ret == SOCKET_ERROR)
	{
		closesocket(m_Socket);
		m_Socket = INVALID_SOCKET;
		return false;
	}

	// ���պͷ���
	char recvBuf[1024] = { 0 };
	char sendBuf[1024] = "send";

	m_RemoteAddress.sin_family = AF_INET;
	m_RemoteAddress.sin_addr.S_un.S_addr = inet_addr(ip_remote);
	m_RemoteAddress.sin_port = htons(9023);
	m_RemoteAddressLen = sizeof(m_RemoteAddress);

	printf("�����ð�ռ�õ�����, ��ip: %s, port: %d\n", inet_ntoa(m_BindAddress.sin_addr), ntohs(m_BindAddress.sin_port));

	int sendLen = sendto(m_Socket, sendBuf, strlen(sendBuf), 0, (sockaddr*)&m_RemoteAddress, m_RemoteAddressLen);
	if (sendLen > 0) {
		cout << "���͵�Զ�̶˵���Ϣ�� " << sendBuf << endl;
	}
	u_long m = 1;
	ioctlsocket(m_Socket, FIONBIO, &m); //���÷���������

	return true;
}
#else

#include <thread>
#include <fcntl.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "unistd.h"
#define SERVER_PORT 9023
#define SERVER_ADDR "***********"
static int sockfd;
struct sockaddr_in server_addr;
/*******************************************************************************
* ��������: start_gps
* ��������: �����߳���Ӧsocker�û�����
* ��������: 
* ��������:         ����                     ����/���     ����
* ip_remote         char*                    ����          ��ַָ�� 
* ip_local          char*                    ����          ��ַָ��    
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int start_gps(char *ip_remote, char *ip_local)
{


    // cereat UDP socket
    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) {
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }

    // set UDP server
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(SERVER_PORT);
    inet_pton(AF_INET, SERVER_ADDR, &server_addr.sin_addr);


    char message[128]="start gps";
    // ��������
    if (sendto(sockfd, message, strlen(message), 0, (struct sockaddr*) &server_addr, sizeof(server_addr)) < 0) {
        perror("sendto failed");
        exit(EXIT_FAILURE);
    }

    int flags;
    if ((flags = fcntl(sockfd, F_GETFL, 0)) < 0) {
        perror("fcntl:F_GETFL");
        return -1;
    }
    // ���÷�����ģʽ
    flags |= O_NONBLOCK;
    if (fcntl(sockfd, F_SETFL, flags) < 0) {
        perror("fcntl:F_SETFL");
        return -1;
    }
    return 1;
}


#endif
/*******************************************************************************
* ��������: get_gps
* ��������: �����߳���Ӧsocker�û�����
* ��������: 
* ��������:         ����                     ����/���     ����
* gps               GpsData*                 ���          gps��Ϣָ�� 
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int get_gps(GpsData *gps)
{
#if 1
	char gps_char[128];
	memset(gps_char, 0, sizeof(gps_char));
#ifdef _WIN32
	int recvLen = recvfrom(m_Socket, gps_char, 128, 0, (sockaddr*)&m_RemoteAddress, &m_RemoteAddressLen);
#else
    socklen_t len = sizeof(server_addr);
    int recvLen = recvfrom(sockfd, gps_char, 128, 0,(struct sockaddr*)&server_addr,&len);
#endif
//printf("recvLen=%d\n",recvLen);

	int hour = 0, min = 0, second = 0;
	double lat_lon_alt[3];
	int ct = 0;
	if (recvLen > 0)
	{
		//utc = Fri Aug 18 08:07 : 30 2023
		//lat = 121.513424
		//lon = 31.334731
		//alt = 23.800000
		for (int n = 5; n < 128; n++)
		{
			char latstr[15];
			if (gps_char[n] == ':')
			{
				memset(latstr, 0, 15);
				latstr[0] = gps_char[n - 2]; latstr[1] = gps_char[n - 1]; hour = atoi(latstr);
				latstr[0] = gps_char[n + 1]; latstr[1] = gps_char[n + 2]; min = atoi(latstr);
				latstr[0] = gps_char[n + 4]; latstr[1] = gps_char[n + 5]; second = atoi(latstr);
				n = n + 5;
			}

			if (gps_char[n] == '=')
			{

				memset(latstr, 0, 15);
				for (int m = 1; m < 12; m++)
				{
					if (gps_char[n + m] == '\n') break;
					latstr[m - 1] = gps_char[n + m];
				}
				lat_lon_alt[ct++] = atof(latstr);
				ct %= 3;
			}
		}

		gps->dLongitude = lat_lon_alt[0];
		gps->dLatitude = lat_lon_alt[1];
		gps->dAltitude = lat_lon_alt[2];
		gps->hour = hour;
		gps->minute = min;
		gps->second = second;

		return 1;
	}
#endif
	return 0;
}