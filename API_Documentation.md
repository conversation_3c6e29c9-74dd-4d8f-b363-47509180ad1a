# API接口文档

## 概述

本文档详细描述了无人机检测系统的API接口，包括数据结构定义、回调函数类型和核心API函数。

## 数据结构定义

### 1. 无人机信息结构体 (DJI_FLIGHT_INFO_Str)

```c
typedef struct {
    uint16_t packet_type;           // 包类型
    uint16_t seq_num;               // 包序号
    uint16_t state_info;            // 状态信息
    uint8_t drone_serial_num[17];   // 无人机唯一序列号
    double drone_longitude;         // 飞机经度
    double drone_latitude;          // 飞机纬度
    float altitude;                 // 海拔
    float height;                   // 高度
    float north_speed;              // 向北速度
    float east_speed;               // 向东速度
    float up_speed;                 // 向上速度
    int16_t pitch_angle;            // 俯仰角
    int16_t roll_angle;             // 翻滚角度
    int16_t yaw_angle;              // 航向
    uint64_t gpstime;               // GPS时间
    double pilot_longitude;         // 飞手经度
    double pilot_latitude;          // 飞手纬度
    double home_longitude;          // 返航经度
    double home_latitude;           // 返航纬度
    uint8_t product_type;           // 产品型号代码
    char product_type_str[32];      // 产品型号
    uint8_t uuid_length;            // 通用唯一识别码长度
    uint8_t uuid[18];               // 通用唯一识别码
    uint8_t license[10];            // 执照
    char wifi_ssid[50];             // WIFI SSID
    char wifi_vendor[50];           // WiFi厂商
    uint8_t wifi_destination_address[6];  // WiFi目标地址
    uint8_t wifi_source_address[6];       // WiFi源地址
    uint8_t wifi_bss_id_address[6];       // WiFi BSS ID
    uint64_t efingerprint;          // 电子指纹
    int64_t freq;                   // 频率
    float rssi;                     // 信号电平
    float sync_corr;                // 同步头的相关值
    float bandwidth;                // 带宽
    char decode_flag;               // 是否CRC校验通过
    char wifi_flag;                 // WiFi标志
    uint16_t device_num;            // 设备编号
    uint16_t antenna_code;          // 天线码(测向使用)
    int32_t _10ms_offset;           // 10ms偏移
    // Open Drone ID相关字段
    uint8_t protocal_version;       // 协议版本
    uint8_t UA_type;                // 无人机类型
    uint8_t drone_id[22];           // 无人机ID
    int16_t direction;              // 方向
    float speed;                    // 速度
    uint8_t dji_byte176[176];       // DJI特定数据
    uint16_t reserved[14];          // 保留字段
} DJI_FLIGHT_INFO_Str;
```

### 2. GPS数据结构体 (GpsData)

```c
typedef struct _tag_GpsData {
    double dLongitude;  // 经度
    double dLatitude;   // 纬度
    double dAltitude;   // 高度
    int hour;           // 小时
    int minute;         // 分钟
    int second;         // 秒
} GpsData, *LPGpsData;
```

### 3. 无人机检测GPS信息报告 (Drone_Detect_GpsInfo_Rpt)

```c
typedef struct {
    double local_longitude;  // 经度
    double local_latitude;   // 纬度
    double local_altitude;   // 高度
} Drone_Detect_GpsInfo_Rpt;
```

## 包类型枚举

```c
enum {
    PACKET_DJI_ID = 1,                    // DJI无人机ID
    PACKET_VEDIO_DJI,                     // DJI OcuSync视频
    PACKET_VEDIO_DAOTONG,                 // 道通视频
    PACKET_VEDIO_LightBrdige_DJI,         // DJI LightBridge视频
    PACKET_VEDIO_LightBrdige_OTHERS,      // 其他LightBridge视频
    PACKET_VEDIO_Yuneec_H52e,             // 昊翔H52e视频
    PACKET_VEDIO_WIFI_DJI,                // DJI WiFi视频
    PACKET_VEDIO_WIFI_DJI_5M,             // DJI 5M WiFi视频
    PACKET_VEDIO_WIFI_PARROT,             // Parrot WiFi视频
    PACKET_VEDIO_WIFI_XIAOMI,             // 小米WiFi视频
    PACKET_VEDIO_WIFI_SJF11,              // 世季F11 WiFi视频
    PACKET_VEDIO_WIFI_POWEREGG,           // PowerEgg WiFi视频
    PACKET_VEDIO_WIFI_OTHERS,             // 其他WiFi视频
    PACKET_VEDIO_HARBOSEN,                // 黑鹰1号和Zino
    PACKET_VEDIO_ANALOG,                  // 模拟视频
    PACKET_VEDIO_ENHANCED_WIFI,           // 增强型WiFi
    PACKET_DATALINK_P900,                 // P840/P900数传
    PACKET_VEDIO_FEIMI,                   // 飞米X8SE
    PACKET_VEDIO_Avatar,                  // 阿凡达图传芯片
    PACKET_VEDIO_ZONGHENG,                // 纵横科技
    PACKET_DJI_ENCYPT,                    // DJI加密
    PACKET_DJI_M200,                      // DJI M200
    PACKET_HAIKAN_WEISHI,                 // 海康威视MX6120A
    PACKET_VEDIO_DJI_O4,                  // DJI OcuSync 4
    PACKET_RSSI_MEASURE_END = 99,         // RSSI测量结束
    PACKET_VEDIO_OTHERS = 100,            // 其他视频
    PACKET_SCAN_ROUND_END,                // 扫描轮次结束
    PACKET_HEART_BEAT_1,                  // 心跳包1
    PACKET_HEART_BEAT_2,                  // 心跳包2
    PACKET_OPEN_DRONE_ID,                 // Open Drone ID
    PACKET_VEDIO_LTE_TYPE,                // LTE类型视频
    PACKET_VEDIO_LightBridge,             // 所有LightBridge信号类型
};
```

## 回调函数类型定义

### 1. 无人机检测回调函数

```c
typedef void(*drone_cb_fn)(DJI_FLIGHT_INFO_Str *message);
```

**参数说明:**
- `message`: 指向无人机信息结构体的指针，包含检测到的无人机详细信息

### 2. 频谱回调函数

```c
typedef void(*spectrum_cb_fn)(float *spectrum, int64_t start_freq, int length, float resolution);
```

**参数说明:**
- `spectrum`: 频谱数据数组指针
- `start_freq`: 起始频率 (Hz)
- `length`: 频谱数据长度
- `resolution`: 频率分辨率 (Hz)

### 3. RSSI回调函数

```c
typedef void(*rssi_cb_fun)(float rssi_drone[], float rssi_max[], int64_t freq);
```

**参数说明:**
- `rssi_drone`: 无人机信号RSSI数组 (8个天线)
- `rssi_max`: 最大RSSI数组 (8个天线)
- `freq`: 当前频率 (Hz)

### 4. IQ数据回调函数

```c
typedef void(*iq_cb_fn)(int16_t *iq, int iq_num, int64_t freq);
```

**参数说明:**
- `iq`: IQ数据指针 (IQIQIQ...格式)
- `iq_num`: IQ对数量
- `freq`: 当前频率 (Hz)

## 核心API函数

### 1. 设备管理

#### config_ip
```c
void config_ip(char *ipstring);
```
**功能**: 配置设备IP地址
**参数**: `ipstring` - IP地址字符串

#### GetDeviceNum
```c
int GetDeviceNum();
```
**功能**: 获取设备数量
**返回值**: 设备数量，-1表示失败

#### OpenDevice
```c
int OpenDevice(int antenna);
```
**功能**: 打开设备
**参数**: `antenna` - 天线选择
**返回值**: 设备编号，-1表示失败

#### stop_device
```c
void stop_device();
```
**功能**: 停止设备运行

#### close_device
```c
void close_device();
```
**功能**: 关闭设备

### 2. 无人机扫描

#### start_drone_scan2 (推荐)
```c
void start_drone_scan2(int64_t freq, int gain, int sample_rate, drone_cb_fn cb, spectrum_cb_fn cb_psd);
```
**功能**: 启动无人机扫描 (推荐使用)
**参数**:
- `freq`: 频率 (0为扫描模式，非0为锁定频率)
- `gain`: 增益 (0-70dB)
- `sample_rate`: 采样率 (15.36e6, 13.44e6, 11.52e6)
- `cb`: 无人机检测回调函数
- `cb_psd`: 频谱回调函数

#### start_drone_scan3
```c
void start_drone_scan3(int64_t freq_list[], int freq_list_len, int gain, int samperate, drone_cb_fn cb, spectrum_cb_fn cb_psd);
```
**功能**: 使用频率列表启动无人机扫描
**参数**:
- `freq_list`: 频率列表数组
- `freq_list_len`: 频率列表长度
- 其他参数同start_drone_scan2

### 3. 频谱分析

#### start_spectrum_scan
```c
void start_spectrum_scan(int64_t f_low, int64_t f_high, float resolution, int gain, spectrum_cb_fn spectrum_call);
```
**功能**: 启动频谱扫描
**参数**:
- `f_low`: 起始频率 (Hz)
- `f_high`: 结束频率 (Hz)
- `resolution`: 频率分辨率 (必须>=5KHz)
- `gain`: 增益
- `spectrum_call`: 频谱回调函数

#### get_spectrum
```c
int get_spectrum(float psd[], int64_t *center_freq);
```
**功能**: 获取频谱数据 (不推荐使用，建议使用回调)
**参数**:
- `psd`: 频谱数据数组
- `center_freq`: 中心频率指针
**返回值**: 成功返回1，失败返回0

### 4. RSSI测量

#### rssi_measure
```c
void rssi_measure(int64_t freq, int gain, int measure_time, int sample_rate, int antenna_code[], rssi_cb_fun cb, drone_cb_fn cbd);
```
**功能**: 进行RSSI测量
**参数**:
- `freq`: 测量频率 (Hz)
- `gain`: 增益
- `measure_time`: 测量时间 (ms)
- `sample_rate`: 采样率
- `antenna_code`: 天线编码数组
- `cb`: RSSI回调函数
- `cbd`: 无人机检测回调函数

### 5. IQ数据采集

#### startIQ
```c
void startIQ(iq_cb_fn iq_cb);
```
**功能**: 启动IQ数据采集
**参数**: `iq_cb` - IQ数据回调函数

### 6. 天线控制

#### Set_RX_Antenna
```c
int Set_RX_Antenna(int antenna);
```
**功能**: 设置接收天线
**参数**: `antenna` - 天线编号 (1-8)
**返回值**: 成功返回0，失败返回-1

### 7. GPIO控制

#### enable_gpio
```c
void enable_gpio(int flag);
```
**功能**: 启用/禁用GPIO
**参数**: `flag` - 1启用，0禁用

#### set_gpio
```c
void set_gpio(int value, int pin);
```
**功能**: 设置GPIO值
**参数**:
- `value`: GPIO值
- `pin`: GPIO引脚 (默认-1)

#### set_gpio2
```c
void set_gpio2(int value, int pin);
```
**功能**: 设置GPIO值 (第二组)
**参数**:
- `value`: GPIO值
- `pin`: GPIO引脚 (默认-1)

### 8. 扫描控制

#### set_scan_time
```c
void set_scan_time(int ms);
```
**功能**: 设置扫描时间间隔
**参数**: `ms` - 时间间隔 (毫秒)

#### Reset_ScanList
```c
void Reset_ScanList();
```
**功能**: 重置扫描列表 (必须在start_drone_scan之后调用)

#### AddOneFreq2ScanList
```c
int AddOneFreq2ScanList(int64_t freq);
```
**功能**: 向扫描列表添加频点
**参数**: `freq` - 频率 (Hz)
**返回值**: 成功返回0，失败返回-1

#### Get_ScanList
```c
int Get_ScanList(int64_t *freq);
```
**功能**: 获取扫描列表
**参数**: `freq` - 频率数组指针
**返回值**: 频点数量

### 9. 设备状态

#### get_device_state
```c
int get_device_state();
```
**功能**: 获取设备状态
**返回值**:
- 0: 异常 (未连接等原因)
- 1: 待机
- 2: 工作中

#### get_state
```c
int get_state(float *x);
```
**功能**: 获取设备状态信息
**参数**: `x` - 状态值指针
**返回值**: 状态码

### 10. RF控制

#### set_rf_gain
```c
void set_rf_gain(int gain);
```
**功能**: 设置RF增益
**参数**: `gain` - 增益值 (0-70dB)

#### set_deviceA_dji_idfun
```c
void set_deviceA_dji_idfun(int enable_disable);
```
**功能**: 设置设备A的DJI ID功能
**参数**: `enable_disable` - 1启用，0禁用

### 11. 图像处理

#### get_analog_image
```c
int get_analog_image(uint8_t *imag, int *row, int *col);
```
**功能**: 获取模拟图像数据
**参数**:
- `imag`: 图像数据缓冲区
- `row`: 行数指针
- `col`: 列数指针
**返回值**: 成功返回1，失败返回0

### 12. GPS功能

#### start_gps
```c
int start_gps(char *ip_remote, char *ip_local);
```
**功能**: 启动GPS功能
**参数**:
- `ip_remote`: 远程设备IP
- `ip_local`: 本地IP
**返回值**: 成功返回1，失败返回0

#### get_gps
```c
int get_gps(GpsData *gps);
```
**功能**: 获取GPS数据
**参数**: `gps` - GPS数据结构体指针
**返回值**: 成功返回1，失败返回0

### 13. 双设备配置

#### config_second_device
```c
int config_second_device(char *ip_remote, char *ip_local, int64_t freq, int gain);
```
**功能**: 配置第二个设备
**参数**:
- `ip_remote`: 远程IP
- `ip_local`: 本地IP
- `freq`: 频率
- `gain`: 增益
**返回值**: 成功返回0，失败返回-1

## 使用示例

### 基本无人机检测

```c
#include "interface.h"

void drone_callback(DJI_FLIGHT_INFO_Str *message) {
    if (message->decode_flag) {
        printf("检测到无人机: 频率=%.1f MHz, RSSI=%.1f dBm\n",
               message->freq / 1e6, message->rssi);
        if (message->packet_type == PACKET_DJI_ENCYPT) {
            // 处理DJI加密信号
        } else {
            // 处理非加密信号
            printf("位置: %.6f, %.6f\n",
                   message->drone_longitude, message->drone_latitude);
        }
    }
}

void spectrum_callback(float *spectrum, int64_t start_freq, int length, float resolution) {
    // 处理频谱数据
    printf("频谱数据: 起始频率=%.1f MHz, 长度=%d\n",
           start_freq / 1e6, length);
}

int main() {
    // 配置设备
    config_ip("***********");

    // 打开设备
    int device_num = OpenDevice(1);
    if (device_num == -1) {
        printf("设备打开失败\n");
        return -1;
    }

    // 启动GPS
    start_gps("***********", "***********8");

    // 设置扫描时间
    set_scan_time(50);

    // 启动无人机扫描
    start_drone_scan2(0, 70, 13.44e6*2, drone_callback, spectrum_callback);

    // 主循环
    while (1) {
        sleep(1);
    }

    return 0;
}
```

## 注意事项

1. **频率单位**: 所有频率参数使用Hz为单位
2. **增益范围**: RF增益范围为0-70dB
3. **采样率**: 推荐使用15.36MHz、13.44MHz或11.52MHz
4. **回调函数**: 所有回调函数在检测线程中执行，注意线程安全
5. **设备初始化**: 必须先调用config_ip和OpenDevice
6. **扫描列表**: Reset_ScanList和AddOneFreq2ScanList必须在start_drone_scan之后调用
7. **内存管理**: 回调函数中的数据指针仅在回调期间有效
