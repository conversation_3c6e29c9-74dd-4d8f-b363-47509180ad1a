# 无人机检测系统使用说明

## 目录
1. [系统要求](#系统要求)
2. [安装部署](#安装部署)
3. [编译构建](#编译构建)
4. [配置说明](#配置说明)
5. [运行操作](#运行操作)
6. [参数说明](#参数说明)
7. [输出解读](#输出解读)
8. [故障排除](#故障排除)
9. [维护保养](#维护保养)

## 系统要求

### 硬件要求
- **CPU**: x86_64或ARM64架构，推荐4核以上
- **内存**: 最低2GB，推荐4GB以上
- **存储**: 最低1GB可用空间
- **网络**: 以太网接口，支持192.168.2.x网段
- **射频设备**: 兼容的SDR设备或专用射频模块

### 软件要求
- **操作系统**: 
  - Linux: Ubuntu 18.04+, CentOS 7+, Debian 9+
  - Windows: Windows 10/11 (64位)
- **编译工具**: 
  - GCC 7.0+ 或 MSVC 2017+
  - CMake 4.0+
- **依赖库**: 
  - libiio (Linux)
  - libad9361 (Linux)
  - WinSock2 (Windows)

### 推荐硬件平台
- **NVIDIA Jetson系列**: Nano, Xavier NX, AGX Xavier
- **工控机**: 支持PCIe扩展的工控主板
- **服务器**: 标准x86_64服务器

## 安装部署

### Linux系统安装

#### 1. 系统准备
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install build-essential cmake git libiio-dev libad9361-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install cmake git libiio-devel libad9361-devel
```

#### 2. 获取源码
```bash
git clone <repository-url>
cd drone_rx_demo
```

#### 3. 配置动态库路径
```bash
# 添加库路径到系统配置
echo "/path/to/drone_rx_demo/so" | sudo tee -a /etc/ld.so.conf
sudo ldconfig
```

#### 4. 设置权限
```bash
# 设置执行权限
chmod +x script.sh
# 如需GPIO访问权限
sudo usermod -a -G gpio $USER
```

### Windows系统安装

#### 1. 安装开发环境
- 安装Visual Studio 2017或更高版本
- 安装CMake 4.0或更高版本
- 安装Git for Windows

#### 2. 获取源码
```cmd
git clone <repository-url>
cd drone_rx_demo
```

#### 3. 配置环境变量
将`so`目录添加到系统PATH环境变量中

## 编译构建

### Linux编译

#### 方法1: 使用脚本编译
```bash
./script.sh
```

#### 方法2: 手动编译
```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
```

#### 方法3: 交叉编译 (ARM64)
```bash
# 设置交叉编译工具链
export CC=/usr/bin/aarch64-linux-gnu-gcc
export CXX=/usr/bin/aarch64-linux-gnu-g++

mkdir build-arm64 && cd build-arm64
cmake -DCMAKE_C_COMPILER=$CC -DCMAKE_CXX_COMPILER=$CXX ..
make -j$(nproc)
```

### Windows编译

#### 使用Visual Studio
```cmd
mkdir build && cd build
cmake -G "Visual Studio 16 2019" -A x64 ..
cmake --build . --config Release
```

#### 使用MinGW
```cmd
mkdir build && cd build
cmake -G "MinGW Makefiles" ..
mingw32-make -j4
```

### 编译选项

#### CMake配置选项
```bash
# Debug模式
cmake -DCMAKE_BUILD_TYPE=Debug ..

# Release模式
cmake -DCMAKE_BUILD_TYPE=Release ..

# 启用双板模式
cmake -DDUAL_BOARD=ON ..

# 指定安装路径
cmake -DCMAKE_INSTALL_PREFIX=/opt/drone_detect ..
```

#### 编译宏定义
- `DUAL_BOARD`: 启用双模块模式
- `SINGLE_BOARD`: 单模块模式
- `ORIN_NANO_PWRIO_V1`: NVIDIA Jetson Orin Nano PWR IO版本1
- `ORIN_NANO_GPS_TTYTHS3`: GPS使用TTYTHS3串口

## 配置说明

### 网络配置

#### IP地址配置
```bash
# 设备默认IP配置
设备IP: ***********
主机IP: ***********8 (单模块) / ***********01 (双模块)
```

#### 端口配置
```bash
GPS通信端口: 9023 (设备) / 9026 (主机)
检测数据端口: 8080 (可配置)
```

### 频率配置

#### 频率扫描文件 (freq_scan.txt)
```
use_this_file: 0          # 是否使用此文件 (0=否, 1=是)
300-400                   # 300-400 MHz
430-435                   # 430-435 MHz  
825-845                   # 825-845 MHz
902-928                   # 902-928 MHz (ISM频段)
1280-1380                 # 1.28-1.38 GHz
1430-1444                 # 1.43-1.444 GHz
2385-2485                 # 2.4 GHz WiFi频段
5150-5250                 # 5.1-5.25 GHz
5725-5940                 # 5.7-5.9 GHz
```

#### 频率范围说明
- **300-400 MHz**: 低频段，部分工业无人机
- **430-435 MHz**: 业余无线电频段
- **825-845 MHz**: 蜂窝通信频段
- **902-928 MHz**: ISM频段，部分无人机使用
- **1.2-1.4 GHz**: L波段，GPS附近频段
- **2.4 GHz**: 主要WiFi频段，大部分消费级无人机
- **5.1-5.9 GHz**: 5G WiFi频段，高端无人机

### GPS配置

#### GPS设备配置
```bash
# GPS设备IP设置
GPS_DEVICE_IP="***********"
HOST_IP="***********8"

# 本地坐标设置 (上海)
LOCAL_LONGITUDE=121.5134
LOCAL_LATITUDE=31.3348

# 本地坐标设置 (长沙)  
LOCAL_LONGITUDE=112.97764
LOCAL_LATITUDE=28.249499
```

### 射频参数配置

#### 基本参数
```bash
# 采样率选项 (Hz)
SAMPLE_RATE_1=11.52e6    # 11.52 MHz
SAMPLE_RATE_2=13.44e6    # 13.44 MHz (推荐)
SAMPLE_RATE_3=15.36e6    # 15.36 MHz

# 增益范围
RF_GAIN_MIN=0            # 最小增益 0dB
RF_GAIN_MAX=70           # 最大增益 70dB
RF_GAIN_DEFAULT=60       # 默认增益 60dB

# 扫描时间
SCAN_TIME_MIN=10         # 最小扫描时间 10ms
SCAN_TIME_DEFAULT=50     # 默认扫描时间 50ms
SCAN_TIME_MAX=1000       # 最大扫描时间 1000ms
```

#### 天线配置
```bash
# 天线编码 (8路天线阵列)
ANTENNA_CODE[0]=0b000    # 天线1
ANTENNA_CODE[1]=0b100    # 天线2  
ANTENNA_CODE[2]=0b010    # 天线3
ANTENNA_CODE[3]=0b110    # 天线4
ANTENNA_CODE[4]=0b001    # 天线5
ANTENNA_CODE[5]=0b101    # 天线6
ANTENNA_CODE[6]=0b011    # 天线7
ANTENNA_CODE[7]=0b111    # 天线8
```

## 运行操作

### 基本运行

#### 单模块模式
```bash
# 使用默认参数
./drone_rx_demo

# 指定基本参数
./drone_rx_demo -ip *********** -gain 70

# 完整参数示例
./drone_rx_demo -ip *********** -gain 70 -rate 13.44 -longi 121.5134 -lati 31.3348 -t 50
```

#### 双模块模式
```bash
# 编译时启用DUAL_BOARD宏
cmake -DDUAL_BOARD=ON ..
make

# 运行双模块程序
./drone_rx_demo -ip ***********0
```

### 高级运行模式

#### 频率锁定模式
```bash
# 锁定2.4GHz频率
./drone_rx_demo -freq 2400 -gain 70

# 锁定5.8GHz频率  
./drone_rx_demo -freq 5800 -gain 70
```

#### 特定天线模式
```bash
# 使用天线1
./drone_rx_demo -ant 1 -gain 70

# 使用天线8
./drone_rx_demo -ant 8 -gain 70
```

#### 自定义位置模式
```bash
# 北京坐标
./drone_rx_demo -longi 116.4074 -lati 39.9042

# 深圳坐标
./drone_rx_demo -longi 114.0579 -lati 22.5431
```

### 运行状态监控

#### 系统状态
- **状态0**: 异常状态 (设备未连接或故障)
- **状态1**: 待机状态 (设备已连接，未开始检测)
- **状态2**: 工作状态 (正在进行无人机检测)

#### GPS状态
- **GPS有效**: 显示经纬度坐标
- **GPS无效**: 显示0.000000坐标

#### 检测状态
- **检测计数**: 显示检测到的无人机数量
- **频率显示**: 当前扫描或锁定的频率
- **RSSI显示**: 当前信号强度

## 参数说明

### 命令行参数详解

| 参数 | 类型 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| -ip | string | *********** | 设备IP地址 | -ip *********** |
| -gain | int | 60 | RF增益(0-70dB) | -gain 70 |
| -rate | float | 13.44 | 采样率(MHz) | -rate 15.36 |
| -freq | float | 0 | 锁定频率(MHz,0=扫描) | -freq 2400 |
| -ant | int | 1 | 天线选择(1-8) | -ant 3 |
| -longi | float | 112.97764 | 本地经度 | -longi 121.5134 |
| -lati | float | 28.249499 | 本地纬度 | -lati 31.3348 |
| -t | int | 50 | 扫描时间(ms) | -t 100 |

### 配置文件参数

#### freq_scan.txt参数
- **use_this_file**: 是否使用频率配置文件
- **频率范围**: 格式为"起始频率-结束频率"(MHz)

#### 内部配置参数
- **DUAL_BOARD**: 双板模式开关
- **SAMPLE_RATE**: 内部采样率设置
- **ANTENNA_CODE**: 天线编码数组

## 输出解读

### 控制台输出

#### 系统启动信息
```
device_num =1  is connected     # 设备连接成功
GPS: 121.513400/31.334800      # GPS坐标
state=2                        # 设备工作状态
```

#### 无人机检测信息

##### DJI加密信号
```
Encypted Mavic_O4_ID=1a2b3c4d5e freq=2437.0, rssi=-45.2
```
- **ID**: 加密的无人机标识符
- **freq**: 检测频率(MHz)  
- **rssi**: 信号强度(dBm)

##### DJI非加密信号
```
drone GPS:121.513400/31.334800  Height|Altitude 50.2|23.8  East|Noth|Up Speed 2.1|1.5|0.3
home GPS:121.513000/31.334500  pilot GPS:121.513200/31.334600
serial: 1234567890ABCDEF, model(1): DJI Mavic Air 2   freq=2437.0, rssi=-42
freq=2437.0, efingerprint=123456789  bw=20
the drone is 0.15 km from here
```

##### 其他无人机信号
```
DJI OcuSync vedio is detected at freq=5745.0, efingerprint=987654321 rssi=-38.5, bw=20M
LightBrdige DJI P4 is detected at freq=5765.0, rssi=-41.2, bw=10M
DJI Mini/Mini SE/Air1 is detected at freq=2462.0, rssi=-39.8, bw=20M
```

### 日志文件输出

#### scan_result.log格式
```
2024-12-26 14:30:25 Encypted Mavic_O4_ID=1a2b3c4d5e freq=2437.0, rssi=-45.2
2024-12-26 14:30:26  freq=2437.0, rssi=-42.0  distance=0.15
 drone GPS:121.513400/31.334800  Height|Altitude 50.2|23.8  East|Noth|Up Speed 2.1|1.5|0.3
home GPS:121.513000/31.334500  pilot GPS:121.513200/31.334600
serial: 1234567890ABCDEF, model(1): DJI Mavic Air 2
```

### 网络数据输出

#### UDP数据包格式
- **端口**: 可配置端口
- **格式**: JSON或二进制
- **内容**: 结构化的检测结果数据

#### JSON数据示例
```json
{
  "timestamp": "2024-12-26T14:30:25Z",
  "packet_type": 48,
  "drone_longitude": 121.513400,
  "drone_latitude": 31.334800,
  "altitude": 23.8,
  "height": 50.2,
  "freq": 2437000000,
  "rssi": -42.0,
  "distance": 0.15,
  "product_type_str": "DJI Mavic Air 2"
}
```

## 故障排除

### 常见问题及解决方案

#### 1. 设备连接问题

**问题**: `device_num = -1 is connected` 或设备无法连接
**原因**:
- 网络连接问题
- IP地址配置错误
- 设备未上电或故障

**解决方案**:
```bash
# 检查网络连接
ping ***********

# 检查网络配置
ifconfig
ip addr show

# 重新配置网络
sudo ifconfig eth0 ***********8 netmask *************
sudo route add default gw ***********
```

#### 2. GPS无法获取位置

**问题**: GPS显示`0.000000/0.000000`
**原因**:
- GPS设备未连接
- GPS信号弱或无信号
- 网络通信问题

**解决方案**:
```bash
# 检查GPS网络连接
telnet *********** 9023

# 手动设置本地坐标
./drone_rx_demo -longi 121.5134 -lati 31.3348

# 检查GPS设备状态
# 确保GPS设备已获得卫星信号
```

#### 3. 编译错误

**问题**: 编译时出现库文件缺失错误
**解决方案**:
```bash
# Ubuntu/Debian
sudo apt install libiio-dev libad9361-dev

# CentOS/RHEL
sudo yum install libiio-devel libad9361-devel

# 检查库文件路径
ldconfig -p | grep iio
ldconfig -p | grep ad9361
```

#### 4. 权限问题

**问题**: GPIO访问权限不足
**解决方案**:
```bash
# 添加用户到gpio组
sudo usermod -a -G gpio $USER

# 重新登录或使用newgrp
newgrp gpio

# 或使用sudo运行
sudo ./drone_rx_demo
```

#### 5. 动态库加载失败

**问题**: 运行时提示找不到.so文件
**解决方案**:
```bash
# 添加库路径
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/path/to/drone_rx_demo/so

# 或永久添加
echo 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/path/to/drone_rx_demo/so' >> ~/.bashrc
source ~/.bashrc

# 或使用ldconfig
echo "/path/to/drone_rx_demo/so" | sudo tee -a /etc/ld.so.conf
sudo ldconfig
```

### 性能优化建议

#### 1. 系统优化
```bash
# 设置CPU性能模式
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# 禁用不必要的服务
sudo systemctl disable bluetooth
sudo systemctl disable cups

# 增加网络缓冲区
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### 2. 实时性优化
```bash
# 设置进程优先级
nice -n -10 ./drone_rx_demo

# 或使用实时调度
sudo chrt -f 50 ./drone_rx_demo
```

### 调试模式

#### 启用调试输出
```bash
# 编译Debug版本
cmake -DCMAKE_BUILD_TYPE=Debug ..
make

# 使用GDB调试
gdb ./drone_rx_demo
(gdb) run -ip *********** -gain 70
```

#### 日志级别控制
```c
// 在代码中设置日志级别
set_log_level(LOG_DEBUG);  // 详细调试信息
set_log_level(LOG_INFO);   // 一般信息
set_log_level(LOG_WARN);   // 警告信息
set_log_level(LOG_ERROR);  // 错误信息
```

## 维护保养

### 定期维护

#### 1. 系统清理
```bash
# 清理日志文件
find . -name "*.log" -mtime +30 -delete

# 清理临时文件
rm -f *.tmp *.dat

# 检查磁盘空间
df -h
```

#### 2. 软件更新
```bash
# 更新系统包
sudo apt update && sudo apt upgrade

# 更新项目代码
git pull origin main
make clean && make
```

#### 3. 硬件检查
- 检查网线连接
- 检查天线连接
- 检查设备指示灯状态
- 检查散热情况

### 备份与恢复

#### 配置备份
```bash
# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz *.txt *.conf

# 备份日志文件
tar -czf logs_backup_$(date +%Y%m%d).tar.gz *.log
```

#### 系统恢复
```bash
# 恢复配置
tar -xzf config_backup_20241226.tar.gz

# 重新编译
make clean && make
```

### 监控脚本

#### 自动重启脚本
```bash
#!/bin/bash
# auto_restart.sh

while true; do
    if ! pgrep -f "drone_rx_demo" > /dev/null; then
        echo "$(date): 重启无人机检测程序"
        cd /path/to/drone_rx_demo
        ./drone_rx_demo -ip *********** -gain 70 &
    fi
    sleep 60
done
```

#### 状态监控脚本
```bash
#!/bin/bash
# monitor.sh

LOG_FILE="/var/log/drone_monitor.log"

while true; do
    # 检查进程状态
    if pgrep -f "drone_rx_demo" > /dev/null; then
        echo "$(date): 程序运行正常" >> $LOG_FILE
    else
        echo "$(date): 程序未运行" >> $LOG_FILE
    fi

    # 检查网络连接
    if ping -c 1 *********** > /dev/null 2>&1; then
        echo "$(date): 网络连接正常" >> $LOG_FILE
    else
        echo "$(date): 网络连接异常" >> $LOG_FILE
    fi

    sleep 300  # 5分钟检查一次
done
```

## 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **技术支持电话**: +86-xxx-xxxx-xxxx
- **在线文档**: https://docs.rhhk.com
- **问题反馈**: https://github.com/rhhk/drone_rx_demo/issues

### 常用资源
- **用户手册**: 本文档
- **API文档**: API_Documentation.md
- **模块文档**: Module_Documentation.md
- **更新日志**: CHANGELOG.md
- **FAQ**: https://docs.rhhk.com/faq

### 培训服务
- **在线培训**: 定期举办在线技术培训
- **现场培训**: 可安排现场技术培训
- **技术咨询**: 提供专业技术咨询服务

---

**版权声明**: 本文档版权归北京RHHK设备有限公司所有，未经授权不得复制或传播。
