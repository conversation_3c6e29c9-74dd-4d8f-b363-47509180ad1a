/*******************************************************************************
* COPYRIGHT BeiJing RHHK Equipment CO.,LTD
********************************************************************************
* �ļ�����:  gpio_control.cpp
* ��������:  nx nano gpio�ܽſ���
* ��    ��:
* ��д����:  2025/04/29
* ˵    ��:
* �޸���ʷ:
* �޸�����    �޸���  BugID/CRID      �޸�����
* ------------------------------------------------------------------------------
* 2025/4/25  liubw    �����ļ�
*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <signal.h>
#include <time.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#define PORT 10086
#define MAX_BUFFER 2048
#define MAX_MESSAGE 512
#define DELIMITER '-'
#define MAX_CLIENTS 10

typedef struct {
    int client_fd;
    int seconds;
    char message[MAX_MESSAGE];
    int request_id;
    int pin;
    const char *gpio_name;
} ClientRequest;

typedef struct {
    int running;
    int port;
} ServerContext;

static int request_counter = 0;

typedef struct {
    const char *name;
    int pin;
} GpioMapping;

GpioMapping gpio_map[] = {
    {"PH.07", 398}, {"PY.04", 474}, {"PY.03", 473}, {"PY.01", 471},
    {"PZ.06", 484}, {"PG.06", 389}, {"PR.05", 461}, {"PI.01", 400},
    {"PI.00", 399}, {"PAC.06", 492}, {"PR.04", 460}, {"PY.00", 470},
    {"PN.01", 433}, {"PZ.05", 483}, {"PZ.04", 482}, {"PZ.03", 481},
    {"PQ.05", 453}, {"PH.00", 391}, {"PI.02", 401}, {"PY.02", 472},
    {"PQ.06", 454}
};

#define GPIO_MAPPING_COUNT (sizeof(gpio_map) / sizeof(GpioMapping))

// GPIO state tracking array [pin, state]
static int gpio_states[GPIO_MAPPING_COUNT][2] = {
    {398,0}, {474,0}, {473,0}, {471,0}, {484,0}, {389,0}, 
    {461,0}, {400,0}, {399,0}, {492,0}, {460,0}, {470,0}, 
    {433,0}, {483,0}, {482,0}, {481,0}, {453,0}, {391,0}, 
    {401,0}, {472,0}, {454,0}
};
/*******************************************************************************
* ��������: get_gpio_state
* ��������: ��ȡgpio״̬
* ��������: 
* ��������:         ����                     ����/���     ����
* pin               int                      ����          �ܽű��  
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int get_gpio_state(int pin) {
    for (int i = 0; i < GPIO_MAPPING_COUNT; i++) {
        if (gpio_states[i][0] == pin) {
            return gpio_states[i][1];
        }
    }
    return -1; // Invalid pin
}

void set_gpio_state(int pin, int state) {
    for (int i = 0; i < GPIO_MAPPING_COUNT; i++) {
        if (gpio_states[i][0] == pin) {
            gpio_states[i][1] = state;
            return;
        }
    }
}

/*******************************************************************************
* ��������: setup_gpio
* ��������: ����gpio����
* ��������: 
* ��������:         ����                     ����/���     ����
* pin               int                      ����          �ܽű�� 
* gpio_name         const char *             ����          �ܽ�����
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int setup_gpio(int pin, const char *gpio_name) {
    if (get_gpio_state(pin) == 1) {
        printf("GPIO %d already exported, skipping\n", pin);
        return -1;
    }

    FILE *fp = fopen("/sys/class/gpio/export", "w");
    if (!fp) {
        perror("Failed to export GPIO");
        return -1;
    }
    fprintf(fp, "%d", pin);
    fclose(fp);

    char path[50];
    snprintf(path, sizeof(path), "/sys/class/gpio/%s/direction", gpio_name);
    fp = fopen(path, "w");
    if (!fp) {
        perror("Failed to set GPIO direction");
        return -1;
    }
    fprintf(fp, "out");
    fclose(fp);

    set_gpio_state(pin, 1);
    printf("GPIO %d (%s) successfully exported\n", pin, gpio_name);
    return 0;
}

/*******************************************************************************
* ��������: gpio_high
* ��������: ����gpio����״̬
* ��������: 
* ��������:         ����                     ����/���     ����
* pin               int                      ����          �ܽű�� 
* gpio_name         const char *             ����          �ܽ�����
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int gpio_high(const char *gpio_name) {
    char path[50];
    snprintf(path, sizeof(path), "/sys/class/gpio/%s/value", gpio_name);
    FILE *fp = fopen(path, "w");
    if (!fp) {
        perror("Failed to set GPIO high");
        return -1;
    }
    fprintf(fp, "1");
    fclose(fp);
    return 0;
}

int gpio_low(const char *gpio_name) {
    char path[50];
    snprintf(path, sizeof(path), "/sys/class/gpio/%s/value", gpio_name);
    FILE *fp = fopen(path, "w");
    if (!fp) {
        perror("Failed to set GPIO low");
        return -1;
    }
    fprintf(fp, "0");
    fclose(fp);
    return 0;
}
/*******************************************************************************
* ��������: cleanup_high
* ��������: ���gpio״̬
* ��������: 
* ��������:         ����                     ����/���     ����
* pin               int                      ����          �ܽű�� 
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
void cleanup_gpio(int pin) {
    if (get_gpio_state(pin) == 0) {
        printf("GPIO %d not exported, skipping cleanup\n", pin);
        return;
    }

    FILE *fp = fopen("/sys/class/gpio/unexport", "w");
    if (!fp) {
        perror("Failed to unexport GPIO");
        return;
    }
    fprintf(fp, "%d", pin);
    fclose(fp);

    set_gpio_state(pin, 0);
    printf("GPIO %d successfully unexported\n", pin);
}

/*******************************************************************************
* ��������: get_gpio_name
* ��������: ��ȡgpio״̬����
* ��������: 
* ��������:         ����                     ����/���     ����
* pin               int                      ����          �ܽű�� 
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
const char *get_gpio_name(int pin) {
    for (int i = 0; i < GPIO_MAPPING_COUNT; i++) {
        if (gpio_map[i].pin == pin) {
            return gpio_map[i].name;
        }
    }
    printf("Invalid GPIO pin: %d\n", pin);
    return NULL;
}

/*******************************************************************************
* ��������: timer_callback
* ��������: ��ʱʱ�䵽���gpio״̬
* ��������: 
* ��������:         ����                     ����/���     ����
* sigval sv         union                    ����          �ź�����ṹָ�� 
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
void timer_callback(union sigval sv) {
    ClientRequest *req = (ClientRequest *)sv.sival_ptr;
    
    if (get_gpio_state(req->pin) == 1) {
        gpio_low(req->gpio_name);
        printf("GPIO %d (%s) set to low\n", req->pin, req->gpio_name);
        cleanup_gpio(req->pin);
    }
    
    free(req);
}

/*******************************************************************************
* ��������: query_gpio_status
* ��������: ������ʱ��
* ��������: 
* ��������:         ����                     ����/���     ����
* client_fd         int                      ����          sockfd
* buffer            char *                   ����          buffer
* length            int                      ����          length
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
int query_gpio_status(int client_fd, char *buffer, int length)
{
	//char *delim_query = strchr(buffer, ',');
	char resp_gpio_buffer[MAX_MESSAGE];
    char *token;           // ���ڴ洢�ָ����ַ���
    const char s[2] = ","; // �ָ���
    int numbers[3];        // ���ڴ洢ת���������
    int status[3];        // ���ڴ洢ת���������
    int i = 0;             // ��������
    int len = 0;
	  char tmpbuf[16];

	  if (buffer == NULL)
	  {
        return -1;
	  }
    printf("1-B###################################################\n");
	  char *delim = strchr(buffer, ',');
	  if (delim != NULL)
	  {
        printf("2-B###################################################\n");
        token = strtok(buffer, s);
        while (token != NULL) 
	      {
            numbers[i] = atoi(token);  // ���ָ����ַ���ת��Ϊ�������洢
            i++;
            token = strtok(NULL, s);   // ��ȡ��һ���ָ����ַ���
        }

        memset(resp_gpio_buffer, 0, sizeof(resp_gpio_buffer));
        for (int j = 0; j < i; j++) 
	      {
	        memset(tmpbuf, 0, sizeof(tmpbuf));
		      status[j] = get_gpio_state(numbers[j]); 
			    sprintf(tmpbuf, "%d-%d,", numbers[j],status[j]);
			    strcat(resp_gpio_buffer, tmpbuf);
        }
		    len = strlen(resp_gpio_buffer);
		    if ((len > 0) && (resp_gpio_buffer[len-1] == ','))
		    {
		       resp_gpio_buffer[len-1] = '@';
		    }
            
        send(client_fd, resp_gpio_buffer, strlen(resp_gpio_buffer), 0); 
        printf("2.1[gpio_client][send response]:%s\n", buffer);
        printf("3-B###################################################\n");   
	  }
     
    if ((NULL == strchr(buffer, ',')) && (NULL == strchr(buffer, '-')))
	  {
        memset(resp_gpio_buffer, 0, sizeof(resp_gpio_buffer));
        int pin = atoi(buffer);
        char *gpio_name = get_gpio_name(pin);
        int config_result = setup_gpio(pin, gpio_name);
        int control_result = gpio_low(gpio_name);
        if ((gpio_name != NULL) && (config_result == 0) && (control_result == 0))
		    {
            snprintf(resp_gpio_buffer, sizeof(resp_gpio_buffer), "OK: GPIO %d Set Low, Successful!", pin);
		    }
        else
        {
            snprintf(resp_gpio_buffer, sizeof(resp_gpio_buffer), "ERROR: GPIO %d Set Low, Failed!", pin);
        }
        send(client_fd, resp_gpio_buffer, strlen(resp_gpio_buffer), 0);
        printf("2.2[gpio_client][send response]:%s\n", buffer);
	  }		 
	  
	  return 0;
}

/*******************************************************************************
* ��������: gpio_client
* ��������: ������ʱ��
* ��������: 
* ��������:         ����                     ����/���     ����
* arg               void*                    ����          �ļ��ṹָ�� 
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
void *gpio_client(void *arg) {
    int client_fd = *(int *)arg;
    free(arg);
    char msg_buf[128] = {0};  // 通用消息缓冲�?

    while (1) 
    {
        char buffer[MAX_BUFFER] = {0};
        int valread = read(client_fd, buffer, MAX_BUFFER - 1);
        if (valread <= 0) break;
        
        printf("1.[gpio_client][read request]:%s\n", buffer);
        char *delim = strchr(buffer, DELIMITER);
        printf("1-A###################################################\n");
        query_gpio_status(client_fd, buffer, valread);
        printf("2-A###################################################\n");
        if (delim != NULL) 
		    {
            printf("3-A###################################################\n"); 
            *delim = '\0';
            int pin = atoi(buffer);
            int seconds = atoi(delim + 1);

            if (seconds <= 0) 
            {
                memset(msg_buf, 0, sizeof(msg_buf));
                snprintf(msg_buf, sizeof(msg_buf), "ERROR: GPIO %d invalid duration (%d<=0)\n", pin, seconds);
                send(client_fd, msg_buf, strlen(msg_buf), 0);
                continue;
            }

            const char *gpio_name = get_gpio_name(pin);
            if (get_gpio_state(pin) == 1) 
            {
                memset(msg_buf, 0, sizeof(msg_buf));
                snprintf(msg_buf, sizeof(msg_buf), "ERROR: GPIO %d is busy\n", pin);
                send(client_fd, msg_buf, strlen(msg_buf), 0);
                continue;
            }

            ClientRequest *req = calloc(1, sizeof(ClientRequest));
            if (!req) 
            {
                memset(msg_buf, 0, sizeof(msg_buf));
                snprintf(msg_buf, sizeof(msg_buf), "ERROR: Server memory full\n");
                send(client_fd, msg_buf, strlen(msg_buf), 0);
                continue;
            }

            req->client_fd = client_fd;
            req->seconds = seconds;
            req->request_id = ++request_counter;
            req->pin = pin;
            req->gpio_name = gpio_name;

            int config_result = setup_gpio(pin, gpio_name);
            int control_result = gpio_high(gpio_name);

            timer_t timerid;
            struct sigevent sev;
            sev.sigev_notify = SIGEV_THREAD;
            sev.sigev_notify_function = timer_callback;
            sev.sigev_value.sival_ptr = req;

            if (timer_create(CLOCK_REALTIME, &sev, &timerid) == -1) 
            {
               free(req);
               continue;
            }

            struct itimerspec its;
            its.it_interval = {0};
            its.it_value = {.tv_sec = seconds};

            if (timer_settime(timerid, 0, &its, NULL) == -1) 
            {
                free(req);
            }

            if ((config_result == 0) && (control_result == 0))
            {
                memset(msg_buf, 0, sizeof(msg_buf));
                snprintf(msg_buf, sizeof(msg_buf), "OK: GPIO %d activated for %d seconds\n", pin, seconds);
            }
			      else
			      {
			          memset(msg_buf, 0, sizeof(msg_buf));
                snprintf(msg_buf, sizeof(msg_buf), "ERROR: GPIO %d unactivated for %d seconds\n", pin, seconds);
            }
            
            snprintf(msg_buf, sizeof(msg_buf), "OK: GPIO %d activated for %d seconds\n", pin, seconds);
            send(client_fd, msg_buf, strlen(msg_buf), 0);
            printf("2.3[gpio_client][send response]:%s\n", buffer);
        }    
    }
    
    close(client_fd);
	  pthread_exit(NULL);
    return NULL;
}
/*******************************************************************************
* ��������: socket_server_thread
* ��������: socket�߳̽����û�����
* ��������: 
* ��������:         ����                     ����/���     ����
* arg               void*                    ����          �ļ��ṹָ�� 
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
void* socket_server_thread(void* arg) {
    ServerContext* pctx = (ServerContext*)arg;
    
    printf("GPIO server entering port:%d, running:%d\n", pctx->port, pctx->running);
    int server_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (server_fd < 0) {
        perror("Socket creation failed");
        free(pctx);
        return NULL;
    }

    // 设置端口复用
    int opt = 1;
    setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));

    struct sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(pctx->port);

    if (bind(server_fd, (struct sockaddr*)&addr, sizeof(addr))) {
        perror("Bind failed");
        close(server_fd);
        free(pctx);
        return NULL;
    }

    if (listen(server_fd, MAX_CLIENTS)) {
        perror("Listen failed");
        close(server_fd);
        free(pctx);
        return NULL;
    }

    printf("GPIO server running on port:%d, running:%d\n", pctx->port, pctx->running);

    while (pctx->running) {
        struct sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);
        
        int client_fd = accept(server_fd, (struct sockaddr*)&client_addr, &client_len);
        if (client_fd < 0) {
            if (pctx->running) perror("Accept failed");
            continue;
        }

        printf("New connection from %s\n", inet_ntoa(client_addr.sin_addr));

        int* pArg = malloc(sizeof(int));
        if (!pArg) {
            perror("Malloc failed");
            close(client_fd);
            continue;
        }
        *pArg = client_fd;

        pthread_t tid;
        if (pthread_create(&tid, NULL, gpio_client, (void *)pArg)) {
            perror("Thread create failed");
            free(pArg);
            close(client_fd);
        } else {
            pthread_detach(tid);
        }
    }

    close(server_fd);
    free(pctx);
    printf("Socket server stopped\n"); 
    return NULL;
}
/*******************************************************************************
* ��������: CreatePowerControl
* ��������: �����߳���Ӧsocker�û�����
* ��������: 
* ��������:         ����                     ����/���     ����
* arg               void*                    ����          �ļ��ṹָ�� 
* ����ֵ: ��
* ����˵��:
*
* �޸�����    �汾��   �޸���  �޸����ݣ������ڱ������ڵ�ȱ���޸���Ҫд�ڴ˴���
* -----------------------------------------------------------------
*******************************************************************************/
void CreatePowerControl(void)
{
     pthread_t server_thread;
     ServerContext *pctx = malloc(sizeof(ServerContext));
	   if (pctx != NULL)
	   {
         pctx->running = 1;
         pctx->port = PORT;
	 
         printf("GPIO server init:%d, running:%d\n", pctx->port, pctx->running);
         if (pthread_create(&server_thread, NULL, socket_server_thread, (void *)pctx) != 0) 
         {
             printf("Failed to create power control task failed\n");
			       free(pctx);
             return;
         }	 
         pthread_detach(server_thread);
		     printf("power control thread running...\n");
    }
	  else
	  {
        printf("power control thread malloc failed...\n");
	  }
    return;
}

