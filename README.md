# 无人机检测系统 (Drone Detection System)

## 项目概述

本项目是一个基于射频信号分析的无人机检测系统，由北京RHHK设备有限公司开发。该系统能够实时检测、识别和定位各种类型的无人机，包括DJI、道通、飞米等主流品牌的无人机。

## 主要功能

### 1. 无人机信号检测
- **多品牌支持**: 支持检测DJI、道通、飞米、Parrot、PowerEgg等多种品牌无人机
- **多协议识别**: 支持OcuSync、LightBridge、WiFi、增强WiFi等多种通信协议
- **实时检测**: 实时扫描和分析射频信号，快速识别无人机信号特征

### 2. 无人机信息解析
- **位置信息**: 解析无人机GPS坐标、高度、速度等飞行参数
- **设备信息**: 获取无人机序列号、型号、电子指纹等设备标识
- **操控信息**: 检测飞手位置、返航点等操控相关信息

### 3. 信号分析
- **频谱扫描**: 支持70MHz-6GHz宽频段频谱扫描
- **RSSI测量**: 实时测量信号强度，支持多天线阵列
- **带宽分析**: 自动识别信号带宽和调制方式

### 4. GPS定位集成
- **本地GPS**: 集成GPS模块获取设备自身位置
- **距离计算**: 自动计算无人机与检测设备的距离
- **坐标转换**: 支持多种坐标系统转换

### 5. 数据记录与报告
- **实时日志**: 自动记录检测结果到日志文件
- **结构化数据**: 支持JSON格式的结构化数据输出
- **网络传输**: 支持UDP网络传输检测结果

## 技术特点

### 硬件支持
- **双板模式**: 支持单模块和双模块工作模式
- **多天线**: 支持8路天线阵列，可进行测向定位
- **GPIO控制**: 支持GPIO接口控制外部设备

### 软件架构
- **模块化设计**: 采用模块化架构，便于维护和扩展
- **跨平台**: 支持Windows和Linux平台
- **实时处理**: 基于回调机制的实时信号处理

### 检测能力
- **高精度**: 支持MHz级别的频率精度
- **高灵敏度**: 最大70dB增益，可检测微弱信号
- **快速扫描**: 可配置扫描时间，最快50ms扫描周期

## 支持的无人机类型

### DJI系列
- **OcuSync协议**: Mavic系列、Phantom 4系列、Inspire系列
- **LightBridge协议**: Phantom 4、Inspire 2等
- **WiFi协议**: Spark、Phantom 3、Tello、Mini系列
- **加密信号**: 支持DJI加密ID信号检测

### 其他品牌
- **道通**: Nano、Nano+、Lite、EVO2等系列
- **飞米**: X8SE等型号
- **Parrot**: 各型号无人机
- **PowerEgg**: 小巧系列
- **模拟图传**: 支持PAL/NTSC模拟视频信号

## 项目结构

```
drone_rx_demo/
├── main.cpp              # 主程序入口（单模块模式）
├── main2.cpp             # 主程序入口（双模块模式）
├── interface.h           # API接口定义
├── gps.cpp              # GPS功能模块
├── gpio_control.cpp     # GPIO控制模块
├── plat/                # 平台相关代码
│   ├── DroneDetect.h    # 无人机检测数据结构
│   ├── DroneDetect.cpp  # 无人机检测实现
│   ├── hl_caps_*.cpp    # CAPS系统各功能模块
│   └── client/          # 客户端相关代码
├── so/                  # 动态链接库
│   ├── libERadiodll.so  # 射频处理库
│   ├── libsignal.so     # 信号处理库
│   └── libwifidll.so    # WiFi处理库
├── build/               # 编译输出目录
└── CMakeLists.txt       # CMake构建配置
```

## 编译环境

- **CMake**: 版本4.0或更高
- **编译器**: 支持C++11标准的GCC或MSVC
- **操作系统**: Linux (推荐) 或 Windows
- **架构**: 支持x86_64和ARM64 (如NVIDIA Jetson)

## 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd drone_rx_demo
   ```

2. **编译项目**
   ```bash
   mkdir build && cd build
   cmake ..
   make
   ```

3. **运行程序**
   ```bash
   ./drone_rx_demo -ip *********** -gain 70 -longi 121.5134 -lati 31.3348
   ```

## 配置参数

- `-ip`: 设备IP地址 (默认: ***********)
- `-gain`: RF增益 (范围: 0-70dB)
- `-rate`: 采样率 (支持: 11.52M, 13.44M, 15.36MHz)
- `-freq`: 锁定频率 (0为扫描模式)
- `-ant`: 天线选择 (1-8)
- `-longi`: 本地经度
- `-lati`: 本地纬度
- `-t`: 扫描时间间隔 (ms)

## 输出格式

系统会生成以下输出：
- **控制台输出**: 实时检测结果
- **日志文件**: scan_result.log 详细检测记录
- **网络数据**: UDP格式的结构化数据

## 许可证

版权所有 © 北京RHHK设备有限公司

## 联系信息

如有技术问题或商务合作，请联系：
- 公司：北京RHHK设备有限公司
- 项目维护：zonghui
