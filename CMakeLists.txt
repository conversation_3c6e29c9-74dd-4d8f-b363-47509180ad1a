cmake_minimum_required(VERSION 4.0)

if (NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=c99 -O3 -DNDEBUG")
else ()
    set(CMAKE_BUILD_TYPE "Debug")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0 -Wall")
endif ()

#add_definitions(-DDUAL_BOARD)
#add_definitions(-DSINGLE_BOARD)
add_definitions(-DORIN_NANO_PWRIO_V1)
add_definitions(-DORIN_NANO_GPS_TTYTHS3)

set(CMAKE_EXE_LINKER_FLAGS "-static")
#include_directories($(CMAKE_CURRENT_SOURCE_DIR/))
#set (CMAKE_C_COMPILER "/usr/bin/aarch64-linux-gnu-gcc")
#set (CMAKE_CXX_COMPILER "/usr/bin/aarch64-linux-gnu-g++")
add_compile_options(-fpermissive)

project(drone_rx_demo)
add_executable(${PROJECT_NAME} main.cpp main2.cpp gps.cpp ./plat/DroneDetect.cpp gpio_control.cpp ./plat/hl_caps_adapt.cpp ./plat/hl_caps_config.cpp ./plat/hl_caps_report.cpp ./plat/hl_caps_manage.cpp ./plat/hl_caps_rdi.cpp ./plat/hl_caps_rssi.cpp)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=c99")

target_link_libraries(drone_rx_demo
        # /data/App/SH_Detection/drone_rx_demo/so/libERadiodll.so
        # /data/App/SH_Detection/drone_rx_demo/so/libsignal.so
        # /data/App/SH_Detection/drone_rx_demo/so/libwifidll.so
        # /usr/lib/aarch64-linux-gnu/libiio.so
        # /usr/lib/aarch64-linux-gnu/libad9361.so
)
