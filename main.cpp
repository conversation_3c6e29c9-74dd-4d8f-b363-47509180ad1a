#include "stdio.h"
#include "stdint.h"
#include "stdlib.h"
#include "interface.h"
#include "math.h"
#include "string.h"
#include "time.h"

#if !DUAL_BOARD
#ifdef _WIN32
#include <Windows.h>
#else
#include <unistd.h>
#endif

#pragma comment(lib, "ERadiodll.lib")
#pragma warning(disable:4996)

//int antenna_code[8] = {0b000,0b001,0b010,0b011,0b100,0b101,0b110,0b111};
int antenna_code[8] = {0b000, 0b100, 0b010, 0b110, 0b001, 0b101, 0b011, 0b111};

int sample_rate = 13.44e6 * 2, gain = 70; //


double gps_distance(double lon1, double lat1, double lon2, double lat2) {
    lat1 = lat1 * 3.14159265358979 / 180;
    lon1 = lon1 * 3.14159265358979 / 180;
    lat2 = lat2 * 3.14159265358979 / 180;
    lon2 = lon2 * 3.14159265358979 / 180;
    double vLon = fabs(lon1 - lon2);
    double vLat = fabs(lat1 - lat2);
    double h = sin(vLat / 2) * sin(vLat / 2) + cos(lat1) * cos(lat2) * sin(vLon / 2) * sin(vLon / 2);
    return 2.0 * 6371 * asin(sqrt(h));
}

void rssi_callback(float rssi_drone[], float rssi_max[], int64_t freq) {
    for (int i = 0; i < 8; i++) {
        printf("rssi: %.1f  %.1f\n", rssi_drone[i], rssi_max[i]);
    }


    //printf("rssi =%.1f,  freq=.1f, gpio=%x\n", rssi, freq / 16, gpio_value);
}

void spectrum_callback(float *spectrum, int64_t start_freq, int length, float resolution) {
    // printf("spectrum_callback  start_freq=%.2f  end_freq=%.2f\n", start_freq / 1e6, (start_freq+resolution*length) / 1e6);
    //FILE *fid = fopen("psd.dat", "ab");
    //fwrite(spectrum, 4, length, fid);
    //fclose(fid);
}


void iq_callback(int16_t *iq, int iq_num, int64_t freq) {
    //*iq : IQIQIQIQ......  iq_num is number of IQ pair
    printf("iq_callback  freq=%.1f  len=%d\n", freq / 1e6, iq_num);
}

//double local_longitude = 121.5134, local_latitude = 31.3348; // sh
double local_longitude = 112.97764, local_latitude = 28.249499; //cs
GpsData gps;
static uint8_t imag[200000];
void drone_callback(DJI_FLIGHT_INFO_Str *message) {
    time_t timep;
    struct tm *currentTime;
    time(&timep);
    currentTime = localtime(&timep); /*取得当地时间*/

    memset(&gps, 0, sizeof(GpsData));
    get_gps(&gps);

    printf("========1>>>GPS: %f/%f\n", gps.dLongitude, gps.dLatitude);
    printf("========1>>>state=%d\n", get_device_state());

    if (message->decode_flag) {
        static int count_msg = 0;
        printf("%d ", count_msg++);

        if (message->packet_type == PACKET_DJI_ENCYPT) //DJI  encrypted drone id message
        {
            uint64_t id = 0;
            for (int i = 0; i < 5; i++) {
                id <<= 8;
                id += message->drone_serial_num[i];
            }
            printf("Encypted Mavic_O4_ID=%llx is detected at freq=%.1f, rssi=%.1f\n", id, message->freq / 1e6,
                   message->rssi);
#if 1 //save log
FILE *fid = fopen("scan_result.log", "a");
fprintf(fid, "%d-%d-%d %d:%d:%d Encypted Mavic_O4_ID=%llx freq=%.1f, rssi=%.1f \n", currentTime->tm_year + 1900,
        currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec,
        id, message->freq / 1e6, message->rssi);
fclose (fid);
#endif

//#if _DEBUG
//				static clock_t last_t = 0;
//				double et = (double)(clock() - last_t) / CLOCKS_PER_SEC;
//				printf("time gap %.3f %.1f\n", et, (et/0.64));
//				last_t = clock();
//#endif

}
			else // DJI none encrypted drone id message
			{
				printf(" drone GPS:%f/%f  Height|Altitude %.1f|%.1f  East|Noth|Up Speed %.1f|%.1f|%.1f\n", message->drone_longitude, message->drone_latitude, message->height, message->altitude, message->east_speed, message->north_speed, message->up_speed);
				printf("home GPS:%f/%f  pilot GPS:%f/%f\n", message->home_longitude, message->home_latitude, message->pilot_longitude, message->pilot_latitude);
				printf("serial: %s, model(%d): %s   freq=%.1f, rssi=%d\n", message->drone_serial_num, message->product_type, message->product_type_str, message->freq / 1e6, (int)message->rssi);
				printf(" freq=%.1f, efingerprint=%lld  bw=%d\n", message->freq/1e6, message->efingerprint, (int)(message->bandwidth / 1e6));
				float distance = 0;
				if (message->drone_longitude != 0)
				{
					if (gps.dLongitude != 0) local_longitude = gps.dLongitude;
					if (gps.dLongitude != 0) local_latitude = gps.dLatitude;
					distance = gps_distance(local_longitude, local_latitude, message->drone_longitude, message->drone_latitude);
					printf("the drone is %.2f km from here\n", distance);
				}
#if 1 //save log
FILE *fid = fopen("scan_result.log", "a");
fprintf(fid, "%d-%d-%d %d:%d:%d  freq=%.1f, rssi=%.1f  distance=%.2f\n", currentTime->tm_year + 1900,
        currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec,
        message->freq / 1e6, message->rssi, distance);
fprintf(fid, " drone GPS:%f/%f  Height|Altitude %.1f|%.1f  East|Noth|Up Speed %.1f|%.1f|%.1f\n",
        message->drone_longitude, message->drone_latitude, message->height, message->altitude, message->east_speed,
        message->north_speed, message->up_speed);
fprintf(fid, "home GPS:%f/%f  pilot GPS:%f/%f\n", message->home_longitude, message->home_latitude,
        message->pilot_longitude, message->pilot_latitude);
fprintf(fid, "serial: %s, model(%d): %s \n\n", message->drone_serial_num, message->product_type,
        message->product_type_str);
fclose (fid);
#endif

}
		}
		else if (message->packet_type== PACKET_OPEN_DRONE_ID)
		{
			char msg[512];
			memset(msg, 0, sizeof(msg));
			sprintf(msg, "open drone id:%s,  freq=%d, rssi=%d\n", message->drone_id, (int)(message->freq/1e6), (int)message->rssi);
			sprintf(msg, "%s drone GPS:%f/%f  pilot:%f/%f speed|Vspeed|direc %.1f|%.1f|%d\n", msg, message->drone_longitude, message->drone_latitude, message->pilot_longitude, message->pilot_latitude
				, message->speed, message->up_speed, message->direction);
			sprintf(msg, "%s wifi ssid=%s MAC=%02x:%02x:%02x:%02x:%02x:%02x \n\n", msg, message->wifi_ssid,
				message->wifi_source_address[0], message->wifi_source_address[1], message->wifi_source_address[2], message->wifi_source_address[3], message->wifi_source_address[4], message->wifi_source_address[5]);
			printf("%s", msg);
		}
		else
		{
			char msg[512];
			memset(msg, 0, sizeof(msg));

			if (message->packet_type == PACKET_DJI_ID)        sprintf(msg,"DJI drone id is detected at freq=%.1f, rssi=%.1f\n", message->freq / 1e6, message->rssi);
			if (message->packet_type == PACKET_VEDIO_DJI) sprintf(msg, "DJI OcuSync vedio  is detected at freq=%.1f, efingerprint=%lld rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->efingerprint, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_LightBrdige_DJI)  sprintf(msg, "LightBrdige DJI P4 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_LightBrdige_OTHERS)  sprintf(msg, "LightBrdige Daotong_Evo2_v2/Feimi/Kuxin type  is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_DAOTONG || message->packet_type == PACKET_VEDIO_Yuneec_H52e)  sprintf(msg, "daotong type  is detected at freq=%.1f, efingerprint=%lld rssi=%.1f, bw=%.0fM\n", message->freq / 1e6, message->efingerprint, message->rssi, message->bandwidth / 1e6);
			if (message->packet_type == PACKET_VEDIO_WIFI_DJI_5M)  sprintf(msg, "DJI Mini/Mini SE/Air1 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_WIFI_DJI )  sprintf(msg, "DJI wifi is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_HARBOSEN)  sprintf(msg, "Harbosen  is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_ANALOG && message->bandwidth == 5e6)  sprintf(msg, "PAL Analog vedio (FPV/DIY/Toy) is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_ANALOG && message->bandwidth == 4.2e6)  sprintf(msg, "NTSC Analog vedio (FPV/DIY/Toy) is detected at freq=%.1f, rssi=%.1f, bw=%.1fM\n", message->freq / 1e6, message->rssi, (message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_WIFI_PARROT)  sprintf(msg, "Parrot is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_WIFI_POWEREGG)  sprintf(msg, "PowerEgg is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_WIFI_SJF11)  sprintf(msg, "SJ F11 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_ENHANCED_WIFI)  sprintf(msg, "enhanced wifi is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_DATALINK_P900)  sprintf(msg, "P900/P840 datalink is detected at freq=%.1f, rssi=%.1f, bw=100K\n", message->freq / 1e6, message->rssi);
			if (message->packet_type == PACKET_VEDIO_FEIMI)  sprintf(msg, "Feimi (X8SE) is detected at freq=%.1f, rssi=%.1f\n", message->freq / 1e6, message->rssi);
			if (message->packet_type == PACKET_VEDIO_Avatar)  sprintf(msg, "PACKET_VEDIO_Avatar is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_ZONGHENG)  sprintf(msg, "PACKET_VEDIO_ZONGHENG is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_DJI_M200)  sprintf(msg, "DJI M200 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_HAIKAN_WEISHI)  sprintf(msg, "Haikan Weishi drone is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_DJI_O4)  sprintf(msg, "DJI O4 vedio is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_LTE_TYPE)  sprintf(msg, "LTE type vedio is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
			if (message->packet_type == PACKET_VEDIO_LightBrdige)  sprintf(msg, "LightBrdige  is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));


			if (message->packet_type == PACKET_SCAN_ROUND_END)
			{
				//printf("scan round end\n;");
			}


			if (message->wifi_flag)
			{
				sprintf(msg, "%s wifi MAC=%02x:%02x:%02x:%02x:%02x:%02x  %02x:%02x:%02x:%02x:%02x:%02x SSID=%s,  freq=%.1f, rssi=%.1f\n", msg,
					message->wifi_source_address[0], message->wifi_source_address[1], message->wifi_source_address[2], message->wifi_source_address[3], message->wifi_source_address[4], message->wifi_source_address[5],
					message->wifi_destination_address[0], message->wifi_destination_address[1], message->wifi_destination_address[2], message->wifi_destination_address[3], message->wifi_destination_address[4], message->wifi_destination_address[5]
					, message->wifi_ssid, message->freq / 1e6, message->rssi);
			}
			if (message->packet_type == PACKET_VEDIO_ANALOG)
			{
				int row_pixel = 0, col_pixel=0;
				if (get_analog_image(imag, &row_pixel, &col_pixel))
				{
					//printf("analog imag is catch here %dx%d", row_pixel, col_pixel); // imag has row_pixel * col_pixel  pixel
					//FILE *fidi = fopen("imag.dat", "wb");
					//fwrite(imag, 1, row_pixel*col_pixel, fidi);
					//fclose(fidi);
				}
			}

			printf("%s", msg);

			if (strlen(msg) > 2)
			{
				struct tm* currentTime;
				time(&timep);
				currentTime = localtime(&timep);
				FILE* fid = fopen("scan_result.log", "a");
				fprintf(fid, "%d-%02d-%02d %02d:%02d:%02d  %s", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, msg);
				fclose(fid);
			}

		}
//PACKET_VEDIO_DJI_WIFI includes dji mini, mini se,dji mavic air1, dji spark, dji P3
//PACKET_VEDIO_DAOTONG includes daotong nano, daotong nana+, daotong lite, daotong lite, daotong ev2_v3,
//PACKET_VEDIO_LightBrdige includes daotong ev2_v2, dji P4,  feimi,
}


#ifdef _WIN32
bool ctrlexit(DWORD fd) {
    fprintf(stdout, "Exit\n");
    stop_device();
    exit(0);
}
#endif

int main(int argc, char *argv[]) {

#ifdef _WIN32
SetConsoleCtrlHandler ((PHANDLER_ROUTINE)ctrlexit, true);
#endif

int64_t freq = 0;
char ip[20];
int antenna = 1, scan_time = 50;
memset(ip, 0, sizeof(ip));

	for (int j = 1; j<argc; j++)
	{
		if (!strcmp(argv[j], "-ip"))
		{
			j++;
			memcpy(ip, argv[j], strlen(argv[j]));
		}
		if (!strcmp(argv[j], "-gain"))
		{
			gain = atoi(argv[++j]);
		}
		if (!strcmp(argv[j], "-rate"))
		{
			double tmp = atof(argv[++j]);
			if (tmp < 10000) sample_rate = tmp*1e6; else sample_rate = tmp;
		}
		if (!strcmp(argv[j], "-freq"))
		{
			double tmp = atof(argv[++j]);
			if (tmp < 10000) freq = tmp*1e6; else freq = tmp;
		}
		if (!strcmp(argv[j], "-ant"))
		{
			antenna = atoi(argv[++j]);
		}
		if (!strcmp(argv[j], "-longi"))
		{
			local_longitude = atof(argv[++j]);
		}
		if (!strcmp(argv[j], "-lati"))
		{
			local_latitude = atof(argv[++j]);
		}
		if (!strcmp(argv[j], "-t"))
		{
			scan_time = atoi(argv[++j]);
		}
	}

	if (strlen(ip)>0)
	{
		config_ip(ip);
	}
	else
	{
		config_ip("***********");//configure ip here
	}

int gps_open_flag = start_gps("***********", "************"); // device ip and computer ip


//int device_num = GetDeviceNum(); // open device,
int device_num = OpenDevice(1); // open device,
	if (device_num== -1)
	{
		getchar(); return 0;
	}
	else {
		printf("device_num =%d  is connected\n", device_num);
	}

enable_gpio (0);


set_scan_time (scan_time); //unit is ms

//推荐使用start_drone_scan2函数，可以通过回调函数返回实时频谱
// 第一个频率参数为0是扫描，频率若不为0是锁定频率，用于调试


start_drone_scan2(freq, gain, sample_rate, drone_callback, spectrum_callback); //

// rssi_measure(2444e6, 40, 200, 15.36e6, antenna_code, rssi_callback, drone_callback);


memset (&gps, 0, sizeof(GpsData));


//resolution must >=5K
//start_spectrum_scan(70e6, 6000e6, 25e3, 50, spectrum_callback);

//startIQ(iq_callback);
//set_rf_gain(30);


float spectrum[16384];
int64_t spectrum_center_freq = 0;
memset(spectrum, 0, sizeof(spectrum));

	while (1)
	{
		get_gps(&gps);
//if(gps.dLongitude!=0) printf("GPS: %f/%f\n", gps.dLongitude, gps.dLatitude);
//printf("state=%d\n", get_device_state());


#ifdef _WIN32
Sleep (1000);
#else
sleep (1000);
#endif

}
	return 0;
}
#endif
