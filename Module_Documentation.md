# 模块功能文档

## 概述

本文档详细描述了无人机检测系统各个功能模块的设计和实现，包括GPS模块、无人机检测模块、信号处理模块等核心组件。

## 1. GPS模块 (gps.cpp)

### 功能概述
GPS模块负责获取设备的地理位置信息，支持通过UDP网络协议与GPS设备通信，为无人机距离计算提供基础数据。

### 主要功能
- **GPS数据接收**: 通过UDP socket接收GPS设备发送的位置数据
- **数据解析**: 解析GPS数据包，提取经纬度、高度和时间信息
- **跨平台支持**: 支持Windows和Linux平台的socket通信
- **非阻塞通信**: 使用非阻塞socket确保实时性

### 核心函数

#### start_gps()
```c
int start_gps(char *ip_remote, char *ip_local)
```
**功能**: 初始化GPS通信
- 创建UDP socket
- 绑定本地地址和端口 (9026)
- 连接远程GPS设备 (端口9023)
- 设置非阻塞模式
- 发送初始化消息

#### get_gps()
```c
int get_gps(GpsData *gps)
```
**功能**: 获取GPS数据
- 从UDP socket接收GPS数据包
- 解析UTC时间信息
- 提取经纬度和高度数据
- 更新GpsData结构体

### 数据格式
GPS数据包格式示例：
```
utc = Fri Aug 18 08:07:30 2023
lat = 121.513424
lon = 31.334731
alt = 23.800000
```

### 配置参数
- **本地端口**: 9026 (接收)
- **远程端口**: 9023 (发送)
- **默认IP**: *********** (设备) / ************ (主机)

## 2. 无人机检测模块 (DroneDetect.h/cpp)

### 功能概述
无人机检测模块是系统的核心，负责识别和分析各种类型的无人机信号，提取飞行参数和设备信息。

### 支持的无人机类型

#### DJI系列
- **OcuSync协议**: Mavic系列、Phantom 4系列、Inspire系列
- **LightBridge协议**: Phantom 4、Inspire 2等专业级无人机
- **WiFi协议**: Spark、Phantom 3、Tello、Mini系列
- **加密信号**: 支持DJI最新加密ID信号检测

#### 其他品牌
- **道通 (Autel)**: Nano、Nano+、Lite、EVO2等全系列
- **飞米 (FIMI)**: X8SE等消费级无人机
- **Parrot**: 各型号消费级无人机
- **PowerEgg**: 小巧系列无人机
- **模拟图传**: PAL/NTSC模拟视频信号

### 检测能力
- **频率范围**: 70MHz - 6GHz
- **信号类型**: 数字调制、模拟调制、WiFi、增强WiFi
- **带宽识别**: 自动识别5MHz、10MHz、20MHz等带宽
- **协议解析**: 深度解析各厂商私有协议

### 数据结构

#### 统一检测结果 (T_DroneInfo)
使用联合体设计，支持不同类型无人机的特定数据结构：
- `T_DecodeDjiEncypt`: DJI加密信号
- `T_DecodeDjiNoneEncypt`: DJI非加密信号
- `T_PacketOpenDroneID`: Open Drone ID标准
- `T_Packet_VedioDJIDrone`: DJI视频信号
- 其他各品牌特定结构体

### 核心算法
- **信号指纹识别**: 基于电子指纹的无人机型号识别
- **协议解析**: 多层协议栈解析
- **CRC校验**: 确保数据完整性
- **实时处理**: 毫秒级信号检测和解析

## 3. 信号处理模块

### 功能概述
信号处理模块负责射频信号的采集、处理和分析，是无人机检测的基础。

### 主要组件

#### 射频前端
- **频率范围**: 70MHz - 6GHz
- **增益控制**: 0-70dB可调增益
- **采样率**: 支持11.52MHz、13.44MHz、15.36MHz
- **多天线**: 支持8路天线阵列

#### 信号处理算法
- **频谱分析**: FFT频谱分析
- **信号检测**: 基于能量检测和特征匹配
- **调制识别**: 自动识别调制方式
- **带宽估计**: 自动估计信号带宽

#### RSSI测量
- **多天线RSSI**: 8路天线同时测量
- **测向功能**: 基于RSSI差值的粗略测向
- **动态范围**: 大动态范围RSSI测量

### 核心函数

#### start_drone_scan2()
主要的无人机扫描函数：
- 配置射频参数
- 启动信号采集
- 注册回调函数
- 开始实时处理

#### spectrum_callback()
频谱数据回调：
- 实时频谱数据
- 频率分辨率配置
- 频谱显示和分析

#### rssi_callback()
RSSI数据回调：
- 8路天线RSSI值
- 最大RSSI记录
- 测向数据处理

## 4. GPIO控制模块 (gpio_control.cpp)

### 功能概述
GPIO控制模块提供硬件接口控制功能，支持外部设备的控制和状态指示。

### 主要功能
- **GPIO输出控制**: 控制外部继电器、LED等设备
- **状态指示**: 通过GPIO输出系统状态
- **硬件接口**: 与外部硬件设备通信

### 应用场景
- **天线切换**: 控制天线切换矩阵
- **状态指示**: LED状态指示灯控制
- **外部设备**: 控制报警器、继电器等

## 5. 网络通信模块

### 功能概述
网络通信模块负责系统的网络数据传输，包括GPS数据接收、检测结果上报等。

### 通信协议
- **UDP协议**: 主要使用UDP进行实时数据传输
- **JSON格式**: 结构化数据使用JSON格式
- **二进制协议**: 高速数据使用二进制协议

### 数据流向
```
GPS设备 ----UDP----> 检测系统 ----UDP----> 上位机
         (位置数据)              (检测结果)
```

## 6. 配置管理模块 (hl_caps_config.h/cpp)

### 功能概述
配置管理模块负责系统参数的配置、存储和管理。

### 配置项目
- **射频参数**: 频率、增益、采样率
- **扫描参数**: 扫描时间、频率列表
- **网络参数**: IP地址、端口配置
- **检测参数**: 检测阈值、过滤条件

### 配置文件
- **freq_scan.txt**: 频率扫描配置
- **配置结构体**: 内存中的配置管理
- **动态配置**: 运行时参数调整

## 7. 数据报告模块 (hl_caps_report.h/cpp)

### 功能概述
数据报告模块负责检测结果的格式化、存储和传输。

### 报告格式
- **实时日志**: 控制台实时输出
- **文件日志**: scan_result.log详细记录
- **网络数据**: UDP结构化数据传输
- **JSON格式**: 标准化数据交换格式

### 数据内容
- **检测时间**: 精确到秒的时间戳
- **无人机信息**: 型号、位置、速度等
- **信号参数**: 频率、RSSI、带宽等
- **距离计算**: 基于GPS的距离信息

## 8. 适配层模块 (hl_caps_adapt.h/cpp)

### 功能概述
适配层模块提供统一的接口适配，支持不同硬件平台和软件版本。

### 主要功能
- **平台适配**: Windows/Linux平台适配
- **硬件抽象**: 不同硬件版本的统一接口
- **API封装**: 底层API的高级封装
- **兼容性**: 向后兼容性支持

### 设计模式
- **适配器模式**: 统一不同接口
- **工厂模式**: 创建平台相关对象
- **策略模式**: 不同平台的处理策略

## 9. 系统集成

### 模块间通信
```
主程序 (main.cpp/main2.cpp)
    ├── GPS模块 (gps.cpp)
    ├── 检测模块 (DroneDetect.cpp)
    ├── 信号处理 (射频库)
    ├── GPIO控制 (gpio_control.cpp)
    ├── 配置管理 (hl_caps_config.cpp)
    ├── 数据报告 (hl_caps_report.cpp)
    └── 适配层 (hl_caps_adapt.cpp)
```

### 数据流
1. **初始化阶段**: 配置加载 → 设备初始化 → GPS启动
2. **检测阶段**: 信号采集 → 信号处理 → 特征识别 → 结果输出
3. **报告阶段**: 数据格式化 → 日志记录 → 网络传输

### 线程模型
- **主线程**: 系统控制和用户交互
- **检测线程**: 实时信号检测和处理
- **GPS线程**: GPS数据接收和处理
- **网络线程**: 网络数据传输

## 10. 性能优化

### 实时性优化
- **零拷贝**: 减少数据拷贝操作
- **内存池**: 预分配内存减少动态分配
- **无锁队列**: 线程间高效数据传输
- **SIMD优化**: 向量化信号处理算法

### 资源管理
- **内存管理**: 智能指针和RAII
- **文件句柄**: 及时释放系统资源
- **网络连接**: 连接池和重用机制
- **CPU调度**: 合理的线程优先级设置
