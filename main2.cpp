/*******************************************************************************
* COPYRIGHT Beijing RHHK Equipment CO.,LTD
********************************************************************************
* 文件名称:  main2.cpp
* 功能描述:  CAPS内部数据结构定义、变量函数声明
* 版    本:
* 编写日期:  2024/10/26
* 说    明:
* 修改历史:
* 修改日期    修改人  BugID/CRID      修改内容
* ------------------------------------------------------------------------------
* 2024/10/25  zonghui    创建文件
*******************************************************************************/
#include "stdio.h"
#include "stdint.h"
#include "stdlib.h"
#include "interface.h"
#include "math.h"
#include "string.h"
#include "time.h"
#include "./plat/hl_caps_rssi.h"
#include "./plat/hl_caps_report.h"


#if DUAL_BOARD

#ifdef _WIN32
#include <Windows.h>
#else
#include <unistd.h>
#endif

#pragma comment(lib, "ERadiodll.lib")
#pragma warning(disable:4996)
extern int Set_LocalTime(void);
extern void CreatRdiPthread(void);

extern int detect_sockfd;
extern int new_socket;

extern struct sockaddr_in detect_server_addr;
extern void SetDroneDectectData(DJI_FLIGHT_INFO_Str *message, Drone_Detect_GpsInfo_Rpt *pstruGpsInfoRpt);
extern int Create_Socket(char *ip_remote, char *ip_local);
extern void CreateGatherGpsPthread(void);
extern void CreatePowerControl(void);
extern void CreatLocalGpsPthread(void);
extern int Create_JasonDataSend(void);
extern int Create_ClinetAcception(void);
extern void Create_NavigationDeception(void);
extern void  Dbg_Level_Print(u32 print_level, char *format, ...);
extern void InitDroneQueue(void);

char *ip_remote = "***********0";
char *ip_local =  "***********01";

//int antenna_code[8] = {0b000,0b001,0b010,0b011,0b100,0b101,0b110,0b111};
int antenna_code[8] = { 0b000, 0b100,0b010, 0b110,0b001,0b101,0b011,0b111 };

int sample_rate = 13.44e6 * 2, gain = 60; //
int gps_init_status = 0;
T_RhhkMangerConfArg OutConfInfo;
/*******************************************************************************
* 函数名称: gps_distance
* 函数功能: 根据经纬度计算距离
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* lon1              double      输入           经度1
* lat1              double      输入           纬度1
* lon2              double      输入           经度2
* lat2              double      输入           纬度2
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
double gps_distance(double lon1, double lat1, double lon2, double lat2)
{
	lat1 = lat1 * 3.14159265358979 / 180;
	lon1 = lon1 * 3.14159265358979 / 180;
	lat2 = lat2 * 3.14159265358979 / 180;
	lon2 = lon2 * 3.14159265358979 / 180;
	double vLon = fabs(lon1 - lon2);
	double vLat = fabs(lat1 - lat2);
	double h = sin(vLat / 2)*sin(vLat / 2) + cos(lat1)*cos(lat2)*sin(vLon / 2)*sin(vLon / 2);
	return 2.0 * 6371 * asin(sqrt(h));
}

int  Count_exit = 0;


void rssi_drone_callback(DJI_FLIGHT_INFO_Str *message)
{

}
/*******************************************************************************
* 函数名称: rssi_callback
* 函数功能: 库回调函数，接收探测信息(暂时弃用)
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* rssi_drone        float      输入           rssi当前值
* rssi_max          float      输入           rssi最大值
* freq              int64      输入           频点
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void rssi_callback(float rssi_drone[], float rssi_max[], int64_t freq)
{
    T_RhhkGatherRssiInfo  strGatherRssiInfo;
    memset(&strGatherRssiInfo, 0, sizeof(T_RhhkGatherRssiInfo));
	for (int i = 0; i < 8; i++)
	{
		printf("rssi: %.1f  %.1f\n", rssi_drone[i], rssi_max[i]);
		strGatherRssiInfo.rssi_drone[i] = rssi_drone[i];
		strGatherRssiInfo.rssi_max[i] = rssi_max[i];
	}
	strGatherRssiInfo.freq = freq;
	strGatherRssiInfo.data_valid = 1;
	Handle_RfRssiDataToPacket(&strGatherRssiInfo);

	Count_exit++;
	
	//printf("rssi =%.1f,  freq=.1f, gpio=%x\n", rssi, freq / 16, gpio_value);
}
void spectrum_callback(float *spectrum, int64_t start_freq, int length, float resolution)
{
	//printf("spectrum_callback  freq=%.3f  len=%d\n", start_freq / 1e6, length);

	//FILE *fid = fopen("psd.dat", "wb");
	//fwrite(spectrum, 4, length, fid);
	//fclose(fid);
}

void iq_callback(int16_t *iq, int iq_num, int64_t freq)
{
	//*iq : IQIQIQIQ......  iq_num is number of IQ pair
	Dbg_Level_Print(LOG_INFO, "iq_callback  freq=%.1f  len=%d\n", freq / 1e6, iq_num);
}
/*******************************************************************************
* 函数名称: gps_init
* 函数功能: 模块初始化(暂时弃用)
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* void
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void gps_init(void)
{
    int antenna = 1,scan_time=20;
    int device_num2 = 0;
    //int device_num2 = config_second_device("***********", "***********8", 0, 70);
    if (device_num2 <= 0)
    {
        printf("RHHK:the second device is not found, can not work\n");
    }

    //config_ip("***********");
    //int gps_open_flag =  start_gps("***********", "***********8"); // device ip and computer ip

    int device_num = 0; 
    //int device_num = OpenDevice(antenna); // open device, 
    if (device_num == -1)
    {
       //getchar(); return 0;
    }
    else 
    {
        Dbg_Level_Print(LOG_INFO, "device_num1 =%d  is connected\n", device_num);
    }
    return 0;
}

double local_longitude = 121.5134, local_latitude = 31.3348; // sh
GpsData gps;
GpsData his_gps;
int count = 0;
static uint8_t imag[200000];

/*******************************************************************************
* 函数名称: drone_callback
* 函数功能: 探测主回调函数(暂时弃用)
* 函数参数: 
* 参数名称: 		类型		输入/输出	   描述
* void
* 返回值: 无
* 函数说明:
*
* 修改日期	  版本号   修改人  修改内容（局限在本函数内的缺陷修改需要写在此处）
* -----------------------------------------------------------------
*******************************************************************************/
void drone_callback(DJI_FLIGHT_INFO_Str *message)
{  
	time_t timep;
	struct tm *currentTime;
        Drone_Detect_GpsInfo_Rpt struGpsInfoRpt;
	time(&timep);
	currentTime = localtime(&timep); /*取得当地时间*/

    if (gps_init_status == 0)
	{	
        gps_init();
	    gps_init_status = 1;
	    Dbg_Level_Print(LOG_INFO, "Gps init successful!\n");
	}	    

    //printf("====zongh====>product_type(mode):%d\n", message->product_type);
    //printf("====zongh====>product_type_str(type):%s\n", message->product_type_str);

    struGpsInfoRpt.local_longitude = 0;
	struGpsInfoRpt.local_latitude = 0;
    struGpsInfoRpt.local_altitude = 0;
	memset(&gps, 0, sizeof(GpsData));
	memset(&struGpsInfoRpt, 0, sizeof(Drone_Detect_GpsInfo_Rpt));
    get_gps(&gps);
	
	if(gps.dLongitude!=0) 
	{
        struGpsInfoRpt.local_longitude = gps.dLongitude;
	    struGpsInfoRpt.local_latitude = gps.dLatitude;
        struGpsInfoRpt.local_altitude = gps.dAltitude;
        his_gps.dLongitude = gps.dLongitude;
	    his_gps.dLatitude = gps.dLatitude;
        his_gps.dAltitude = gps.dAltitude;
        //printf("==Dji None Encryped==21>>>GPS: %f/%f\n", gps.dLongitude, gps.dLatitude);
        //printf("==Dji None Encryped==21>>>GPS: %f/%f/%f\n", struGpsInfoRpt.local_longitude, struGpsInfoRpt.local_latitude, struGpsInfoRpt.local_altitude);
        //printf("==Dji None Encryped==21>>>state=%d\n", get_device_state());
    }
	else
	{
        struGpsInfoRpt.local_longitude = his_gps.dLongitude;
	    struGpsInfoRpt.local_latitude = his_gps.dLatitude;
        struGpsInfoRpt.local_altitude = his_gps.dAltitude;
        //printf("========20>>>GPS: %f/%f/%f\n", struGpsInfoRpt.local_longitude, struGpsInfoRpt.local_latitude, struGpsInfoRpt.local_altitude);
        //printf("========20>>>state=%d\n", get_device_state());
	}
 
	SetDroneDectectData((DJI_FLIGHT_INFO_Str *)message, (Drone_Detect_GpsInfo_Rpt *)&struGpsInfoRpt);

	if (message->decode_flag)
	{
		static int count_msg = 0; printf("%d #%d  ", count_msg++, message->device_num);

		if (message->packet_type == PACKET_DJI_ENCYPT) //DJI  encrypted drone id message
		{
			uint64_t  id = 0;
			for (int i = 0; i < 6; i++)
			{
				id <<= 8; id += message->drone_serial_num[i];
			}
			Dbg_Level_Print(LOG_INFO, "Encypted Mavic_O4_ID=%lx is detected at freq=%.1f, rssi=%.1f\n", id, message->freq / 1e6, message->rssi);
			for (int i = 0; i < 176; i++) printf("%02x", message->dji_byte176[i]); printf("\n");

#if 1 //save log	 
			FILE *fid = fopen("scan_result.log", "a");
			fprintf(fid, "%d-%d-%d %d:%d:%d Encypted Mavic_O4_ID=%lx freq=%.1f, rssi=%.1f \n", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, id, message->freq / 1e6, message->rssi);
			fclose(fid);
#endif 
		}
		else // DJI none encrypted drone id message
		{

			Dbg_Level_Print(LOG_INFO, " drone GPS:%f/%f  Height|Altitude %.1f|%.1f  East|Noth|Up Speed %.1f|%.1f|%.1f\n", message->drone_longitude, message->drone_latitude, message->height, message->altitude, message->east_speed, message->north_speed, message->up_speed);
			Dbg_Level_Print(LOG_INFO, "home GPS:%f/%f  pilot GPS:%f/%f\n", message->home_longitude, message->home_latitude, message->pilot_longitude, message->pilot_latitude);
			Dbg_Level_Print(LOG_INFO, "serial: %s, model(%d): %s   freq=%.1f, rssi=%d\n", message->drone_serial_num, message->product_type, message->product_type_str, message->freq / 1e6, (int)message->rssi);
			Dbg_Level_Print(LOG_INFO, " freq=%.1f, efingerprint=%ld  bw=%d\n", message->freq / 1e6, message->efingerprint, (int)(message->bandwidth / 1e6));

			float distance = 0;
			if (message->drone_longitude != 0)
			{
				if (gps.dLongitude != 0) local_longitude = gps.dLongitude;
				if (gps.dLongitude != 0) local_latitude = gps.dLatitude;
				distance = gps_distance(local_longitude, local_latitude, message->drone_longitude, message->drone_latitude);
				printf("the drone is %.2f km from here\n", distance);
			}
			printf("antenna code =%d \n", message->antenna_code);
#if 1 //save log	 
			FILE *fid = fopen("scan_result.log", "a");
			fprintf(fid, "%d-%d-%d %d:%d:%d  freq=%.1f, rssi=%.1f  distance=%.2f\n", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, message->freq / 1e6, message->rssi, distance);
			fprintf(fid, " drone GPS:%f/%f  Height|Altitude %.1f|%.1f  East|Noth|Up Speed %.1f|%.1f|%.1f\n", message->drone_longitude, message->drone_latitude, message->height, message->altitude, message->east_speed, message->north_speed, message->up_speed);
			fprintf(fid, "home GPS:%f/%f  pilot GPS:%f/%f\n", message->home_longitude, message->home_latitude, message->pilot_longitude, message->pilot_latitude);
			fprintf(fid, "serial: %s, model(%d): %s \n\n", message->drone_serial_num, message->product_type, message->product_type_str);
			fclose(fid);
#endif 

		}
	}else if (message->packet_type == PACKET_OPEN_DRONE_ID)
	{
		char msg[512];
		memset(msg, 0, sizeof(msg));
		sprintf(msg, "open drone id:%s,  freq=%d, rssi=%d\n", message->drone_id, (int)(message->freq / 1e6), (int)message->rssi);
		sprintf(msg, "%s drone GPS:%f/%f  pilot:%f/%f speed|Vspeed|direc %.1f|%.1f|%d\n", msg, message->drone_longitude, message->drone_latitude, message->pilot_longitude, message->pilot_latitude, message->speed, message->up_speed, message->direction);
		sprintf(msg, "%s wifi ssid=%s MAC=%02x:%02x:%02x:%02x:%02x:%02x \n\n", msg, message->wifi_ssid,message->wifi_source_address[0], message->wifi_source_address[1], message->wifi_source_address[2], message->wifi_source_address[3], message->wifi_source_address[4], message->wifi_source_address[5]);
		Dbg_Level_Print(LOG_INFO, "%s", msg);
	}
	else
	{
		char msg[512];
		memset(msg, 0, sizeof(msg));
		if (message->packet_type == PACKET_DJI_ID)        sprintf(msg, "DJI drone id is detected at freq=%.1f, rssi=%.1f\n", message->freq / 1e6, message->rssi);
		if (message->packet_type == PACKET_VEDIO_DJI) sprintf(msg, "DJI OcuSync vedio  is detected at freq=%.1f, efingerprint=%ld rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->efingerprint, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_LightBrdige_DJI)  sprintf(msg, "LightBrdige DJI P4 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_LightBrdige_OTHERS)  sprintf(msg, "LightBrdige Daotong_Evo2_v2/Feimi/Kuxin type  is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_DAOTONG || message->packet_type == PACKET_VEDIO_Yuneec_H52e)  sprintf(msg, "daotong type  is detected at freq=%.1f, efingerprint=%ld rssi=%.1f, bw=%.0fM\n", message->freq / 1e6, message->efingerprint, message->rssi, message->bandwidth / 1e6);
		//if (message->packet_type == PACKET_VEDIO_Yuneec_H52e)  sprintf(msg, "Yuneec_H52e is detected at freq=%.1f, efingerprint=%d rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->efingerprint, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_WIFI_DJI_5M)  sprintf(msg, "DJI Mini/Mini SE/Air1 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_WIFI_DJI)  sprintf(msg, "DJI wifi is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_HARBOSEN)  sprintf(msg, "Harbosen  is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_ANALOG && message->bandwidth == 5e6)  sprintf(msg, "PAL Analog vedio (FPV/DIY/Toy) is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_ANALOG && message->bandwidth == 4.2e6)  sprintf(msg, "NTSC Analog vedio (FPV/DIY/Toy) is detected at freq=%.1f, rssi=%.1f, bw=%.1fM\n", message->freq / 1e6, message->rssi, (message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_WIFI_PARROT)  sprintf(msg, "Parrot is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_WIFI_POWEREGG)  sprintf(msg, "PowerEgg is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_WIFI_SJF11)  sprintf(msg, "SJ F11 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_ENHANCED_WIFI)  sprintf(msg, "enhanced wifi is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_DATALINK_P900)  sprintf(msg, "P900/P840 datalink is detected at freq=%.1f, rssi=%.1f, bw=100K\n", message->freq / 1e6, message->rssi);
		if (message->packet_type == PACKET_VEDIO_FEIMI)  sprintf(msg, "Feimi (X8SE) is detected at freq=%.1f, rssi=%.1f\n", message->freq / 1e6, message->rssi);
		if (message->packet_type == PACKET_VEDIO_Avatar)  sprintf(msg, "PACKET_VEDIO_Avatar is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_ZONGHENG)  sprintf(msg, "PACKET_VEDIO_ZONGHENG is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_DJI_M200)  sprintf(msg, "DJI M200 is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_HAIKAN_WEISHI)  sprintf(msg, "Haikan Weishi drone is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_DJI_O4)  sprintf(msg, "DJI O4 vedio is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_LTE_TYPE)  sprintf(msg, "LTE type vedio is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));
		if (message->packet_type == PACKET_VEDIO_LightBridge)  sprintf(msg, "LightBrdige  is detected at freq=%.1f, rssi=%.1f, bw=%dM\n", message->freq / 1e6, message->rssi, (int)(message->bandwidth / 1e6));

		if (message->packet_type == PACKET_HEART_BEAT_2)  sprintf(msg, "UDP Device Heart Beat\n");		
		if (message->packet_type == PACKET_SCAN_ROUND_END)
		{
			static clock_t t1 = clock();
			Dbg_Level_Print(LOG_INFO, "scan round end  %.1fs\n;", (double)(clock()-t1)/CLOCKS_PER_SEC);
			t1 = clock();
		}
		if (message->wifi_flag)
		{
			sprintf(msg, "%s wifi MAC=%02x:%02x:%02x:%02x:%02x:%02x  %02x:%02x:%02x:%02x:%02x:%02x SSID=%s,  freq=%.1f, rssi=%.1f\n", msg,message->wifi_source_address[0], message->wifi_source_address[1], message->wifi_source_address[2], message->wifi_source_address[3], message->wifi_source_address[4], message->wifi_source_address[5],message->wifi_destination_address[0], message->wifi_destination_address[1], message->wifi_destination_address[2], message->wifi_destination_address[3], message->wifi_destination_address[4], message->wifi_destination_address[5], message->wifi_ssid, message->freq / 1e6, message->rssi);
		}

		if (message->packet_type == PACKET_VEDIO_ANALOG)
		{
			int row_pixel = 0, col_pixel = 0;
		        //if (get_analog_image(imag, &row_pixel, &col_pixel))
			{
				Dbg_Level_Print(LOG_INFO, "analog imag is catch here %dx%d", row_pixel, col_pixel); // imag has row_pixel * col_pixel  pixel
				//FILE *fidi = fopen("imag.dat", "wb");
				//fwrite(imag, 1, row_pixel*col_pixel, fidi);
				// fclose(fidi);
			}
		}

		Dbg_Level_Print(LOG_INFO, "%s", msg);
		if (strlen(msg) > 2)
		{
			struct tm* currentTime;
			time(&timep);
			currentTime = localtime(&timep);
			FILE* fid = fopen("scan_result.log", "a");
			fprintf(fid, "%d-%02d-%02d %02d:%02d:%02d  %s", currentTime->tm_year + 1900, currentTime->tm_mon + 1, currentTime->tm_mday, currentTime->tm_hour, currentTime->tm_min, currentTime->tm_sec, msg);
			fclose(fid);
		}
	}
	//PACKET_VEDIO_DJI_WIFI includes dji mini, mini se,dji mavic air1, dji spark, dji P3
	//PACKET_VEDIO_DAOTONG includes daotong nano, daotong nana+, daotong lite, daotong lite, daotong ev2_v3,
	//PACKET_VEDIO_LightBrdige includes daotong ev2_v2, dji P4,  feimi,
}



#ifdef _WIN32
bool ctrlexit(DWORD fd) {
	fprintf(stdout, "Exit\n");
	stop_device();
	exit(0);
}
#endif

int main(int argc, char* argv[])
{
#ifdef _WIN32
	SetConsoleCtrlHandler((PHANDLER_ROUTINE)ctrlexit, true);
#endif

	int64_t freq = 0;
	char ip[20], ip2[20];
	int antenna = 1,scan_time=20;
	memset(ip, 0, sizeof(ip));

	for (int j = 1; j < argc; j++)
	{
		if (!strcmp(argv[j], "-ip"))
		{
			j++;
			memcpy(ip, argv[j], strlen(argv[j]));
		}
		if (!strcmp(argv[j], "-ip2"))
		{
			j++;
			memcpy(ip2, argv[j], strlen(argv[j]));
		}
		if (!strcmp(argv[j], "-gain"))
		{
			gain = atoi(argv[++j]);
		}
		if (!strcmp(argv[j], "-rate"))
		{
			double tmp = atof(argv[++j]);
			if (tmp < 10000) sample_rate = tmp * 1e6; else sample_rate = tmp;
		}
		if (!strcmp(argv[j], "-freq"))
		{
			double tmp = atof(argv[++j]);
			if (tmp < 10000) freq = tmp*1e6; else freq = tmp;
		}
		if (!strcmp(argv[j], "-ant"))
		{
			antenna = atoi(argv[++j]);
		}
		if (!strcmp(argv[j], "-longi"))
		{
			local_longitude = atof(argv[++j]);
		}
		if (!strcmp(argv[j], "-lati"))
		{
			local_latitude = atof(argv[++j]);
		}
		if (!strcmp(argv[j], "-t"))
		{
			scan_time = atoi(argv[++j]);
		}
	}
#if 0
	if (strlen(ip) > 0)
	{
		config_ip(ip);
	}
	else
	{
		int device_num2 = config_second_device("***********", "***********8", 0, 70);//2nd device, configure ip here

	 if (device_num2 <= 0)
	 {
		 printf("second device is not found, can not work\n");
	 }

		config_ip("***********");//configure the fist device here
	}
	int gps_open_flag =  start_gps("***********", "***********8"); // device ip and computer ip

	int device_num = OpenDevice(antenna); // open device, 
	if (device_num == -1)
	{
		//getchar(); return 0;
	}
	else {
		printf("device_num1 =%d  is connected\n", device_num);

	}
#endif 
	int64_t freq_scan_list[500], num = 0;
	memset(freq_scan_list, 0, sizeof(freq_scan_list));
	freq_scan_list[99] = 8;
	//freq = 2433e6;
	if (freq != 0) // freqquency lock 
	{
		freq_scan_list[0] = freq;
		freq_scan_list[1] = 0;
		num = 1;
	}

#if 0 //freqquency lock rssi measurement:
	freq_scan_list[0] = 2459e6; //freq
	freq_scan_list[1] = 100; // meassure time in ms
	freq_scan_list[2] = 0xff; // GPIO value related to antenna selection
	sample_rate = 26.88e6;
	num = 1;
#endif

	//enable_gpio(1);

	//set_gpio(0xff); 
	//set_gpio2(0xff); 

	//set_scan_time(scan_time); //unit is ms
	//第二个参数指示频点个数，为0是默认按动态库中扫描范围扫描，不为0时，按照freq_scan_list配置的频点扫描
        //start_drone_scan3(freq_scan_list, num, gain, sample_rate, drone_callback, spectrum_callback);

	//rssi_measure(5058e6, 60, 500, 15.36e6, antenna_code, rssi_callback, drone_callback);
	//rssi_measure(5058e6, 60, 500, 15.36e6, antenna_code, rssi_callback, rssi_drone_callback);
 
	//startIQ(iq_callback);
  usleep(2000000);
	memset(&gps, 0, sizeof(GpsData));
	//Set_LocalTime();
	InitDroneQueue();
 
  char pActualBrd_1[] = "B1";
  T_RhhkMangerConfArg OutConfInfo;
	memset(&OutConfInfo,0,sizeof(T_RhhkMangerConfArg));
  if(0 == RHHK_GetConfigStructArg((T_RhhkMangerConfArg *)&OutConfInfo))
  {
		 Dbg_Level_Print(LOG_INFO, "PROCESS START AND GET CONFIGURATION INFO!\n");
  }	 
 
  const char *log_path = "/home/<USER>/log";
  Rhhk_JudgeDirExistAndCreate(log_path);
 
	float spectrum[16384];
	int64_t spectrum_center_freq = 0;
	memset(spectrum, 0, sizeof(spectrum));

	CreatLocalGpsPthread();
	Dbg_Level_Print(LOG_INFO, "0$$$$$$$$$$$$$$$$$$$START$$$$$$$$$$$$$$$$$$$\n");

    CreatRdiPthread();
    Dbg_Level_Print(LOG_INFO, "1$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
        
	CreatRssiSwitchPthread();
    Dbg_Level_Print(LOG_INFO, "2$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");

    Create_DjiModule_Pthread();
    Dbg_Level_Print(LOG_INFO, "3$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
       
    CreateGatherGpsPthread();
    Dbg_Level_Print(LOG_INFO, "4$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
    
    Dbg_Level_Print(LOG_INFO, "5$$$$$$$$$$$$$$$$$$$$$:RequireBrd:%s, ActualBrd:%s\n",OutConfInfo.brd_code, pActualBrd_1);
	  if (0 == strncmp(OutConfInfo.brd_code, pActualBrd_1, sizeof(pActualBrd_1)))
	  {
        CreatePowerControl();
        Dbg_Level_Print(LOG_INFO, "5$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
	  }
    
    Create_ClinetAcception();
    Dbg_Level_Print(LOG_INFO, "6$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");

    Create_JasonDataSend();	
    Dbg_Level_Print(LOG_INFO, "7$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
	
    Create_NavigationDeception();
	  Dbg_Level_Print(LOG_INFO, "8$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");

    Rhhk_UpdateTokenTask();
	  Dbg_Level_Print(LOG_INFO, "9$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
     
	while (1)
	{
   	get_gps(&gps);
         //printf("========3>>>GPS: %f/%f\n", gps.dLongitude, gps.dLatitude);
         //printf("========3>>>state=%d\n", get_device_state());

#ifdef _WIN32
	     Sleep(100);
#else
	     usleep(100);
#endif

	}
	return 0;
}
#endif
